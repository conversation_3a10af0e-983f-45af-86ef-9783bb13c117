<?php

namespace Lokalise\Command;

use Lokalise\Services\WebHookService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class AddBlacklistedIp extends Command
{
    protected static $defaultName = 'lokalise:blacklist:add';

    private $service;

    public function __construct(WebHookService $service)
    {
        parent::__construct();
        $this->service = $service;
    }

    protected function configure()
    {
        $this->setDescription('Add IP to blacklisted address cache, stored in redis.');
        $this->addOption('host', null, InputOption::VALUE_NONE, 'Convert provided host to IP');
        $this->addArgument('address', InputArgument::REQUIRED, 'Address to ban');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $convertHost = $input->getOption('host');
        $address = $input->getArgument('address');

        if ($convertHost) {
            $addedIp = $this->service->addBlacklistedAddress($address);
            $output->writeln("added <info>{$address}</info> ({$addedIp})");
        } else {
            $this->service->addBlacklistedIp($address);
            $output->writeln("added <info>{$address}</info>");
        }

        $output->writeln('<comment>Done.</comment>');

        return 0;
    }
}
