<?php

declare(strict_types=1);

namespace Lokalise\Command;

use Lokalise\Adapter\RedisAdapter;
use Lokalise\Common\Helper\ProgressBarHelper;
use Lokalise\Factory\ProjectDataFactory;
use Lokalise\QAChecks\Check\UnbalancedBracketsQACheck;
use Lokalise\QAChecks\Service\QACheckManager;
use Psr\Log\LoggerInterface;
use RedisService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Command\LockableTrait;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Throwable;
use TranslationService;

class ApplyUnbalancedBracketsQaCheckCommand extends Command
{
    use LockableTrait;

    private const BATCH_LIMIT = 500;

    protected static $defaultName = 'lokalise:apply-unbalanced-brackets-qa-check';
    private TranslationService $translationService;
    private OutputInterface $output;
    private SymfonyStyle $io;
    private ProgressBar $progressBar;
    private RedisAdapter $redisAdapter;
    private QACheckManager $qaCheckManager;
    private ?int $minId;
    private ?int $maxId;
    private int $lastId;
    private InputInterface $input;
    private LoggerInterface $logger;
    private ProjectDataFactory $projectDataFactory;

    public function __construct(
        TranslationService $translationService,
        RedisService $redisService,
        QACheckManager $qaCheckManager,
        LoggerInterface $logger,
        ProjectDataFactory $projectDataFactory,
        $name = null
    ) {
        parent::__construct($name);
        $this->translationService = $translationService;
        $this->redisAdapter = $redisService->getClient();
        $this->qaCheckManager = $qaCheckManager;
        $this->logger = $logger;
        $this->projectDataFactory = $projectDataFactory;
    }

    protected function configure(): void
    {
        $this
            ->addArgument('min_id', InputArgument::OPTIONAL, 'ID of the translation to start with')
            ->addArgument('max_id', InputArgument::OPTIONAL, 'ID of the translation to end with')
            ->addOption('reset-last-id', null, InputOption::VALUE_OPTIONAL, 'Forces the command to reset last_id. Example: --reset-last-id=1');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->input = $input;
        $this->output = $output;
        $this->io = new SymfonyStyle($input, $this->output);
        $this->minId = $input->getArgument('min_id') ? (int) $input->getArgument('min_id') : null;
        $this->maxId = $input->getArgument('max_id') ? (int) $input->getArgument('max_id') : null;

        $lockKey = $this->getLockKey();
        if (!$this->lock($lockKey)) {
            return 0;
        }

        $this->lastId = $this->getLastId();

        try {
            $this->beginProgress();
            do {
                $translations = $this->translationService->findTranslationsPaginated($this->lastId, self::BATCH_LIMIT, $this->minId, $this->maxId);

                $currentLoopCount = 0;
                $translationsByProjectId = [];
                foreach ($translations as $translation) {
                    $this->lastId = (int) $translation['id'];
                    $currentLoopCount++;
                    $this->advanceProgress();

                    if ($this->translationNeedsToBeChecked($translation['translation'] ?? '')) {
                        $translationsByProjectId[$translation['project_id']][$translation['id']] = true;
                    }
                }
                $this->redisAdapter->set($this->getLastIdCacheKey(), $this->lastId, ['ex' => 60 * 60 * 36]);

                foreach ($translationsByProjectId as $projectId => $translationsToCheck) {
                    $this->qaCheckManager->setProjectData($this->projectDataFactory->createFromUnknownId($projectId));
                    $this->qaCheckManager->testBulk($translationsToCheck);
                }

                $this->heartbeatProgress();
            } while ($currentLoopCount >= self::BATCH_LIMIT);
            $this->finishProgress();
        } catch (Throwable $e) {
            $this->logger->error($e->getMessage(), ['exception' => $e]);

            return 1;
        }

        return 0;
    }

    private function translationNeedsToBeChecked($translation): bool
    {
        return preg_match(UnbalancedBracketsQACheck::PATTERN, $translation) === 1;
    }

    private function beginProgress(): void
    {
        if (!$this->io->isVerbose()) {
            return;
        }

        $this->logger->info('`' . $this->getLockKey() . '` Started working.');
        $this->io->writeln('Traversing over translations...');
        $totalTranslationsCount = $this->translationService->getTotalCount($this->lastId, $this->minId, $this->maxId);
        $this->progressBar = $this->io->createProgressBar();
        $this->progressBar->start($totalTranslationsCount);
    }

    private function advanceProgress(): void
    {
        if (!$this->progressBar || !$this->io->isVerbose()) {
            return;
        }

        $this->progressBar->advance();
    }

    private function heartbeatProgress(): void
    {
        if (!$this->progressBar || !$this->io->isVerbose()) {
            return;
        }

        $progressMessage = ProgressBarHelper::getMessageByProgressbar($this->progressBar, $this->output);
        $this->logger->info('`' . $this->getLockKey() . '` ' . $progressMessage);
    }

    private function finishProgress(): void
    {
        if (!$this->progressBar || !$this->io->isVerbose()) {
            return;
        }

        $this->progressBar->finish();
        $progressMessage = ProgressBarHelper::getMessageByProgressbar($this->progressBar, $this->output);
        $this->logger->info('`' . $this->getLockKey() . '`' . $progressMessage);
        $this->io->success('Finished successfully');
        $this->logger->info('`' . $this->getLockKey() . '` Finished working.');
    }

    private function getLockKey(): string
    {
        return implode('|', $this->input->getArguments());
    }

    protected function getLastIdCacheKey(): string
    {
        return $this->getLockKey() . '|lastId';
    }

    protected function getLastId(): int
    {
        $resetLastId = $this->input->getOption('reset-last-id');
        if ($resetLastId) {
            $this->lastId = 0;
        } else {
            $this->lastId = (int) $this->redisAdapter->get($this->getLastIdCacheKey());
        }

        return $this->lastId;
    }
}
