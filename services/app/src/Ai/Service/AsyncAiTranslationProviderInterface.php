<?php

declare(strict_types=1);

namespace Lokalise\Ai\Service;

use Lokalise\Ai\Exception\TranslationRequestException;
use Lokalise\Ai\Request\BaseRequestContextMetadata;
use Lokalise\AutomaticTranslation\Client\Polyglot\Request\AiIntegrationKind;
use Lokalise\AutomaticTranslation\Client\Polyglot\Request\MtIntegrationKind;
use Lokalise\Models\Locale;

interface AsyncAiTranslationProviderInterface
{
    /**
     * @param TranslationItem[] $translationItems
     * @param string|null $requestDescription Any helpful description that applies to the whole request. E.g. project description
     *
     * @throws TranslationRequestException
     *
     * @return string Returns requestId
     */
    public function translateAsync(
        string $correlationId,
        Locale $sourceLocale,
        Locale $targetLocale,
        BaseRequestContextMetadata $requestContextMetadata,
        ?GlossaryProviderInterface $glossaryProvider,
        ?StyleguideProviderInterface $styleguideProvider,
        ?string $requestDescription,
        array $translationItems,
        MtIntegrationKind|AiIntegrationKind|null $integration
    ): string;
}
