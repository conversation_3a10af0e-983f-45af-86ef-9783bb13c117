<?php

declare(strict_types=1);

namespace Lokalise\AiBilling\Services;

use InvalidArgumentException;
use Lokalise\Ai\Enum\AutomaticTranslationSource;
use Lokalise\AiBilling\Exception\TranslationObjectNotFoundException;
use Lokalise\Client\TeamPlanEntitlements\TeamPlanLimitClient;
use Lokalise\Common\Team\ValueObject\TeamId;
use Lokalise\Common\TeamPlanEntitlements\Dto\Request\TeamPlanLimitRequest;
use Lokalise\Common\TeamPlanEntitlements\Enum\TeamPlanEntitlementsEnum;
use Lokalise\Constants\Plan;
use Lokalise\Constants\ProjectTaskType;
use Lokalise\Models\ProjectData\ProjectData;
use Lokalise\Repository\TranslationRepository;
use MongoDB\BSON\UTCDateTime;
use MongoDB\Collection;
use TranslationService;

class TeamAiUsageService
{
    public const MONGODB_COLLECTION_NAME = 'ai_usage';
    public const FLUENTD_TAG = 'mongo.ai_usage';

    public const AI_FREE_WORDS_TO_PLAN_MAP = [
        2000 => [
            Plan::V2_SEAT_BASED_START_ANNUAL,
            Plan::V2_SEAT_BASED_START_MONTHLY,
            Plan::TRIAL,
        ],
        5000 => [
            Plan::V2_SEAT_BASED_ESSENTIAL_MONTHLY,
            Plan::V2_SEAT_BASED_ESSENTIAL_ANNUAL,
        ],
        1500 => [
            Plan::V2_SEAT_BASED_PRO_MONTHLY,
            Plan::V2_SEAT_BASED_PRO_ANNUAL,
            Plan::V2_SEAT_BASED_PRO_2_YEARS_MONTHLY,
            Plan::V3_SEAT_BASED_PRO_MONTHLY,
            Plan::V3_SEAT_BASED_PRO_ANNUAL,
            Plan::V3_SEAT_BASED_PRO_6_MONTH_MONTHLY,
            Plan::V3_SEAT_BASED_PRO_2_YEARS_MONTHLY,
            Plan::V1_SEAT_BASED_PRO_ANNUAL_BI_ANNUAL_PAYMENTS,
            Plan::V1_SEAT_BASED_PRO_ANNUAL_QUARTERLY_PAYMENTS,
            Plan::V1_SEAT_BASED_PRO_ANNUAL_MONTHLY_PAYMENTS,
        ],
        3000 => [
            Plan::V2_SEAT_BASED_ENTERPRISE_ANNUAL,
            Plan::V2_SEAT_BASED_ENTERPRISE_6_MONTHS_MONTHLY,
            Plan::V2_SEAT_BASED_ENTERPRISE_2_YEARS_MONTHLY,
            Plan::V2_SEAT_BASED_ENTERPRISE_ANNUAL_BI_ANNUAL_PAYMENTS,
            Plan::V2_SEAT_BASED_ENTERPRISE_ANNUAL_QUARTERLY_PAYMENTS,
            Plan::V3_SEAT_BASED_ENTERPRISE_6_MONTHS_MONTHLY,
            Plan::V3_SEAT_BASED_ENTERPRISE_2_YEARS_MONTHLY,
            Plan::V3_SEAT_BASED_ENTERPRISE_ANNUAL,
            Plan::V3_SEAT_BASED_ENTERPRISE_ANNUAL_BI_ANNUAL_PAYMENTS,
            Plan::V3_SEAT_BASED_ENTERPRISE_ANNUAL_QUARTERLY_PAYMENTS,
        ],
    ];

    // Threshold in percent
    public const QUOTA_WARNING_THRESHOLDS = [
        80,
        90,
        95,
    ];
    public const QUOTA_REACHED_THRESHOLD = 100; // Threshold in percent

    public function __construct(
        private readonly ModuleProxyService $moduleProxyService,
        private readonly TranslationService $translationService,
        private readonly AiUsageRedisService $aiUsageRedisService,
        private readonly Collection $aiUsageCollection,
        private readonly TranslationRepository $translationRepository,
        private readonly TeamPlanLimitClient $teamPlanLimitClient,
    ) {
    }

    public function getLimitThresholdValue(int $limit, int $threshold = self::QUOTA_WARNING_THRESHOLDS[0]): float
    {
        return $limit / 100 * $threshold;
    }

    public function getLimitThresholdStep(int $usage, int $limit): ?int
    {
        if ($usage >= $limit) {
            return self::QUOTA_REACHED_THRESHOLD;
        }

        foreach (array_reverse(self::QUOTA_WARNING_THRESHOLDS) as $threshold) {
            if ($usage >= $this->getLimitThresholdValue($limit, $threshold)) {
                return $threshold;
            }
        }

        //No threshold has been reached
        return null;
    }

    public function getTeamAvailableAiUnit(int $teamId): int
    {
        $team = $this->moduleProxyService->getTeamById($teamId);

        if (null === $team) {
            throw new InvalidArgumentException(sprintf('Team with ID %d not found', $teamId));
        }

        return (int) $this->teamPlanLimitClient->getTeamPlanLimit(
            new TeamPlanLimitRequest(
                teamId: new TeamId($teamId),
                limit: TeamPlanEntitlementsEnum::AI_USAGE_WORDS_LIMIT
            )
        );
    }

    public function getTeamSpentUnit(int $teamId): int
    {
        return $this->aiUsageRedisService->getTeamSpentUnit($teamId);
    }

    public function logAiUsageData(
        int $teamId,
        ProjectData $projectData,
        int $taskId,
        string $taskType,
        int $wordsUsage,
        array $translation,
        array $sourceTranslation,
        AutomaticTranslationSource $translationSource,
    ): void {
        $allowedTranslationKeys = [
            'id',
            'akey_id',
            'lang_id',
            'modified_at',
            'translation',
            'words',
            'master_reference_id',
            'project_branch_id',
            'segment_number',
        ];

        $aiUsageData = [
            'teamId' => $teamId,
            'usageWords' => $translationSource === AutomaticTranslationSource::SOURCE_TM ? 0 : $wordsUsage,
            'taskId' => $taskId,
            'taskType' => $taskType,
            'translation' => array_intersect_key($translation, array_flip($allowedTranslationKeys)),
            'project' => [
                'insertId' => $projectData->getMasterProjectInsertId(),
                'name' => $projectData->getProject()->name,
            ],
            'sourceTranslation' => array_intersect_key($sourceTranslation, array_flip($allowedTranslationKeys)),
            'translationSource' => $translationSource->value,
            'time' => new UTCDateTime(),
        ];

        $this->aiUsageCollection->insertOne($aiUsageData);
    }

    public function logAiUsageDataBulk(
        int $teamId,
        ProjectData $projectData,
        int $taskId,
        string $taskType,
        array $translations,
        array $sourceTranslations,
        AutomaticTranslationSource $translationSource,
    ): void {
        $allowedTranslationKeys = [
            'id',
            'akey_id',
            'lang_id',
            'modified_at',
            'translation',
            'words',
            'master_reference_id',
            'project_branch_id',
            'segment_number',
        ];

        $translationsByKeySegment = [];
        foreach ($translations as $translation) {
            $translationsByKeySegment[sprintf('%s:%s', $translation['akey_id'], $translation['segment_number'])] = $translation;
        }

        $sourceTranslationsByKeySegment = [];
        foreach ($sourceTranslations as $sourceTranslation) {
            $sourceTranslationsByKeySegment[sprintf('%s:%s', $sourceTranslation['akey_id'], $sourceTranslation['segment_number'])] = $sourceTranslation;
        }

        $aiUsageData = [];
        foreach ($translationsByKeySegment as $keySegment => $translation) {
            $sourceTranslation = $sourceTranslationsByKeySegment[$keySegment];

            $wordsUsage = $translationSource === AutomaticTranslationSource::SOURCE_TM ? 0 : $this->calculateUsageUnits(
                (int) $sourceTranslation['words'],
                (int) $translation['words'],
                $taskType
            );

            $aiUsageData[] = [
                'teamId' => $teamId,
                'usageWords' => $wordsUsage,
                'taskId' => $taskId,
                'taskType' => $taskType,
                'translation' => array_intersect_key($translation, array_flip($allowedTranslationKeys)),
                'project' => [
                    'insertId' => $projectData->getMasterProjectInsertId(),
                    'name' => $projectData->getProject()->name,
                ],
                'sourceTranslation' => array_intersect_key($sourceTranslation, array_flip($allowedTranslationKeys)),
                'translationSource' => $translationSource->value,
                'time' => new UTCDateTime(),
            ];
        }

        $this->aiUsageCollection->insertMany($aiUsageData);
    }

    public function unlockRedisUsage(int $teamId, int $usage, int $taskId): void
    {
        $this->aiUsageRedisService->unlockRedisUsage($teamId, $usage, $taskId);
    }

    public function clearTaskUsage(int $teamId, int $taskId): void
    {
        $this->aiUsageRedisService->clearTaskUsage($teamId, $taskId);
    }

    public function logAiUsageForTaskItem(
        int $teamId,
        ProjectData $projectData,
        int $taskId,
        int $translationId,
        int $keyId,
        int $originalLangId,
        string $taskType,
        AutomaticTranslationSource $translationSource,
    ): void {
        $translation = $this->translationService->getById($translationId);
        $originalTranslation = $this->translationService->findTranslationByKeyIdAndLangId($keyId, $originalLangId);

        if (null === $translation || null === $originalTranslation) {
            throw new TranslationObjectNotFoundException();
        }

        $calculatedUsage = $this->calculateUsageUnits(
            (int) $originalTranslation['words'],
            (int) $translation['words'],
            $taskType
        );

        $this->unlockRedisUsage($teamId, $calculatedUsage, $taskId);

        // no need to track usage for TM translations
        if ($translationSource !== AutomaticTranslationSource::SOURCE_TM) {
            $this->updateTeamUsage($teamId, $calculatedUsage);
        }

        $this->logAiUsageData(
            teamId: $teamId,
            projectData: $projectData,
            taskId: $taskId,
            taskType: $taskType,
            wordsUsage: $calculatedUsage,
            translation: $translation,
            sourceTranslation: $originalTranslation,
            translationSource: $translationSource,
        );
    }

    /**
     * @param int[] $translationIds
     * @param array{akey_id: int, segment_number:int}[] $keySegments
     */
    public function logAiUsageForTaskItems(
        int $teamId,
        ProjectData $projectData,
        int $taskId,
        array $translationIds,
        array $keySegments,
        int $originalLangId,
        string $taskType,
        AutomaticTranslationSource $translationSource,
    ): void {
        $translations = $this->translationService->getByIds($translationIds);
        $originalTranslations = $this->translationRepository->findTranslationsByKeySegmentsAndLangId(
            $keySegments,
            $originalLangId
        );

        $originalTranslationsWordsCount = array_sum(array_column($originalTranslations, 'words'));
        $translationsWordsCount = array_sum(array_column($translations, 'words'));

        $calculatedUsage = $this->calculateUsageUnits(
            $originalTranslationsWordsCount,
            $translationsWordsCount,
            $taskType
        );

        $this->unlockRedisUsage($teamId, $calculatedUsage, $taskId);

        // no need to track usage for TM translations
        if ($translationSource !== AutomaticTranslationSource::SOURCE_TM) {
            $this->updateTeamUsage($teamId, $calculatedUsage);
        }

        $this->logAiUsageDataBulk(
            teamId: $teamId,
            projectData: $projectData,
            taskId: $taskId,
            taskType: $taskType,
            translations: $translations,
            sourceTranslations: $originalTranslations,
            translationSource: $translationSource,
        );
    }

    /**
     * Used to unlock usage for task items that were not processed by AI
     */
    public function unlockTaskItem(
        int $teamId,
        int $taskId,
        int $translationId,
        int $keyId,
        int $originalLangId,
        string $taskType
    ): void {
        $translation = $this->translationService->getById($translationId);
        $originalTranslation = $this->translationService->findTranslationByKeyIdAndLangId($keyId, $originalLangId);

        if (null === $translation || null === $originalTranslation) {
            throw new TranslationObjectNotFoundException();
        }

        $calculatedUsage = $this->calculateUsageUnits(
            (int) $originalTranslation['words'],
            (int) $translation['words'],
            $taskType
        );

        $this->unlockRedisUsage($teamId, $calculatedUsage, $taskId);
    }

    public function lockRedisUsage(int $teamId, int $usage, int $taskId): void
    {
        $this->aiUsageRedisService->lockRedisUsage($teamId, $usage, $taskId);
    }

    public function calculateUsageUnits(
        int $sourceWordsCount,
        int $targetWordsCount,
        string $taskType
    ): int {
        return $this->calculateUsageInWords($sourceWordsCount, $targetWordsCount, $taskType);
    }

    private function calculateUsageInWords(int $sourceWordsCount, int $targetWordsCount, string $taskType): int
    {
        if ($taskType === ProjectTaskType::LqaByAi) {
            return max($sourceWordsCount, $targetWordsCount);
        } elseif ($taskType === ProjectTaskType::AutomaticTranslationByAi) {
            return $sourceWordsCount;
        }

        throw new InvalidArgumentException(sprintf('Unknown task type: %s', $taskType));
    }

    public function updateTeamUsage(int $teamId, int $wordsUsage): void
    {
        $this->moduleProxyService->updateTeamUsage($teamId, $wordsUsage);
    }

    /*
     * Atomically check whether we have enough words, and if yes, then update the limit
     */
    public function checkLimitAvailableAndUpdate(int $teamId, int $numberOfUnits): bool
    {
        $success = false;
        $this->aiUsageRedisService->performAtomicTeamCalculations(
            $teamId,
            function () use ($teamId, $numberOfUnits, &$success): void {
                if ($this->checkIfTeamWillHaveOverlimitMonthlyAiWordsUsage($teamId, $numberOfUnits)) {
                    return;
                }

                $this->updateTeamUsage($teamId, $numberOfUnits);

                $success = true;
            }
        );

        return $success;
    }

    public function overrideTeamAiUsage(int $teamId, int $wordsUsage): void
    {
        $this->moduleProxyService->overrideTeamAiUsage($teamId, $wordsUsage);
    }

    public function checkIfTeamWillHaveOverlimitMonthlyAiWordsUsage(int $teamId, int $numberOfUnits): bool
    {
        $currentValue = $this->getTeamSpentUnit($teamId);

        return $currentValue + $numberOfUnits > $this->getTeamAvailableAiUnit($teamId);
    }

    public function checkIfTeamHasWordsOverLimit(int $teamId): bool
    {
        return $this->getTeamSpentUnit($teamId) > $this->getTeamAvailableAiUnit($teamId);
    }

    public function setTeamAiUsageLimitNotifiedLock(int $teamId, int $limitThresholdStep): bool
    {
        return $this->aiUsageRedisService->setTeamAiUsageLimitNotifiedLock($teamId, $limitThresholdStep);
    }

    public function removeTeamAiUsageLimitNotifiedLocks(int $teamId): void
    {
        $this->aiUsageRedisService->removeTeamAiUsageLimitNotifiedLocks($teamId);
    }
}
