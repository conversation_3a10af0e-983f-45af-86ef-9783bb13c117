<?php

declare(strict_types=1);

namespace Lokalise\TeamPlanEntitlements\Services;

use Lokalise\AiBilling\Services\TeamAiUsageService;
use Lokalise\Common\Team\ValueObject\TeamId;
use Lokalise\Common\TeamPlanEntitlements\Enum\TeamPlanEntitlementsEnum;
use Lokalise\Entity\Plan;
use Lokalise\Plan\Model\PlanId;
use Lokalise\Services\PlanService;
use Lokalise\TeamManagement\Team\Entity\TeamEntity;
use Lokalise\TeamManagement\Team\Repository\TeamEntityRepository;

readonly class TeamPlanEntitlementsService
{
    public const int INFINITY_LIMIT_VALUE = 999999999;

    public function __construct(
        private PlanService $planService,
        private TeamEntityRepository $teamRepository,
    ) {
    }

    public function getTeamPlanEntitlements(int $teamId): array
    {
        $team = $this->teamRepository->find($teamId);

        if ($team === null || $team->getPlan() === null) {
            return [];
        }

        $plan = $this->planService->findByPlanId(new PlanId($team->getPlan()));

        if ($plan === null) {
            return [];
        }

        return $this->buildTeamPlanEntitlements($team, $plan);
    }

    /**
     * @param int[] $teamIds
     */
    public function getTeamsPlanEntitlements(array $teamIds): array
    {
        $data = [];
        $teams = $this->teamRepository->findBy(['id' => $teamIds]);

        foreach ($teams as $team) {
            if ($team === null || $team->getPlan() === null) {
                $data['teamId'] = [];

                continue;
            }

            $plan = $this->planService->findByPlanId(new PlanId($team->getPlan()));

            if ($plan === null) {
                $data['teamId'] = [];

                continue;
            }

            $data[$team->getId()] = $this->buildTeamPlanEntitlements($team, $plan);
        }

        return $data;
    }

    public function updateTeamPlanLimits(int $teamId, array $limits): void
    {
        $team = $this->teamRepository->find($teamId);

        if (null === $team) {
            return;
        }

        $this->teamRepository->updateCustomPlan($team, $limits);
    }

    public function updateTeamPlanFeaturesState(int $teamId, array $features): void
    {
        $team = $this->teamRepository->find($teamId);

        if (null === $team) {
            return;
        }

        $this->teamRepository->updateCustomPlan($team, array_merge($team->getCustomPlan() ?? [], ['features' => $features]));
    }

    public function getTeamCustomPlan(int $teamId): array
    {
        return $this->teamRepository->find($teamId)?->getCustomPlan() ?? [];
    }

    /**
     * @param TeamId[] $teamIds
     */
    public function getTeamsCustomPlan(array $teamIds): array
    {
        $data = [];
        $teams = $this->teamRepository->findBy(['id' => array_map(static fn (TeamId $teamId) => $teamId->getValue(), $teamIds)]);

        foreach ($teams as $team) {
            $data[$team->getId()] = $team->getCustomPlan();
        }

        return $data;
    }

    private function buildTeamPlanEntitlements(TeamEntity $teamEntity, Plan $planEntity): array
    {
        $customPlanFeatures = $teamEntity->getCustomPlan()['features'] ?? [];

        $teamFeatures = array_map('intval', array_unique(array_merge($planEntity->getFeatures(), array_keys($customPlanFeatures))));

        if (true === $planEntity->isSeatBased()) {
            $contributorsLimit = $teamEntity->getPlanSeatLimit() + (int) $teamEntity->getPlanFreeSeatCount();
        } elseif (null !== $teamEntity->getCustomPlan() && isset($teamEntity->getCustomPlan()[TeamPlanEntitlementsEnum::CONTRIBUTOR_LIMIT->value])) {
            $contributorsLimit = (int) $teamEntity->getCustomPlan()[TeamPlanEntitlementsEnum::CONTRIBUTOR_LIMIT->value];
        } else {
            $contributorsLimit = $planEntity->getContributorLimit();
        }

        $aiWordsAllowance = $this->calculateAiWordsAllowance($teamEntity, $planEntity);

        if (true === $planEntity->isV2Generation()) {
            // For generation V2 plans, we need to set the limits to null (unlimited)
            return [
                TeamPlanEntitlementsEnum::KEY_LIMIT->value => self::INFINITY_LIMIT_VALUE,
                TeamPlanEntitlementsEnum::PROJECT_LIMIT->value => self::INFINITY_LIMIT_VALUE,
                TeamPlanEntitlementsEnum::CONTRIBUTOR_LIMIT->value => self::INFINITY_LIMIT_VALUE,
                TeamPlanEntitlementsEnum::GLOSSARY_LIMIT->value => self::INFINITY_LIMIT_VALUE,
                TeamPlanEntitlementsEnum::MAU_LIMIT->value => self::INFINITY_LIMIT_VALUE,
                TeamPlanEntitlementsEnum::AUTOMATION_LIMIT->value => self::INFINITY_LIMIT_VALUE,
                TeamPlanEntitlementsEnum::AUTOMATION_LIMIT_MT_CHARS->value => self::INFINITY_LIMIT_VALUE,
                TeamPlanEntitlementsEnum::MAX_NUMBER_OF_PROJECT_LEVEL_WEBHOOK_HANDLERS->value => self::INFINITY_LIMIT_VALUE,
                TeamPlanEntitlementsEnum::MAX_NUMBER_OF_TEAM_WEBHOOK_LEVEL_HANDLERS->value => self::INFINITY_LIMIT_VALUE,
                TeamPlanEntitlementsEnum::OTA_TRAFFIC_LIMIT->value => self::INFINITY_LIMIT_VALUE,
                TeamPlanEntitlementsEnum::FEATURES->value => $teamFeatures,
                // AI_USAGE_WORDS_LIMIT still here for compatibility reasons, later on we switch to PRO_AI_TRANSLATIONS_LIMIT
                TeamPlanEntitlementsEnum::AI_USAGE_WORDS_LIMIT->value => $aiWordsAllowance,
                TeamPlanEntitlementsEnum::WORKFLOWS_NUMBER_LIMIT->value => self::INFINITY_LIMIT_VALUE,
                TeamPlanEntitlementsEnum::WORKFLOWS_TEMPLATE_GATE_LEVEL->value => self::INFINITY_LIMIT_VALUE,
                TeamPlanEntitlementsEnum::PROCESSED_WORDS_LIMIT->value => $teamEntity->getCustomPlan()[TeamPlanEntitlementsEnum::PROCESSED_WORDS_LIMIT->value] ?? $planEntity->getProcessedWordsLimit(),
                TeamPlanEntitlementsEnum::HOSTED_WORDS_LIMIT->value => self::INFINITY_LIMIT_VALUE,
                TeamPlanEntitlementsEnum::ADVANCED_SEATS_LIMIT->value => self::INFINITY_LIMIT_VALUE,
                TeamPlanEntitlementsEnum::PRO_AI_TRANSLATIONS_LIMIT->value => $aiWordsAllowance,
                TeamPlanEntitlementsEnum::LANGUAGES_LIMIT->value => self::INFINITY_LIMIT_VALUE,
                TeamPlanEntitlementsEnum::API_CALLS_RATE_LIMIT->value => self::INFINITY_LIMIT_VALUE,
                TeamPlanEntitlementsEnum::INTEGRATIONS_LIMIT->value => self::INFINITY_LIMIT_VALUE,
                TeamPlanEntitlementsEnum::AUTOMATION_V2_LIMIT->value => self::INFINITY_LIMIT_VALUE,
            ];
        }

        return [
            TeamPlanEntitlementsEnum::KEY_LIMIT->value => $teamEntity->getCustomPlan()[TeamPlanEntitlementsEnum::KEY_LIMIT->value] ?? $planEntity->getKeyLimit(),
            TeamPlanEntitlementsEnum::PROJECT_LIMIT->value => $teamEntity->getCustomPlan()[TeamPlanEntitlementsEnum::PROJECT_LIMIT->value] ?? $planEntity->getProjectLimit(),
            TeamPlanEntitlementsEnum::CONTRIBUTOR_LIMIT->value => $contributorsLimit,
            TeamPlanEntitlementsEnum::GLOSSARY_LIMIT->value => $teamEntity->getCustomPlan()[TeamPlanEntitlementsEnum::GLOSSARY_LIMIT->value] ?? $planEntity->getGlossaryLimit(),
            TeamPlanEntitlementsEnum::MAU_LIMIT->value => $teamEntity->getCustomPlan()[TeamPlanEntitlementsEnum::MAU_LIMIT->value] ?? $planEntity->getMauLimit(),
            TeamPlanEntitlementsEnum::AUTOMATION_LIMIT->value => $teamEntity->getCustomPlan()[TeamPlanEntitlementsEnum::AUTOMATION_LIMIT->value] ?? $planEntity->getAutomationLimit(),
            TeamPlanEntitlementsEnum::AUTOMATION_LIMIT_MT_CHARS->value => $teamEntity->getCustomPlan()[TeamPlanEntitlementsEnum::AUTOMATION_LIMIT_MT_CHARS->value] ?? $planEntity->getAutomationLimitMtChars(),
            TeamPlanEntitlementsEnum::MAX_NUMBER_OF_PROJECT_LEVEL_WEBHOOK_HANDLERS->value => $teamEntity->getCustomPlan()[TeamPlanEntitlementsEnum::MAX_NUMBER_OF_PROJECT_LEVEL_WEBHOOK_HANDLERS->value] ?? $planEntity->getMaxNumberOfProjectLevelWebhookHandlers(),
            TeamPlanEntitlementsEnum::MAX_NUMBER_OF_TEAM_WEBHOOK_LEVEL_HANDLERS->value => $teamEntity->getCustomPlan()[TeamPlanEntitlementsEnum::MAX_NUMBER_OF_TEAM_WEBHOOK_LEVEL_HANDLERS->value] ?? $planEntity->getMaxNumberOfTeamWebhookLevelHandlers(),
            TeamPlanEntitlementsEnum::OTA_TRAFFIC_LIMIT->value => $teamEntity->getCustomPlan()[TeamPlanEntitlementsEnum::OTA_TRAFFIC_LIMIT->value] ?? $planEntity->getOtaTrafficLimit(),
            TeamPlanEntitlementsEnum::FEATURES->value => $teamFeatures,
            TeamPlanEntitlementsEnum::AI_USAGE_WORDS_LIMIT->value => $aiWordsAllowance,
            TeamPlanEntitlementsEnum::WORKFLOWS_NUMBER_LIMIT->value => $teamEntity->getCustomPlan()[TeamPlanEntitlementsEnum::WORKFLOWS_NUMBER_LIMIT->value] ?? $planEntity->getWorkflowsNumberLimit(),
            TeamPlanEntitlementsEnum::WORKFLOWS_TEMPLATE_GATE_LEVEL->value => $teamEntity->getCustomPlan()[TeamPlanEntitlementsEnum::WORKFLOWS_TEMPLATE_GATE_LEVEL->value] ?? $planEntity->getWorkflowsTemplateGateLevel(),
            TeamPlanEntitlementsEnum::PROCESSED_WORDS_LIMIT->value => 0,
            TeamPlanEntitlementsEnum::HOSTED_WORDS_LIMIT->value => 0,
            TeamPlanEntitlementsEnum::ADVANCED_SEATS_LIMIT->value => 0,
            TeamPlanEntitlementsEnum::PRO_AI_TRANSLATIONS_LIMIT->value => 0,
            TeamPlanEntitlementsEnum::LANGUAGES_LIMIT->value => 0,
            TeamPlanEntitlementsEnum::API_CALLS_RATE_LIMIT->value => 0,
            TeamPlanEntitlementsEnum::INTEGRATIONS_LIMIT->value => 0,
            TeamPlanEntitlementsEnum::AUTOMATION_V2_LIMIT->value => 0,
        ];
    }

    private function calculateAiWordsAllowance(TeamEntity $teamEntity, Plan $plan): int
    {
        if (true === $teamEntity->hasV2GenerationPlan()) {
            return !empty($teamEntity->getCustomPlan()[TeamPlanEntitlementsEnum::PRO_AI_TRANSLATIONS_LIMIT->value]) ?
                $teamEntity->getCustomPlan()[TeamPlanEntitlementsEnum::PRO_AI_TRANSLATIONS_LIMIT->value] :
                $plan->getProAiTranslationsLimit();
        }

        $freeAiWords = 0;

        if (false === $teamEntity->isFreeAiWordsUsed()) {
            // it's relevant only for the old pricing plans
            foreach (TeamAiUsageService::AI_FREE_WORDS_TO_PLAN_MAP as $words => $plans) {
                if (in_array($teamEntity->getPlan(), $plans, true)) {
                    $freeAiWords = $words;

                    break;
                }
            }
        }

        $allowanceFromCustomPlan = !empty($teamEntity->getCustomPlan()[TeamPlanEntitlementsEnum::AI_USAGE_WORDS_LIMIT->value]) ?
            $teamEntity->getCustomPlan()[TeamPlanEntitlementsEnum::AI_USAGE_WORDS_LIMIT->value] :
            0;

        return $freeAiWords + (int) $allowanceFromCustomPlan;
    }
}
