<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\DBALException;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210421123231 extends AbstractMigration
{
    private array $chunkedRecordIds;

    public function getDescription(): string
    {
        return "Delete integration_item records that don't have related project or project_data_integration";
    }

    /**
     * @throws DBALException
     */
    public function preUp(Schema $schema): void
    {
        parent::preUp($schema);

        $sql1 = '
        SELECT
            integration_item.id
        FROM
            integration_item
            LEFT JOIN project ON integration_item.project_insert_id = project.insert_id
        WHERE
            project.id IS NULL
        ';

        $ids1 = $this->connection->executeQuery($sql1)->fetchAll();

        $sql2 = '
        SELECT integration_item.id
        FROM
            integration_item
            JOIN project ON integration_item.project_insert_id = project.insert_id
            LEFT JOIN project_data_integration
                      ON project_data_integration.project_id = project.id AND integration_item.integration = project_data_integration.integration
        WHERE
            project_data_integration.id IS NULL
        ';

        $ids2 = $this->connection->executeQuery($sql2)->fetchAll();

        $ids = array_merge($ids1, $ids2);

        $this->chunkedRecordIds = array_chunk(array_map(fn ($item) => (int) $item['id'], $ids), 100);
    }

    public function up(Schema $schema): void
    {
        $sqlDelete = 'DELETE FROM integration_item WHERE id IN (:ids)';

        foreach ($this->chunkedRecordIds as $ids) {
            $this->addSql($sqlDelete, ['ids' => $ids], ['ids' => Connection::PARAM_INT_ARRAY]);
        }
    }

    public function down(Schema $schema): void
    {
        // there is nothing we can do here
    }
}
