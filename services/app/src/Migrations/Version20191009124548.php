<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20191009124548 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Small changes related to branching implementation';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
			ALTER TABLE `project` 
			ADD COLUMN `is_branching_enabled` TINYINT(4) NOT NULL DEFAULT 0,
			ADD COLUMN `master_reference_id` INT UNSIGNED NULL DEFAULT NULL;
		');
        $this->addSql('
			ALTER TABLE `project_lang` 
			ADD COLUMN `master_reference_id` INT UNSIGNED NULL DEFAULT NULL,
			ADD COLUMN `project_branch_id` INT UNSIGNED NULL DEFAULT NULL,
			ADD COLUMN `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
			ADD COLUMN `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
		');
        $this->addSql('
			ALTER TABLE `akey` 
			ADD COLUMN `master_reference_id` INT UNSIGNED NULL DEFAULT NULL,
			ADD COLUMN `project_branch_id` INT UNSIGNED NULL DEFAULT NULL,
			ADD COLUMN `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
		');
        $this->addSql('
			ALTER TABLE `filename` 
			ADD COLUMN `master_reference_id` INT UNSIGNED NULL DEFAULT NULL,
			ADD COLUMN `project_branch_id` INT UNSIGNED NULL DEFAULT NULL,
			ADD COLUMN `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
			ADD COLUMN `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
		');
        $this->addSql('
			ALTER TABLE `akey_filename` 
			ADD COLUMN `master_reference_id` INT UNSIGNED NULL DEFAULT NULL,
			ADD COLUMN `project_branch_id` INT UNSIGNED NULL DEFAULT NULL,
			ADD COLUMN `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
			ADD COLUMN `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
		');
        $this->addSql('
			ALTER TABLE `translation` 
			ADD COLUMN `master_reference_id` INT UNSIGNED NULL DEFAULT NULL,
			ADD COLUMN `project_branch_id` INT UNSIGNED NULL DEFAULT NULL;
		');
        $this->addSql('
			ALTER TABLE `order` 
			ADD COLUMN `master_reference_id` INT UNSIGNED NULL DEFAULT NULL;
		');
        $this->addSql('
			ALTER TABLE `translation_custom_statuses`
			ADD COLUMN `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP;
		');
        $this->addSql('
			ALTER TABLE `translation_stats` 
			ADD COLUMN `project_branch_id` INT(10) UNSIGNED NULL DEFAULT NULL AFTER `project_insert_id`;
		');
        $this->addSql('
			ALTER TABLE `project` 
			ADD INDEX `master_reference_id` (`master_reference_id` ASC);
		');
        $this->addSql('
			ALTER TABLE `project_lang` 
			ADD INDEX `master_reference_id` (`master_reference_id` ASC);
		');
        $this->addSql('
			ALTER TABLE `project_lang` 
			ADD INDEX `project_branch_id` (`project_branch_id` ASC);
		');
        $this->addSql('
			ALTER TABLE `akey` 
			ADD INDEX `master_reference_id` (`master_reference_id` ASC);
		');
        $this->addSql('
			ALTER TABLE `akey` 
			ADD INDEX `project_branch_id` (`project_branch_id` ASC);
		');
        $this->addSql('
			ALTER TABLE `filename` 
			ADD INDEX `master_reference_id` (`master_reference_id` ASC);
		');
        $this->addSql('
			ALTER TABLE `filename` 
			ADD INDEX `project_branch_id` (`project_branch_id` ASC);
		');
        $this->addSql('
			ALTER TABLE `akey_filename` 
			ADD INDEX `master_reference_id` (`master_reference_id` ASC);
		');
        $this->addSql('
			ALTER TABLE `akey_filename` 
			ADD INDEX `project_branch_id` (`project_branch_id` ASC);
		');
        $this->addSql('
			ALTER TABLE `order` 
			ADD INDEX `master_reference_id` (`master_reference_id` ASC);
		');
        $this->addSql('
			ALTER TABLE `translation` 
			ADD INDEX `master_reference_id` (`master_reference_id` ASC);
		');
        $this->addSql('
			ALTER TABLE `translation` 
			ADD INDEX `project_branch_id` (`project_branch_id` ASC);
		');
        $this->addSql('
			ALTER TABLE `translation_stats` 
			ADD INDEX `project_branch_id` (`project_branch_id` ASC);
		');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
			ALTER TABLE `project` 
			DROP INDEX `master_reference_id`;
		');
        $this->addSql('
			ALTER TABLE `project_lang` 
			DROP INDEX `master_reference_id`;
		');
        $this->addSql('
			ALTER TABLE `project_lang` 
			DROP INDEX `project_branch_id`;
		');
        $this->addSql('
			ALTER TABLE `akey` 
			DROP INDEX `master_reference_id`;
		');
        $this->addSql('
			ALTER TABLE `akey` 
			DROP INDEX `project_branch_id`;
		');
        $this->addSql('
			ALTER TABLE `filename` 
			DROP INDEX `master_reference_id`;
		');
        $this->addSql('
			ALTER TABLE `filename` 
			DROP INDEX `project_branch_id`;
		');
        $this->addSql('
			ALTER TABLE `akey_filename` 
			DROP INDEX `master_reference_id`;
		');
        $this->addSql('
			ALTER TABLE `akey_filename` 
			DROP INDEX `project_branch_id`;
		');
        $this->addSql('
			ALTER TABLE `order` 
			DROP INDEX `master_reference_id`;
		');
        $this->addSql('
			ALTER TABLE `translation` 
			DROP INDEX `master_reference_id`;
		');
        $this->addSql('
			ALTER TABLE `translation` 
			DROP INDEX `project_branch_id`;
		');
        $this->addSql('
			ALTER TABLE `translation_stats` 
			DROP INDEX `project_branch_id`;
		');

        $this->addSql('
			ALTER TABLE `project` 
			DROP COLUMN `is_branching_enabled`,
			DROP COLUMN `master_reference_id`;
		');
        $this->addSql('
			ALTER TABLE `project_lang` 
			DROP COLUMN `master_reference_id`,
			DROP COLUMN `project_branch_id`,
			DROP COLUMN `created_at`,
			DROP COLUMN `updated_at`;
		');
        $this->addSql('
			ALTER TABLE `akey` 
			DROP COLUMN `master_reference_id`,
			DROP COLUMN `project_branch_id`,
			DROP COLUMN `updated_at`;
		');
        $this->addSql('
			ALTER TABLE `filename` 
			DROP COLUMN `master_reference_id`,
			DROP COLUMN `project_branch_id`,
			DROP COLUMN `created_at`,
			DROP COLUMN `updated_at`;
		');
        $this->addSql('
			ALTER TABLE `akey_filename` 
			DROP COLUMN `master_reference_id`,
			DROP COLUMN `project_branch_id`,
			DROP COLUMN `created_at`,
			DROP COLUMN `updated_at`;
		');
        $this->addSql('
			ALTER TABLE `translation` 
			DROP COLUMN `master_reference_id`,
			DROP COLUMN `project_branch_id`;
		');
        $this->addSql('
			ALTER TABLE `order` 
			DROP COLUMN `master_reference_id`;
		');
        $this->addSql('
			ALTER TABLE `translation_custom_statuses`
			DROP COLUMN `created_at`;
		');
        $this->addSql('
			ALTER TABLE `translation_stats` 
			DROP COLUMN `project_branch_id`;
		');
    }
}
