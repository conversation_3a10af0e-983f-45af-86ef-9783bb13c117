<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;
use Lokalise\Enums\Feature;
use Lokalise\Integration\Integration;
use Lokalise\Integration\IntegrationType;

final class Version20220913132742 extends AbstractMigration
{
    private const CATEGORY_NAME = 'Content management';

    private const RAW_APP_DEFINITION = [
        'title' => Integration::INTEGRATION_TITLES[Integration::TYPEFORM],
        'tagline' => 'Exchange data with Typeform.',
        'summary' => 'Exchange data with Typeform.',
        'description' => null,
        'export_description' => null,
        'url' => null,
        'logo' => '/img/integrations/typeform.png',
        'logo_darkmode' => '/img/integrations/typeform.png',
        'static_key' => Integration::TYPEFORM,
        'is_static' => 1,
        'is_internal_app' => 1,
        'docs_url' => 'http://docs.lokalise.com/en/articles/6544827-typeform',
        'privacy_policy_link' => 'https://lokalise.com/privacy-policy',
        'terms_of_service_link' => 'https://lokalise.com/terms',
        'feature' => Feature::INTEGRATION_TYPEFORM,
        'type' => IntegrationType::CONTENT_ENGINE,
        'tags' => [
            Integration::TAG_HIDDEN,
            Integration::TAG_EXCLUDE_FROM_DOC_PROJECT,
        ],
        'author' => 'Lokalise',
        'about' => null,
        'key_features' => null,
        'media_content' => null,
        'config' => null,
    ];

    public function getDescription(): string
    {
        return 'Create app definition for Typeform';
    }

    public function up(Schema $schema): void
    {
        $parameters = array_merge(self::RAW_APP_DEFINITION, ['category_name' => self::CATEGORY_NAME]);
        $parameters['tags'] = implode(',', $parameters['tags']);
        if (null !== $parameters['config']) {
            $parameters['config'] = json_encode($parameters['config']);
        }

        $this->addSql(
            <<<'SQL'
                INSERT INTO integration_definition
                    (title, tagline, url, logo, logo_darkmode, static_key, is_static, is_internal_app, `description`,
                     summary, export_description, docs_url, privacy_policy_link, terms_of_service_link, feature, type,
                     tags, author, about, key_features, media_content, config)
                    VALUES (:title, :tagline, :url, :logo, :logo_darkmode, :static_key, :is_static, :is_internal_app,
                            :description, :summary, :export_description, :docs_url, :privacy_policy_link,
                            :terms_of_service_link, :feature, :type, :tags, :author, :about, :key_features,
                            :media_content, :config);
                SQL,
            $parameters,
            ['config' => Types::STRING, 'tags' => Types::STRING]
        );

        $this->addSql(
            <<<'SQL'
                    INSERT INTO integration_definition_category (integration_definition_id, integration_category_id)
                        SELECT integration_definition.id, integration_category.id
                        FROM integration_definition, integration_category
                        WHERE static_key = :static_key AND integration_category.name = :category_name;
                SQL,
            $parameters,
            ['config' => Types::STRING, 'tags' => Types::STRING]
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql(
            <<<'SQL'
                DELETE FROM integration_definition_category
                    WHERE integration_definition_id = (SELECT id FROM integration_definition WHERE static_key = :static_key);
                SQL,
            ['static_key' => Integration::TYPEFORM],
        );

        $this->addSql(
            <<<'SQL'
                DELETE FROM integration_definition WHERE static_key = :static_key;
                SQL,
            ['static_key' => Integration::TYPEFORM],
        );
    }
}
