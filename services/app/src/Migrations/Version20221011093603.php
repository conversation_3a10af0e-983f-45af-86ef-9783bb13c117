<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Driver\Exception as DriverException;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\MigrationException;
use Lokalise\Enums\Feature;

final class Version20221011093603 extends AbstractMigration
{
    private const FEATURE_ID = Feature::INTEGRATION_SMC;
    private const PLAN_LIST = [
        // Enterprise
        72,
        76,
        156,
        159,
        160,
        256,
        259,
        260,
        4730,
        5718,
        7495,
        // Others
        9999,
        9998,
    ];

    private array $plans;

    public function getDescription(): string
    {
        return 'Add Salesforce Marketing Cloud integration to the plans';
    }

    /**
     * @throws DriverException
     * @throws Exception
     * @throws MigrationException
     */
    public function preUp(Schema $schema): void
    {
        parent::preUp($schema);
        $this->fetchPlans();
    }

    /**
     * @throws DriverException
     * @throws Exception
     * @throws MigrationException
     */
    public function preDown(Schema $schema): void
    {
        parent::preDown($schema);
        $this->fetchPlans();
    }

    /**
     * @throws DriverException
     * @throws Exception
     */
    private function fetchPlans(): void
    {
        $this->plans = $this->connection->executeQuery('
        SELECT
            id,
            features
        FROM
            plan
        WHERE
            plan_id IN (:plans)
        ', [
            'plans' => self::PLAN_LIST,
        ], [
            'plans' => Connection::PARAM_INT_ARRAY,
        ])->fetchAllAssociativeIndexed();
    }

    private function updatePlan(int $planId, array $features): void
    {
        $this->addSql('
        UPDATE plan
        SET
            features = :features
        WHERE
            id = :planId
        ', [
            'features' => json_encode($features),
            'planId' => $planId,
        ]);
    }

    public function up(Schema $schema): void
    {
        $this->connection->beginTransaction();

        foreach ($this->plans as $planId => $planRow) {
            $featureArr = json_decode($planRow['features'], true);

            if (in_array(self::FEATURE_ID, $featureArr, false)) {
                continue;
            }

            $featureArr[] = self::FEATURE_ID;

            $this->updatePlan($planId, $featureArr);
        }

        $this->connection->commit();
    }

    public function down(Schema $schema): void
    {
        $this->connection->beginTransaction();

        foreach ($this->plans as $planId => $planRow) {
            $featureArr = json_decode($planRow['features'], true);

            $key = array_search(self::FEATURE_ID, $featureArr, true);

            if ($key !== false) {
                unset($featureArr[$key]);

                $this->updatePlan($planId, $featureArr);
            }
        }

        $this->connection->commit();
    }
}
