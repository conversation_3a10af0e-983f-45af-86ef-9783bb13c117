<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Lokalise\Enums\Feature;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20210528075803 extends AbstractMigration
{
    private const FEATURE_ID = Feature::INTEGRATION_ZENDESK_DC;
    private const PLAN_LIST = [
        154,
        155,
        156,
        157,
        158,
        159,
        160,
        9998,
        9999,
    ];
    /**
     * @var array[]
     */
    private array $plans;

    public function getDescription(): string
    {
        return 'Add Zendesk DC integration to the plans';
    }

    private function fetchPlans(): void
    {
        $this->plans = $this->connection->fetchAll(
            'SELECT id, features
             FROM plan
             WHERE plan_id IN (:plans)',
            [
                'plans' => self::PLAN_LIST,
            ],
            [
                'plans' => Connection::PARAM_INT_ARRAY,
            ]
        );
    }

    private function updatePlan(string $planId, array $features): void
    {
        $this->addSql(
            'UPDATE plan
                 SET features = :features
                 WHERE id = :planId',
            [
                'features' => json_encode($features),
                'planId' => $planId,
            ]
        );
    }

    public function preUp(Schema $schema): void
    {
        parent::preUp($schema);
        $this->fetchPlans();
    }

    public function preDown(Schema $schema): void
    {
        parent::preDown($schema);
        $this->fetchPlans();
    }

    public function up(Schema $schema): void
    {
        foreach ($this->plans as $planRow) {
            $featureArr = json_decode($planRow['features']);

            if (in_array(self::FEATURE_ID, $featureArr, false)) {
                continue;
            }

            $featureArr[] = self::FEATURE_ID;

            $this->updatePlan((string) $planRow['id'], $featureArr);
        }
    }

    public function down(Schema $schema): void
    {
        foreach ($this->plans as $planRow) {
            $featureArr = json_decode($planRow['features']);

            if (!in_array(self::FEATURE_ID, $featureArr, false)) {
                continue;
            }

            $featureArrNew = array_values(array_diff($featureArr, [self::FEATURE_ID]));

            $this->updatePlan((string) $planRow['id'], $featureArrNew);
        }
    }
}
