<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Lokalise\Constants\Plan;

final class Version20250530100652 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'GRO-730 Set the limits of AI Words and OTA for New Plans';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'SQL'
                        UPDATE plan SET processed_words_limit = 1000000,
                        pro_ai_translations_limit = 150000
                        WHERE plan_id IN (:plans)
                SQL,
            [
                'plans' => [Plan::GEN2_ADVANCED_MONTHLY, Plan::GEN2_ADVANCED_YEARLY],
            ],
            [
                'plans' => Connection::PARAM_INT_ARRAY,
            ]
        );

        $this->addSql(
            <<<'SQL'
                        UPDATE plan SET processed_words_limit = 3000000,
                        pro_ai_translations_limit = 400000
                        WHERE plan_id = :plan
                SQL,
            [
                'plan' => Plan::GEN2_ENTERPRISE_YEARLY,
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql(
            <<<'SQL'
                        UPDATE plan SET processed_words_limit = 2000000,
                        pro_ai_translations_limit = 200000
                        WHERE plan_id IN (:plans)
                SQL,
            [
                'plans' => [Plan::GEN2_ADVANCED_MONTHLY, Plan::GEN2_ADVANCED_YEARLY],
            ],
            [
                'plans' => Connection::PARAM_INT_ARRAY,
            ]
        );

        $this->addSql(
            <<<'SQL'
                        UPDATE plan SET processed_words_limit = null,
                        pro_ai_translations_limit = 1000000
                        WHERE plan_id = :plan
                SQL,
            [
                'plan' => Plan::GEN2_ENTERPRISE_YEARLY,
            ]
        );
    }
}
