<?php

declare(strict_types=1);

namespace Lokalise\TeamDomain\Repository;

use DateTimeInterface;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Lokalise\TeamDomain\Entity\TeamJoinRequest;
use Lokalise\Type\UserTeamRole;

/**
 * @method TeamJoinRequest|null find($id, $lockMode = null, $lockVersion = null)
 * @method TeamJoinRequest|null findOneBy(array $criteria, array $orderBy = null)
 * @method TeamJoinRequest[] findAll()
 * @method TeamJoinRequest[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TeamJoinRequestRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TeamJoinRequest::class);
    }

    public function create(
        int $userId,
        int $teamId,
        ?int $handledBy = null,
        ?UserTeamRole $approvedRole = null,
        ?DateTimeInterface $handledAt = null
    ): TeamJoinRequest {
        $joinRequest = (new TeamJoinRequest())
            ->setUserId($userId)
            ->setTeamId($teamId)
            ->setHandledBy($handledBy)
            ->setApprovedRole($approvedRole)
            ->setHandledAt($handledAt);

        $this->save($joinRequest);

        return $joinRequest;
    }

    public function save(TeamJoinRequest $teamJoinRequest): void
    {
        $this->getEntityManager()->persist($teamJoinRequest);
        $this->getEntityManager()->flush();
    }

    /**
     * @return TeamJoinRequest[]
     */
    public function findUnhandledRequestsByUserId(int $userId): array
    {
        $qb = $this->getUnhandledRequestsByUserIdQueryBuilder($userId);
        $qb = $this->createQueryBuilder('r');

        return $qb
            ->where($qb->expr()->isNull('r.handledAt'))
            ->andWhere('r.userId = :userId')
            ->setParameter('userId', $userId)
            ->getQuery()
            ->getResult();
    }

    public function countUnhandledRequestsByUserId(int $userId): int
    {
        $qb = $this->getUnhandledRequestsByUserIdQueryBuilder($userId);
        $qb
            ->select('count(r)')
            ->setMaxResults(1);

        return (int) $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @return TeamJoinRequest[]
     */
    public function findUnhandledRequestsByUserIdAndTeamId(int $userId, int $teamId): array
    {
        $qb = $this->createQueryBuilder('r');

        return $qb
            ->where($qb->expr()->isNull('r.handledAt'))
            ->andWhere('r.userId = :userId')
            ->andWhere('r.teamId = :teamId')
            ->setParameter('userId', $userId)
            ->setParameter('teamId', $teamId)
            ->getQuery()
            ->getResult();
    }

    /**
     * @return TeamJoinRequest[]
     */
    public function findUnhandledRequestsByTeamId(int $teamId): array
    {
        $qb = $this->createQueryBuilder('r');

        return $qb
            ->where($qb->expr()->isNull('r.handledAt'))
            ->andWhere('r.teamId = :teamId')
            ->setParameter('teamId', $teamId)
            ->getQuery()
            ->getResult();
    }

    private function getUnhandledRequestsByUserIdQueryBuilder(int $userId): QueryBuilder
    {
        $qb = $this->createQueryBuilder('r');

        return $qb
            ->where($qb->expr()->isNull('r.handledAt'))
            ->andWhere('r.userId = :userId')
            ->setParameter('userId', $userId);
    }
}
