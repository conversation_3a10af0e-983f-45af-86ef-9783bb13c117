<?php

declare(strict_types=1);

namespace Lokalise\Permission\MessageHandler;

use Lokalise\Common\MessageHandler\MessageHandlerInterface;
use Lokalise\Common\Project\ValueObject\ProjectId;
use Lokalise\Permission\Message\ProjectPermissionsUpdatedMessage;
use Lokalise\Permission\Service\PermissionCacheService;

class ProjectPermissionsUpdatedMessageHandler implements MessageHandlerInterface
{
    public function __construct(
        readonly private PermissionCacheService $permissionCacheService,
    ) {
    }

    public function __invoke(ProjectPermissionsUpdatedMessage $message): void
    {
        $this->permissionCacheService->clearDataForProject(ProjectId::fromSafe($message->getProjectId()));
    }
}
