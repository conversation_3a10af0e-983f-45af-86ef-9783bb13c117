<?php

declare(strict_types=1);

namespace Lokalise\StyleGuide\Request\ParamConverter;

use Lokalise\Common\Request\ParamConverter\AbstractRequestToDtoParamConverter;
use Lokalise\Exception\ValidationViolationException;
use Lokalise\Services\TeamService;
use Lokalise\StyleGuide\Request\Dto\StyleGuideApiSearchRequest;
use Lokalise\Validation\Constraint as LokaliseAssert;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContext;
use Symfony\Component\Validator\Validation;

final class StyleGuideApiSearchRequestParamConverter extends AbstractRequestToDtoParamConverter
{
    public function __construct(private readonly TeamService $teamService)
    {
    }

    protected function dtoFqcn(): string
    {
        return StyleGuideApiSearchRequest::class;
    }

    protected function createDto(Request $request): StyleGuideApiSearchRequest
    {
        $validator = Validation::createValidator();

        $input = $request->query->all();

        $teamIds = $input['teamIds'];
        $projects = [[]];
        $languages = [[]];
        foreach ($teamIds as $teamId) {
            $languages[] = $this->teamService->getTeamProjectsLanguages($teamId);
            $projects[] = $this->teamService->getTeamProjects($teamId);
        }
        $allLanguages = array_merge(...$languages);
        $allProjects = array_merge(...$projects);
        $violations = $validator->validate($input, new Assert\Collection([
            'fields' => [
                'teamIds' => [
                    new Assert\Required(
                        new Assert\All(
                            [
                                new Assert\NotBlank(),
                                new Assert\Type('integer'),
                            ]
                        ),
                    ),
                ],
                'projects' => [
                    new Assert\Optional(
                        [
                            new Assert\Type('array'),
                            new Assert\All(
                                [
                                    new Assert\NotBlank(),
                                    new LokaliseAssert\ProjectId(),
                                    new Assert\Callback(static function ($value, ExecutionContext $context) use ($allProjects) {
                                        if (empty(array_filter($allProjects, fn ($project) => $project['id'] === $value))) {
                                            $context->buildViolation(sprintf('Project %s does not exist in given team', $value))
                                                ->addViolation();
                                        }
                                    }),
                                ]
                            ),
                        ]
                    ),
                ],
                'languages' => [
                    new Assert\Optional(
                        [
                            new Assert\Type('array'),
                            new Assert\All(
                                [
                                    new Assert\NotBlank(),
                                    new Assert\Type('integer'),
                                    new Assert\Callback(static function ($value, ExecutionContext $context) use ($allLanguages) {
                                        $languageId = (int) $value;
                                        if ($languageId !== 0 && empty(array_filter($allLanguages, fn ($language) => (int) $language['id'] === $languageId))) {
                                            $context->buildViolation(sprintf('Team does not have projects with language %s', $languageId))
                                                ->addViolation();
                                        }
                                    }),
                                ]
                            ),
                        ]
                    ),
                ],
            ],
            'allowExtraFields' => true,
        ]));

        if ($violations->count() > 0) {
            throw new ValidationViolationException($violations);
        }

        $languages = $input['languages'];
        $projects = $input['projects'];

        return new StyleGuideApiSearchRequest(
            $teamIds,
            $languages,
            $projects
        );
    }
}
