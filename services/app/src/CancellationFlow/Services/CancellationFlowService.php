<?php

declare(strict_types=1);

namespace Lokalise\CancellationFlow\Services;

use DateTime;
use DateTimeImmutable;
use InvalidArgumentException;
use Lokalise\CancellationFlow\Dto\TeamCancellationDiscountInformation;
use Lokalise\CancellationFlow\Dto\TeamCancellationDiscountReminderInformation;
use Lokalise\CancellationFlow\Entity\TeamCancellationFlowCouponReminderEntity;
use Lokalise\CancellationFlow\Repository\TeamCancellationFlowCouponReminderRepository;
use Lokalise\Common\Log\WithChannelContext;
use Lokalise\Common\Team\ValueObject\TeamId;
use Lokalise\Constants\Plan as PlanConstants;
use Lokalise\Constants\PlanPeriod;
use Lokalise\Coupon\Service\TeamDiscountCouponsService;
use Lokalise\Entity\AlterSubscriptionPlanParams;
use Lokalise\Entity\Plan;
use Lokalise\Enums\CancellationFlow;
use Lokalise\Enums\Feature;
use Lokalise\Exception\CurrentPlanSubscriptionLineNotFoundException;
use Lokalise\Services\PlanService;
use Lokalise\Services\Stripe\StripeService;
use Lokalise\TeamManagement\Team\Entity\TeamEntity;
use Psr\Log\LoggerInterface;
use Stripe\Coupon;
use Stripe\Exception\ApiErrorException;
use Stripe\Invoice;
use Stripe\Subscription;
use Stripe\SubscriptionItem;
use Stripe\SubscriptionSchedule;

class CancellationFlowService
{
    public const string CANCELLATION_FLOW_FEATURE_FLAG = 'growth_cancellation_flow';
    public const string CANCELLATION_FLOW_COUPON_TYPE = 'cancellation_flow_discount';
    public const int CANCELLATION_FLOW_REMINDER_DISCOUNT_AMOUNT_PERCENT = 50;
    public const int CANCELLATION_FLOW_DISCOUNT_MONTHS_REASON_TOO_EXPENSIVE = 4;
    public const int CANCELLATION_FLOW_DISCOUNT_MONTHS_REASON_LOCALISATION_NO_LONGER_RELEVANT = 5;
    public const int CANCELLATION_FLOW_DISCOUNT_ANNUAL = 12;

    public function __construct(
        private readonly PlanService $planService,
        private readonly StripeService $stripeService,
        private readonly TeamDiscountCouponsService $teamDiscountCouponsService,
        private readonly TeamCancellationFlowCouponReminderRepository $teamCancellationFlowCouponReminderRepository,
        private LoggerInterface $logger,
        private readonly string $subscriptionStartProductId,
        private readonly string $subscriptionEssentialProductId,
        private readonly string $subscriptionProProductId,
    ) {
        $this->logger = new WithChannelContext($this->logger, 'CANCELLATION_FLOW');
    }

    public function getPlansInfoForCancellationFlow(Plan $currentPlan, DateTime $expires, string $reason): array
    {
        $discountMonths = $this->getDiscountMonthsCount($reason);

        $availablePlans = $this->planService->getAvailableSelfServicePlans();
        $yearlyPlanRenewalDateWithoutDiscount = (clone $expires)->modify('+1 year');
        $monthlyPlanRenewalDateWithoutDiscount = (clone $expires)->modify(sprintf('+%s months', $discountMonths));
        $planFeatures = [
            'Start' => [
                Feature::ProjectAutomations => Feature::NAME_MAP[Feature::ProjectAutomations],
                Feature::IntegrationGitLab => Feature::NAME_MAP[Feature::IntegrationGitLab],
                Feature::OTA => Feature::NAME_MAP[Feature::OTA],
            ],
            'Essential' => [
                Feature::OfflineTranslation => Feature::NAME_MAP[Feature::OfflineTranslation],
                Feature::Screenshots => Feature::NAME_MAP[Feature::Screenshots],
                Feature::BulkActionsSnapshots => Feature::NAME_MAP[Feature::BulkActionsSnapshots],
            ],
            'Pro' => [
                Feature::AdvancedSecurity => Feature::NAME_MAP[Feature::AdvancedSecurity],
                Feature::IntegrationGitHubEnterprise => Feature::NAME_MAP[Feature::IntegrationGitHubEnterprise],
                Feature::IntegrationHubSpot => Feature::NAME_MAP[Feature::IntegrationHubSpot],
            ],
        ];

        $plansWithDiscount = match ($currentPlan->getDisplayName()) {
            'Start' => [
                'Start' => 1,
                'Essential' => 1,
                'Pro' => 1,
            ],
            'Essential' => [
                'Start' => 0,
                'Essential' => 1,
                'Pro' => 1,
            ],
            'Pro' => [
                'Start' => 0,
                'Essential' => 0,
                'Pro' => 1,
            ],
            default => throw new InvalidArgumentException(sprintf('Plan %s is not supported', $currentPlan->getDisplayName())),
        };

        $modifiedPlans = [];
        foreach ($availablePlans as $plan) {
            $planPeriod = $plan->getPeriod()->getValue();
            $modifiedPlans[$planPeriod][] = [
                'id' => $plan->getPlanId()->getValue(),
                'name' => $plan->getName(),
                'basePrice' => $plan->getPrice(),
                'discount' => $plansWithDiscount[$plan->getDisplayName()] === 1 ? $plan->getPrice() * 0.5 : 0,
                'periodCountDiscount' => $planPeriod === PlanPeriod::YEAR ? 1 : $discountMonths,
                'renewalWithoutDiscount' => $planPeriod === PlanPeriod::YEAR ? $yearlyPlanRenewalDateWithoutDiscount : $monthlyPlanRenewalDateWithoutDiscount,
                'features' => $planFeatures[$plan->getDisplayName()],
            ];
        }

        return $modifiedPlans;
    }

    public function getNeighboringPlanId(int $currentPlanId): int
    {
        return match ($currentPlanId) {
            PlanConstants::V2_SEAT_BASED_START_ANNUAL => PlanConstants::V2_SEAT_BASED_START_MONTHLY,
            PlanConstants::V2_SEAT_BASED_START_MONTHLY => PlanConstants::V2_SEAT_BASED_START_ANNUAL,
            PlanConstants::V2_SEAT_BASED_ESSENTIAL_ANNUAL => PlanConstants::V2_SEAT_BASED_ESSENTIAL_MONTHLY,
            PlanConstants::V2_SEAT_BASED_ESSENTIAL_MONTHLY => PlanConstants::V2_SEAT_BASED_ESSENTIAL_ANNUAL,
            PlanConstants::V3_SEAT_BASED_PRO_ANNUAL => PlanConstants::V3_SEAT_BASED_PRO_MONTHLY,
            PlanConstants::V3_SEAT_BASED_PRO_MONTHLY => PlanConstants::V3_SEAT_BASED_PRO_ANNUAL,
            default => throw new InvalidArgumentException('Unable to find neighboring plan'),
        };
    }

    public function createDiscountCoupon(TeamEntity $team, Plan $newPlan, string $reason): Coupon
    {
        $discountMonths = $newPlan->getPeriod()->getValue() === PlanPeriod::MONTH ? $this->getDiscountMonthsCount($reason) : self::CANCELLATION_FLOW_DISCOUNT_ANNUAL;

        $stripePrice = $this->stripeService->getPriceService()->retrievePrice((string) $newPlan->getPlanId()->getValue());

        return $this->stripeService->getCouponService()->createCoupon(
            $team,
            [$stripePrice->product],
            self::CANCELLATION_FLOW_REMINDER_DISCOUNT_AMOUNT_PERCENT,
            $discountMonths
        );
    }

    public function attachCouponToSubscription(TeamEntity $team, Coupon $coupon): void
    {
        $this->stripeService->getSubscriptionService()->attachCouponToSubscription($team->getStripeSubscription(), $coupon->id);
    }

    public function scheduleSubscriptionUpdate(
        TeamEntity $teamEntity,
        Plan $currentTeamPlan,
        Plan $upcomingTeamPlan,
        Coupon $coupon,
    ): void {
        $stripeSubscription = $this->stripeService->getSubscriptionService()
            ->retrieve($teamEntity->getStripeSubscription());

        $params = new AlterSubscriptionPlanParams(
            $teamEntity,
            $teamEntity->getStripeSubscription(),
            (string) $upcomingTeamPlan->getPlanId()->getValue(),
            $teamEntity->getPlanSeatLimit(),
            $teamEntity->isInvoiced(),
            false,
            $currentTeamPlan->getPlanId()->getValue(),
            $stripeSubscription
        );

        $this->schedulePlanChangeOnNextCycle($params, $currentTeamPlan, $upcomingTeamPlan, $coupon);
    }

    /**
     * @throws CurrentPlanSubscriptionLineNotFoundException
     * @throws ApiErrorException
     */
    private function schedulePlanChangeOnNextCycle(
        AlterSubscriptionPlanParams $params,
        Plan $currentPlan,
        Plan $upcomingPlan,
        Coupon $coupon,
    ): void {
        $subscription = $params->getExistingSubscription();

        if (null === $subscription) {
            throw new InvalidArgumentException('Subscription not found');
        }

        $currentSubscriptionItems = [];
        /**
         * @var SubscriptionItem $subscriptionItem
         */
        foreach ($subscription->items as $subscriptionItem) {
            $currentSubscriptionItems[] = [
                'price' => $subscriptionItem->price->id,
                'quantity' => $subscriptionItem->quantity,
            ];
        }

        $lastCycleSubscriptionItems = $this->stripeService->getSubscriptionService()->getCleanSubscriptionItems(
            $subscription,
            (string) $currentPlan->getPlanId()->getValue(),
            $upcomingPlan->toLegacyDbArray(),
            $params->getSeatCount()
        );

        $phases = [
            [
                'start_date' => 'now',
                'end_date' => $subscription->current_period_end,
                'items' => $currentSubscriptionItems,
            ],
            [
                'start_date' => $subscription->current_period_end,
                'items' => $lastCycleSubscriptionItems,
                'proration_behavior' => 'create_prorations',
                'collection_method' => $params->isPayingWithInvoice() ? Invoice::COLLECTION_METHOD_SEND_INVOICE : Invoice::COLLECTION_METHOD_CHARGE_AUTOMATICALLY,
                'discounts' => [
                    [
                        'coupon' => $coupon->id,
                    ],
                ],
            ],
        ];

        /**
         * @var SubscriptionSchedule $schedule
         */
        $scheduleId = $subscription->schedule;
        if (empty($scheduleId)) {
            $schedule = SubscriptionSchedule::create(
                [
                    'from_subscription' => $subscription->id,
                ]
            );
            $scheduleId = $schedule->id;

            array_unshift($phases, [
                'end_date' => 'now',
                'start_date' => $subscription->current_period_start,
                'items' => $currentSubscriptionItems,
            ]);
        } else {
            $schedule = SubscriptionSchedule::retrieve($scheduleId);
            $phases[0]['start_date'] = $schedule->current_phase->start_date;
        }

        SubscriptionSchedule::update($scheduleId, [
            'proration_behavior' => Subscription::PRORATION_BEHAVIOR_CREATE_PRORATIONS,
            'phases' => $phases,
            'end_behavior' => 'release',
        ]);
    }

    public function isCancellationFlowTeamCouponExists(int $teamId): bool
    {
        return $this->teamDiscountCouponsService->isCancellationFlowTeamCouponExists($teamId);
    }

    public function isCancellationFlowTeamCouponExistsAndRedeemed(int $teamId): bool
    {
        return $this->teamDiscountCouponsService->isCancellationFlowTeamCouponExistsAndRedeemed($teamId);
    }

    public function isCancellationFlowTeamReminderExists(int $teamId): bool
    {
        return $this->getCancellationFlowTeamReminder($teamId) !== null;
    }

    public function getCancellationFlowTeamReminder(int $teamId): ?TeamCancellationFlowCouponReminderEntity
    {
        return $this->teamCancellationFlowCouponReminderRepository->findOneBy(['teamId' => $teamId]);
    }

    public function scheduleReminder(TeamEntity $team, Plan $currentPlan, int $afterMonths, int $createdBy): void
    {
        $reminder = TeamCancellationFlowCouponReminderEntity::createInitialRecord(
            $team->getId(),
            $currentPlan->getPlanId()->getValue(),
            $team->getPlanSeatLimit(),
            $afterMonths,
            $createdBy,
        );

        $this->teamCancellationFlowCouponReminderRepository->save($reminder);
    }

    /**
     * @return array<TeamCancellationFlowCouponReminderEntity>
     */
    public function getRemindersToSend(): array
    {
        return $this->teamCancellationFlowCouponReminderRepository->getRemindersToSend();
    }

    public function markReminderAsSent(TeamCancellationFlowCouponReminderEntity $reminderEntity): void
    {
        $this->teamCancellationFlowCouponReminderRepository->markReminderAsSent($reminderEntity);
    }

    public function updateRemindAt(TeamCancellationFlowCouponReminderEntity $reminderEntity, DateTime $dateTime): void
    {
        $this->teamCancellationFlowCouponReminderRepository->updateRemindAt($reminderEntity, $dateTime);
    }

    public function getReminderByToken(string $token): ?TeamCancellationFlowCouponReminderEntity
    {
        return $this->teamCancellationFlowCouponReminderRepository->getReminderByToken($token);
    }

    public function getReminderByTeamId(int $teamId): ?TeamCancellationFlowCouponReminderEntity
    {
        return $this->teamCancellationFlowCouponReminderRepository->getReminderByTeamId($teamId);
    }

    public function createDiscountCouponFromReminder(TeamEntity $teamEntity, Plan $previousPlan, Plan $currentPlan): Coupon
    {
        $discountMonths = ($currentPlan->isPaid() ? $currentPlan : $previousPlan)->getPeriod()->getValue() === PlanPeriod::YEAR
            ? self::CANCELLATION_FLOW_DISCOUNT_ANNUAL
            : self::CANCELLATION_FLOW_DISCOUNT_MONTHS_REASON_LOCALISATION_NO_LONGER_RELEVANT;

        return $this->stripeService->getCouponService()->createCoupon(
            $teamEntity,
            $this->getStripeProductIdsForTheDiscount($previousPlan),
            self::CANCELLATION_FLOW_REMINDER_DISCOUNT_AMOUNT_PERCENT,
            $discountMonths,
        );
    }

    public function addStripeCouponIdToReminder(TeamCancellationFlowCouponReminderEntity $reminder, string $couponId): void
    {
        $this->teamCancellationFlowCouponReminderRepository->updateStripeCouponId($reminder, $couponId);
    }

    public function getTeamDiscountInformation(TeamId $teamId, Plan $plan): ?TeamCancellationDiscountInformation
    {
        if (false === $plan->isPaid()) {
            return null;
        }

        $cancellationFlowDiscountCoupon = $this->teamDiscountCouponsService->getCancellationFlowTeamCoupon($teamId->getValue());

        if ($cancellationFlowDiscountCoupon === null) {
            return null;
        }

        $coupon = null;

        try {
            $couponService = $this->stripeService->getCouponService();
            $coupon = $couponService->getCoupon($cancellationFlowDiscountCoupon->stripeId);
        } catch (ApiErrorException $e) {
            $this->logger->warning(
                'Failed to retrieve coupon',
                [
                    'team_id' => $teamId->getValue(),
                    'coupon_id' => $cancellationFlowDiscountCoupon->stripeId,
                    'plan_id' => $plan->getPlanId()->getValue(),
                    'exception' => $e,
                ]
            );
        }

        $couponExists = null !== $coupon;
        $couponActive = false;
        $validUntilDate = null;
        $validFromDate = null;
        $percentOff = null;

        if ($couponExists) {
            $percentOff = $coupon->percent_off;
            $validFrom = $coupon->metadata->offsetGet('start_date');

            $validFromDate = DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $validFrom);
            $modifier = $plan->getPeriod()->getValue() === PlanPeriod::YEAR ? '+1 year' : sprintf('+%s months', $coupon->metadata->offsetGet('valid_months'));
            $validUntilDate = (clone $validFromDate)->modify($modifier);
            if (new DateTime() < $validUntilDate) {
                $couponActive = true;
            }
        }

        return new TeamCancellationDiscountInformation(
            $couponExists,
            $couponActive,
            $validUntilDate,
            $validFromDate,
            $percentOff
        );
    }

    public function getDiscountReminderInformation(TeamId $teamId): ?TeamCancellationDiscountReminderInformation
    {
        $cancellationDiscountReminderEntity = $this->getCancellationFlowTeamReminder($teamId->getValue());

        if (null === $cancellationDiscountReminderEntity) {
            return null;
        }

        return new TeamCancellationDiscountReminderInformation(
            token: $cancellationDiscountReminderEntity->getToken(),
            remindAt: DateTimeImmutable::createFromMutable($cancellationDiscountReminderEntity->getRemindAt()),
            reminderSentAt: $cancellationDiscountReminderEntity->getReminderSentAt() ? DateTimeImmutable::createFromMutable($cancellationDiscountReminderEntity->getReminderSentAt()) : null,
            planSeatCount: $cancellationDiscountReminderEntity->getPlanSeatCount(),
        );
    }

    private function getStripeProductIdsForTheDiscount(Plan $plan): array
    {
        return match ($plan->getDisplayName()) {
            'Start' => [$this->subscriptionStartProductId, $this->subscriptionEssentialProductId, $this->subscriptionProProductId],
            'Essential' => [$this->subscriptionEssentialProductId, $this->subscriptionProProductId],
            'Pro' => [$this->subscriptionProProductId],
            default => throw new InvalidArgumentException(sprintf('Plan %s is not supported', $plan->getDisplayName())),
        };
    }

    private function getDiscountMonthsCount(string $reason): int
    {
        return match ($reason) {
            CancellationFlow::CHURN_REASON_TOO_EXPENSIVE => self::CANCELLATION_FLOW_DISCOUNT_MONTHS_REASON_TOO_EXPENSIVE,
            CancellationFlow::CHURN_REASON_LOCALISATION_NO_LONGER_RELEVANT => self::CANCELLATION_FLOW_DISCOUNT_MONTHS_REASON_LOCALISATION_NO_LONGER_RELEVANT,
            default => throw new InvalidArgumentException(sprintf('Reason %s is not supported', $reason)),
        };
    }
}
