<?php

declare(strict_types=1);

namespace Lokalise\Collaboration\Comment\Service\AttachPoint\Resolver;

use Lokalise\Collaboration\Comment\Factory\AttachPointFactory;
use Lokalise\Collaboration\Comment\Message\CommentCountReindexingMessage;
use Lokalise\Collaboration\Comment\Model\AccessCheck;
use Lokalise\Collaboration\Comment\Model\AttachPoint\AbstractAttachPoint;
use Lokalise\Collaboration\Comment\Model\AttachPoint\AttachPointNameCollection;
use Lokalise\Collaboration\Comment\Model\AttachPoint\AttachPointNameRequest;
use Lokalise\Collaboration\Comment\Model\AttachPoint\AttachPointType;
use Lokalise\Collaboration\Comment\Model\Comment;
use Lokalise\Collaboration\Comment\Model\Listing\Filter;
use Lokalise\Collaboration\Comment\Model\UserId;
use Lokalise\Collaboration\Comment\Service\AttachPoint\AttachPointIdIdentityMapperTrait;
use Lokalise\Collaboration\Comment\Service\AttachPoint\AttachPointResolver;
use Lokalise\Collaboration\Comment\Service\LegacyCommentsService;
use Lokalise\Common\Project\ValueObject\ProjectId;
use Lokalise\EventDispatcher\EventDispatcher;
use Lokalise\Events\Project\Key\KeyCommentAddedEvent;
use Lokalise\Exception\ProjectData\ProjectNotFoundException;
use Lokalise\Services\MessagePublisherInterface;
use Psr\Log\LoggerInterface;

class KeysAttachPointResolver implements AttachPointResolver
{
    use AttachPointIdIdentityMapperTrait;

    public function __construct(
        private readonly LegacyCommentsService $legacyCommentsService,
        private readonly MessagePublisherInterface $commentsMessagePublisher,
        private readonly LoggerInterface $logger,
        private readonly EventDispatcher $eventDispatcher
    ) {
    }

    public static function handledType(): AttachPointType
    {
        return AttachPointType::Keys;
    }

    public function doesUserHaveAccessTo(AccessCheck $accessCheck): bool
    {
        return $this->legacyCommentsService->keyCheck(
            $accessCheck->getProject(),
            (int) $accessCheck->getAttachPoint()->getId()
        );
    }

    public function provideAttachPointName(AttachPointNameRequest $attachPointNameRequest): string
    {
        return $this->legacyCommentsService->getKeyName(
            $attachPointNameRequest->getAttachPoint(),
            $attachPointNameRequest->getProjectId()
        );
    }

    public function provideAttachPointNames(array $attachPoints): AttachPointNameCollection
    {
        $collection = new AttachPointNameCollection();

        $keyNamesByIds = $this->legacyCommentsService->getKeyNamesByIds(
            array_map(static fn (AbstractAttachPoint $attachPoint) => (int) $attachPoint->getId(), $attachPoints)
        );

        foreach ($keyNamesByIds as $keyId => $keyName) {
            $collection->addName(
                AttachPointFactory::fromTypeAndId(AttachPointType::Keys->value, (string) $keyId),
                $keyName
            );
        }

        return $collection;
    }

    public function adjustListingFilter(ProjectId $projectId, UserId $userId, Filter $filter): void
    {
        // No action for this attachPoint
    }

    public function postParentCommentCreateAction(Comment $comment): void
    {
        $this->commentsMessagePublisher->dispatch(
            new CommentCountReindexingMessage($comment->getProjectId(), $comment->getAttachPoint())
        );

        // Fire webhook event 'project.key.comment.added'
        $this->keyCommentAddedWebhookEvent(
            $comment->getProjectId(),
            $comment,
            $this->provideAttachPointName(
                new AttachPointNameRequest($comment->getProjectId(), $comment->getAttachPoint())
            )
        );
    }

    /**
     * Fires project.key.comment.added event
     * used in Lokalise webhooks
     */
    private function keyCommentAddedWebhookEvent(ProjectId $projectId, Comment $comment, string $attachPointName): void
    {
        try {
            $projectData = $this->legacyCommentsService->getProjectData($projectId);
        } catch (ProjectNotFoundException $exception) {
            $this->logger->error($exception->getMessage(), [
                'exception' => $exception,
            ]);

            return;
        }

        $keyId = (int) $comment->getAttachPoint()->getId();

        $this->eventDispatcher->dispatch(
            new KeyCommentAddedEvent(
                $projectData->getMasterProjectId(),
                $projectData->getBranchId(),
                $keyId,
                $attachPointName,
                $comment->getMessage(),
                false
            ),
            KeyCommentAddedEvent::EVENT_NAME
        );

        $keyDetails = $this->legacyCommentsService->getKeyDetailsById($keyId);
        $keyMasterId = null;
        if (null !== $keyDetails && $keyDetails['master_reference_id'] !== null) {
            $keyMasterId = (int) $keyDetails['master_reference_id'];
        }

        if (null === $keyMasterId) {
            return;
        }

        // Duplicates events on master for branching
        $this->eventDispatcher->dispatch(
            new KeyCommentAddedEvent(
                $projectData->getMasterProjectId(),
                null,
                $keyMasterId,
                $attachPointName,
                $comment->getMessage(),
                false
            ),
            KeyCommentAddedEvent::EVENT_NAME
        );
    }

    public function postParentCommentDeleteAction(ProjectId $projectId, AbstractAttachPoint $attachPoint): void
    {
        $this->commentsMessagePublisher->dispatch(
            new CommentCountReindexingMessage($projectId, $attachPoint)
        );
    }
}
