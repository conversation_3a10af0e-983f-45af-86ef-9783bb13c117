<?php

declare(strict_types=1);

namespace Lokalise\Collaboration\Comment\Service;

use Lokalise\Collaboration\Comment\Model\Comment;
use Lokalise\Collaboration\Comment\Model\CommentReply;
use Lokalise\Collaboration\Comment\Model\ReadStateCollection;
use Lokalise\Collaboration\Comment\Model\ReadStateRange;
use Lokalise\Collaboration\Comment\Model\ReadStateRangeId;
use Lokalise\Collaboration\Comment\Model\ReadStateSnapshot;
use Lokalise\Collaboration\Comment\Model\UserId;
use Lokalise\Collaboration\Comment\Repository\CommentRepository;
use Lokalise\Collaboration\Comment\Repository\ReadStateRangeRepositoryInterface;
use Lokalise\Collaboration\Comment\Repository\ReadStateSnapshotRepositoryInterface;
use Lokalise\Collaboration\Comment\Service\AttachPoint\AttachPointGuard;
use Lokalise\Common\Project\ValueObject\ProjectId;
use Lokalise\Utils\DateTimeProviderInterface;

class ReadStateService
{
    public function __construct(
        private readonly DateTimeProviderInterface $dateTimeProvider,
        private readonly ReadStateRangeRepositoryInterface $readStateRangeRepository,
        private readonly AttachPointGuard $attachPointGuard,
        private readonly ReadStateSnapshotRepositoryInterface $readStateSnapshotRepository,
        private readonly CommentRepository $commentRepository,
    ) {
    }

    /**
     * Gets read state for set of comments for user.
     * If comment's author is user, we create fake read state to mark user's comment automatically as read.
     *
     * @param Comment[] $comments
     */
    public function getReadStateForCommentsForUser(array $comments, UserId $userId): ReadStateCollection
    {
        $projectIds = array_map(static fn (Comment $c) => $c->getProjectId()->value(), $comments);
        $projectIds = array_unique($projectIds);

        $snapshotsMap = $this->readStateSnapshotRepository->findForUserAndProjects(
            $userId,
            $projectIds
        );

        $readStateRanges = $this->readStateRangeRepository->getReadStateRangesForUser($userId, $comments);

        $readStateRangesForReplies = $this->readStateRangeRepository->getLastReadForReplies($comments);

        return new ReadStateCollection($userId, $readStateRanges, $snapshotsMap, $readStateRangesForReplies);
    }

    /**
     * @param Comment[] $comments
     */
    public function markParentCommentsAsReadForUser(
        UserId $userId,
        array $comments,
        ReadStateCollection $readStateCollection
    ): void {
        $now = $this->dateTimeProvider->get();

        $rangesToPersist = [];

        foreach ($comments as $comment) {
            $readStateRange = $readStateCollection->getReadStateRangeForThread($comment->getId());

            if ($readStateRange === null) {
                $readStateRange = new ReadStateRange(
                    ReadStateRangeId::createRandom(),
                    $comment->getProjectId(),
                    new UserId($userId->getValue()),
                    $comment->getAttachPoint(),
                    $comment->getCreatedAt(),
                    $now,
                    $comment->getId()
                );
            } elseif ($readStateRange->getLastReadAt() < $comment->getCreatedAt()) {
                $readStateRange->setLastReadAt($comment->getCreatedAt());
            }

            $rangesToPersist[] = $readStateRange;
        }

        $this->readStateRangeRepository->saveMany($rangesToPersist);
    }

    public function markThreadReadForAuthor(
        UserId $userId,
        CommentReply $commentReply,
    ): void {
        // See if we already have readStateRange entry for this thread for this user
        $readStateRange = $this->readStateRangeRepository
            ->findForParentCommentId($commentReply->getProjectId(), $userId, $commentReply->getParentId());

        $now = $this->dateTimeProvider->get();
        if (null === $readStateRange) {
            $readStateRange = new ReadStateRange(
                ReadStateRangeId::createRandom(),
                $commentReply->getProjectId(),
                new UserId($userId->getValue()),
                $commentReply->getAttachPoint(),
                $commentReply->getCreatedAt(),
                $now,
                $commentReply->getParentId(),
            );
        } elseif ($readStateRange->getLastReadAt() < $commentReply->getCreatedAt()) {
            $readStateRange->setLastReadAt($commentReply->getCreatedAt());
        }

        $this->readStateRangeRepository->saveMany([$readStateRange]);
    }

    /**
     * @param Comment[] $replies
     */
    public function markThreadAsRead(UserId $userId, Comment $parentComment, array $replies): void
    {
        $now = $this->dateTimeProvider->get();

        $lastReadAt = empty($replies)
            ? $parentComment->getCreatedAt()
            : $replies[array_key_last($replies)]->getCreatedAt();

        $readStateForParent = $this->readStateRangeRepository->findForParentCommentId(
            $parentComment->getProjectId(),
            $userId,
            $parentComment->getId(),
        );

        if ($readStateForParent === null) {
            $this->readStateRangeRepository->saveMany(
                [
                    new ReadStateRange(
                        ReadStateRangeId::createRandom(),
                        $parentComment->getProjectId(),
                        $userId,
                        $parentComment->getAttachPoint(),
                        $lastReadAt,
                        $now,
                        $parentComment->getId()
                    ),
                ]
            );

            return;
        }

        $readStateForParent->setLastReadAt($lastReadAt);

        $this->readStateRangeRepository->saveMany([$readStateForParent]);
    }

    public function markCommentsAsReadForUserInProject(UserId $userId, ProjectId $projectId): void
    {
        $this->attachPointGuard->ensureAccessToProject($projectId, $userId);

        $this->readStateSnapshotRepository->upsert(
            new ReadStateSnapshot(
                $projectId,
                $userId,
                $this->dateTimeProvider->get()
            )
        );
    }
}
