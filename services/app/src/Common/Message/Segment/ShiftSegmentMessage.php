<?php

declare(strict_types=1);

namespace Lokalise\Common\Message\Segment;

use Lokalise\Common\Key\ValueObject\KeyId;

class ShiftSegmentMessage
{
    public function __construct(
        private readonly KeyId $keyId,
        private readonly int $startingFromNumber,
        private readonly int $shiftBy
    ) {
    }

    public function getKeyId(): KeyId
    {
        return $this->keyId;
    }

    public function getStartingFromNumber(): int
    {
        return $this->startingFromNumber;
    }

    public function getShiftBy(): int
    {
        return $this->shiftBy;
    }
}
