<?php

namespace Lokalise\Common\Diff\DiffStrategy;

use <PERSON>fcherng\Diff\Differ;
use <PERSON>fcherng\Diff\Factory\RendererFactory;

/**
 * Compare is done using PHP Diff library
 * It's a much more efficient and rich solution comparing to own one
 *
 * @see https://github.com/jfcherng/php-diff
 */
class PhpDiffText implements DiffStrategyInterface, HighlightableInterface
{
    protected bool $highlight = true;

    protected array $context;

    /**
     * {@inheritDoc}
     */
    public function setContext(array $context): void
    {
        $this->context = $context;
    }

    public function supports(array $subject): bool
    {
        // Checking null is because of DEV-2394. We have some translations in DB set as NULL.
        // NULL is not scalar.
        return empty($subject['is_plural']) && (is_scalar($subject['translation']) || $subject['translation'] === null);
    }

    public function diff($from, $to)
    {
        if (!$this->highlight) {
            return;
        }

        return static::doCompare($from ?? '', $to ?? '');
    }

    /**
     * Do char based compare using PHP Diff library
     *
     * @see https://github.com/jfcherng/php-diff
     *
     * @return string HTML report
     */
    public static function doCompare(
        string $source,
        string $target,
        string $renderer = 'Combined',
        array $differOptions = [],
        array $rendererOptions = []
    ): string {
        $defaultRendererOptions = [
            'detailLevel' => 'char',
            'showHeader' => false,
        ];

        $rendererOptions = array_merge($defaultRendererOptions, $rendererOptions);

        // original calculate method breaks text in lines
        // but we need new lines changes to be calculated and rendered as well
        // DiffHelper::calculate($source, $target, $renderer, $differOptions, $rendererOptions)

        return RendererFactory::getInstance($renderer)
            ->setOptions($rendererOptions)
            ->render(
                Differ::getInstance()
                    ->setOldNew([$source], [$target])
                    ->setOptions($differOptions)
            );
    }

    /**
     * @return self
     */
    public function setHighlight(bool $highlight): HighlightableInterface
    {
        $this->highlight = $highlight;

        return $this;
    }
}
