<?php

namespace Lokalise\Integration\Model;

use InvalidArgumentException;

class IntegrationExportResultList
{
    private array $results;

    /**
     * @deprecated
     */
    private array $errors = [];

    public function __construct(array $localeCodes)
    {
        $results = [];
        foreach ($localeCodes as $code) {
            $results[$code] = new IntegrationExportResult();
        }
        $this->results = $results;
    }

    public function getLocales(): array
    {
        return array_keys($this->results);
    }

    /**
     * @deprecated
     */
    public function addError(string $error): void
    {
        $this->errors[] = $error;
    }

    public function get(string $localeCode): IntegrationExportResult
    {
        if (!isset($this->results[$localeCode])) {
            throw new InvalidArgumentException(sprintf("Locale '%s' is not available in results", $localeCode));
        }

        return $this->results[$localeCode];
    }

    /**
     * @param string[] $locales
     * @param string[] $messages
     */
    public function addSkipped(
        string $uniqueId,
        array $locales,
        ?string $reason = null,
        array $messages = []
    ): void {
        foreach ($locales as $locale) {
            $this->get($locale)->addSkipped(
                $uniqueId,
                $reason,
                $messages
            );
        }
    }

    /**
     * @param string[] $locales
     */
    public function addRemoved(
        string $uniqueId,
        array $locales
    ): void {
        foreach ($locales as $locale) {
            $this->get($locale)->addRemoved($uniqueId);
        }
    }

    /**
     * @param string[] $locales
     */
    public function addUpdated(string $uniqueId, array $locales): void
    {
        foreach ($locales as $locale) {
            $this->get($locale)->addUpdated($uniqueId);
        }
    }
}
