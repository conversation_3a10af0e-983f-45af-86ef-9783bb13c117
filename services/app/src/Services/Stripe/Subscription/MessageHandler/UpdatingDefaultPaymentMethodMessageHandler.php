<?php

declare(strict_types=1);

namespace Lokalise\Services\Stripe\Subscription\MessageHandler;

use Lokalise\Common\MessageHandler\MessageHandlerInterface;
use Lokalise\Services\Stripe\CustomerService;
use Lokalise\Services\Stripe\Subscription\Message\UpdatingDefaultPaymentMethod;

class UpdatingDefaultPaymentMethodMessageHandler implements MessageHandlerInterface
{
    private CustomerService $customerService;

    public function __construct(CustomerService $customerService)
    {
        $this->customerService = $customerService;
    }

    public function __invoke(UpdatingDefaultPaymentMethod $updatingDefaultPaymentMethod)
    {
        $this->customerService->updateCustomerDefaultPaymentMethod($updatingDefaultPaymentMethod->getTeamId());
    }
}
