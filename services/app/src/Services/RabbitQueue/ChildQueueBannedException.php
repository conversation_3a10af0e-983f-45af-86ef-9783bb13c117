<?php

namespace Lokalise\Services\RabbitQueue;

use Throwable;

class ChildQueueBannedException extends ChildQueueProcessingException
{
    public const DEFAULT_MESSAGE = 'Child queue banned';

    public function __construct(string $childQueueId, array $context = [], $message = self::DEFAULT_MESSAGE, ?Throwable $previous = null)
    {
        parent::__construct($childQueueId, $context, $message, $previous);
    }
}
