<?php

declare(strict_types=1);

namespace Lokalise\Services\Import\Formats;

use DOMDocument;
use DOMElement;
use DOMException;
use ICUMessageUtils;
use JsonException;
use LanguageService;
use Lokalise\Dto\Format\InternalFormatModel;
use Lokalise\Dto\Format\Key;
use Lokalise\Dto\Format\Translation;
use Lokalise\Enums\FileFormat;
use Lokalise\Exception\Import\FileParseException;
use Lokalise\Exception\Import\InvalidXliffException;
use Lokalise\Helper\AppleStringsDictHelper;
use Lokalise\Helper\KeyAncestorHelper;
use Lokalise\Services\Import\Configuration\ImportConfigurationInterface;
use Lokalise\Services\Import\Configuration\ImportXliffConfiguration;
use Lokalise\Services\Import\Utility\XliffValidatorUtility;
use Lokalise\Services\UnicodeUtil;
use stdClass;
use Throwable;
use TranslationService;

class ParserXliffFormat extends ParserBase implements ParserFormatInterface
{
    private TranslationService $translationService;
    private LanguageService $languageService;

    private DOMDocument $dom;
    private ?int $sourceLanguageId;
    private ?int $importLanguageId;
    private ?int $targetLanguageId;
    private array $keys = [];
    private array $keysTranslations = [];

    private const PLACEHOLDER_REPLACEMENTS = [
        '___LOKALISE___' => '&',
        // XLIFF inline items
        '___LOKALISE3___' => '<x ',
    ];
    public const TRANS_UNIT_ID_PREFIX = 'lokalise-';
    public const OFFLINE_XLIFF_ID_PATTERN = '/^lokalise-([0-9]+)(-([0-9]+))?$/';

    private const POSITIVE_PROOFREAD_STATES = ['final', 'signed-off'];

    public function __construct(
        TranslationService $translationService,
        LanguageService $languageService
    ) {
        $this->translationService = $translationService;
        $this->languageService = $languageService;
    }

    /**
     * @throws FileParseException
     */
    public function parse(string $data): array
    {
        if ($data === '') {
            throw new FileParseException('Empty document');
        }

        $this->importLanguageId = $this->getConfiguration()->getTargetLanguageId();

        $convertedData = UnicodeUtil::convertUtf16ToUtf8($data);

        $this->replaceValuesWithPlaceholders($convertedData);

        $this->dom = $this->getXmlDom($convertedData);

        try {
            XliffValidatorUtility::validate($this->dom);
        } catch (InvalidXliffException $e) {
            throw new FileParseException($e->getMessage(), 0, $e);
        }

        $this->initOfflineXliffLanguageData();

        $this->process();

        return array_values($this->keys);
    }

    public function convert(array $data): InternalFormatModel
    {
        $collection = new InternalFormatModel();

        foreach ($data as $keyItem) {
            $key = $this->createKey($keyItem['key']);
            $this->processKey($key, $keyItem);

            $translation = $this->createTranslation($keyItem['translation']);
            $translation->setProofread($keyItem['is_proofread'] ?? false);
            $translation->setIsFuzzy($keyItem['fuzzy'] ?? false);
            $key->addTranslation($translation);

            $collection->addKey($key);
        }

        return $collection;
    }

    /**
     * @throws FileParseException
     */
    private function getXmlDom(string $rawXml): DOMDocument
    {
        $dom = new DOMDocument();
        $dom->encoding = 'utf-8';
        $dom->recover = true;

        libxml_use_internal_errors(true);

        if (!$dom->loadXML($rawXml)) {
            throw new FileParseException('Error in XML format');
        }

        $this->checkLibXMLErrors();

        libxml_use_internal_errors(false);

        return $dom;
    }

    private function createKey(string $keyName): Key
    {
        $key = new Key();
        $key->setKey($keyName);

        return $key;
    }

    private function processKey(Key $key, array $keyParams): void
    {
        if (!empty($keyParams['plural'])) {
            $key->setPluralName($keyParams['plural']);
        }

        $this->setKeyDescription($key, $keyParams['comments']);

        $key->setComments($keyParams['comments']);

        if (!empty($keyParams['custom_attributes'])) {
            $key->setAttributes($keyParams['custom_attributes']);
        }

        $key->setContext($keyParams['context']);

        if (!empty($keyParams['char_limit'])) {
            $key->setCharLimit($keyParams['char_limit']);
        }
    }

    private function createTranslation(string $translationValue): Translation
    {
        $translation = new Translation();
        if ($this->getConfiguration()->getLanguageId()) {
            $translation->setLanguageId($this->getConfiguration()->getLanguageId());
        }
        $translation->setTranslation($translationValue);

        return $translation;
    }

    private function setKeyDescription(Key $key, array &$comments): void
    {
        $description = array_shift($comments);
        $key->setDescription($description);
    }

    public function toLegacyArray(Key $key, ?int $languageId): array
    {
        if ($languageId !== null) {
            $translation = $key->getTranslationByLanguage($languageId);
        } else {
            $translationsArray = $key->getTranslations();
            $translation = reset($translationsArray);
        }

        $translationValue = $translation ? $translation->getTranslation() : null;
        $isProofread = $translation ? $translation->isProofread() : null;

        $attributes = !empty($key->getAttributes()) ? json_encode($key->getAttributes()) : null;

        $comments = [];
        if ($description = $key->getDescription()) {
            $comments[] = $description;
        }
        $comments = array_merge($comments, $key->getComments());

        return [
            'key' => $key->getKey(),
            'plural' => $key->getPluralName(),
            'translation' => $translationValue,
            'context' => $key->getContext(),
            'fix_plural' => false,
            'custom_attributes' => $attributes,
            'comments' => json_encode($comments),
            'fuzzy' => $translation ? $translation->isFuzzy() : false,
            'char_limit' => $key->getCharLimit(),
            'key_hash' => $this->getKeyHash($key->getKey(), $key->getContext()),
            'is_proofread' => $isProofread,
            'import_lang_id' => $this->importLanguageId,
        ];
    }

    /**
     * @return ImportXliffConfiguration
     *
     * @noinspection SenselessMethodDuplicationInspection
     */
    public function getConfiguration(): ImportConfigurationInterface
    {
        return $this->configuration;
    }

    public function getImportDevice(): ?int
    {
        if ($this->getConfiguration()->getFormat() == FileFormat::AppleXliff) {
            return D_IOS;
        }

        return D_WEB;
    }

    private function isOfflineXliff(): bool
    {
        return $this->getConfiguration()->getFormat() == FileFormat::OfflineXliff;
    }

    private function replacePlaceholdersWithValues(string &$string): void
    {
        $string = str_replace(
            array_keys(self::PLACEHOLDER_REPLACEMENTS),
            array_values(self::PLACEHOLDER_REPLACEMENTS),
            $string
        );
    }

    private function replaceValuesWithPlaceholders(string &$string): void
    {
        $string = str_replace(
            array_values(self::PLACEHOLDER_REPLACEMENTS),
            array_keys(self::PLACEHOLDER_REPLACEMENTS),
            $string
        );
    }

    /**
     * @throws FileParseException
     */
    private function checkLibXMLErrors(): void
    {
        foreach (libxml_get_errors() as $libXMLError) {
            if (
                $libXMLError->level === LIBXML_ERR_ERROR
                || $libXMLError->level === LIBXML_ERR_FATAL
            ) {
                throw new FileParseException("Parsing file error at line {$libXMLError->line}: {$libXMLError->message}");
            }
        }
    }

    /**
     * @throws FileParseException
     */
    private function initOfflineXliffLanguageData(): void
    {
        if (!$this->isOfflineXliff()) {
            return;
        }

        $files = $this->dom->getElementsByTagName('file');
        /**
         * @var DOMElement $fileElement
         */
        $fileElement = $files->item(0);

        $sourceLanguageIso = strtolower($fileElement->getAttribute('source-language'));
        $sourceLanguageIsoUnderscore = str_replace('-', '_', $sourceLanguageIso);

        $targetLanguageIso = strtolower($fileElement->getAttribute('target-language'));
        $targetLanguageIsoUnderscore = str_replace('-', '_', $targetLanguageIso);

        $projectLanguages = $this->languageService->getProjectLangIsos($this->getConfiguration()->getProjectId());

        foreach ($projectLanguages as $projectLanguage) {
            $projectLanguageIso = strtolower($projectLanguage['language_iso']);

            if (
                $projectLanguageIso === $sourceLanguageIsoUnderscore
                || $projectLanguageIso === $sourceLanguageIso
            ) {
                $this->sourceLanguageId = (int) $projectLanguage['lang_id'];
            }

            if (
                $projectLanguageIso === $targetLanguageIsoUnderscore
                || $projectLanguageIso === $targetLanguageIso
            ) {
                $this->targetLanguageId = (int) $projectLanguage['lang_id'];
            }
        }

        if (!$this->targetLanguageId) {
            throw new FileParseException("{$targetLanguageIso}: Invalid target language");
        }

        if (!$this->sourceLanguageId && $this->getConfiguration()->getTaskId()) {
            throw new FileParseException("{$sourceLanguageIso}: Invalid source language");
        }
    }

    /**
     * @throws FileParseException
     */
    private function process(): void
    {
        $files = $this->dom->getElementsByTagName('file');
        foreach ($files as $file) {
            $this->processLanguages($file);
            $this->processFileElement($file);
        }
    }

    /**
     * @throws FileParseException
     */
    private function processLanguages(DOMElement $file): void
    {
        $sourceLanguage = $file->getAttribute('source-language') ?: null;
        $targetLanguage = $file->getAttribute('target-language') ?: null;

        if (!$sourceLanguage && !$targetLanguage && !$this->getConfiguration()->isPreprocess()) {
            throw new FileParseException('No language found in document.');
        }

        $allIsoVariations = $this->languageService->getAllIsosVariations($this->getConfiguration()->getProjectId());

        if ($this->getConfiguration()->isPreprocess()) { // autodetect language
            if ($sourceLanguage) {
                $this->sourceLanguageId = $allIsoVariations[$sourceLanguage]
                    ? (int) $allIsoVariations[$sourceLanguage] : (int) $this->languageService->findLanguageByIso($sourceLanguage);
                $this->importLanguageId = $this->sourceLanguageId;
            }
            if ($targetLanguage) {
                $this->targetLanguageId = $allIsoVariations[$targetLanguage]
                    ? (int) $allIsoVariations[$targetLanguage] : (int) $this->languageService->findLanguageByIso($targetLanguage);
                $this->importLanguageId = $this->targetLanguageId;
            }
            // force language
        } elseif (!$sourceLanguage) {
            $this->targetLanguageId = $this->getConfiguration()->getTargetLanguageId();
        } elseif (!$targetLanguage) {
            $this->sourceLanguageId = $this->getConfiguration()->getTargetLanguageId();
        } else {
            $this->sourceLanguageId = $allIsoVariations[$sourceLanguage] ? (int) $allIsoVariations[$sourceLanguage] : null;
            $this->targetLanguageId = $this->getConfiguration()->getTargetLanguageId();
        }
    }

    /**
     * @throws FileParseException
     */
    private function processFileElement(DOMElement $file): void
    {
        $originalFileName = $file->getAttribute('original');

        foreach ($file->childNodes as $node) {
            if ($node->nodeName === 'body') {
                $this->processElementRecursively(
                    $node,
                    $originalFileName
                );
            }
        }
    }

    /**
     * Searches for trans-elements in the given node and processes them
     *
     * @param DOMElement[] $ancestors
     *
     * @throws FileParseException
     */
    private function processElementRecursively(
        DOMElement $element,
        ?string $originalFileName,
        array $ancestors = []
    ): void {
        foreach ($element->childNodes as $child) {
            if ($child->nodeName === 'group') {
                if ($child->getAttribute('id') === '') {
                    $child->setAttribute('id', uniqid());
                }

                $this->processElementRecursively(
                    $child,
                    $originalFileName,
                    array_merge([$child], $ancestors)
                );
            }

            if ($child->nodeName === 'trans-unit') {
                $parent = null;

                if ($this->getConfiguration()->userCanManageKeys()) {
                    $parent = KeyAncestorHelper::buildAncestorsTree($ancestors);
                }

                try {
                    $this->processTransUnit($child, $originalFileName, $parent);
                } catch (Throwable $e) {
                    throw new FileParseException($e->getMessage(), 0, $e);
                }
            }
        }
    }

    /**
     * @throws DOMException|FileParseException|JsonException
     */
    private function processTransUnit(
        DOMElement $transUnit,
        string $originalFileName,
        ?stdClass $transUnitParent
    ): void {
        /*
         * context-groups each element - [ 'purpose' => '', items => [] ]
         *
         * <context-group purpose="location">
         *   <context context-type="sourcefile">app/app.component.ts</context>
         *   <context context-type="x-foo">bar</context>
         * </context-group>
         * notes each element - ['priority' => 1..10, 'from' => '', value => '']
         */
        $id = $transUnit->getAttribute('id');

        if ($this->isOfflineXliff() && !preg_match(self::OFFLINE_XLIFF_ID_PATTERN, $id)) {
            return;
        }

        $this->replacePlaceholdersWithValues($id);

        $this->replacePlaceholdersWithValues($originalFileName);
        $originalFileName = htmlspecialchars_decode($originalFileName);

        $appleStringsDictPluralKey = AppleStringsDictHelper::getPluralKeyFormat($id);

        if ($appleStringsDictPluralKey['type'] === 'format') {
            $id = $appleStringsDictPluralKey['key'];
        }

        if ($appleStringsDictPluralKey['type'] === 'translation') {
            $id = $appleStringsDictPluralKey['key'] . '::' . $appleStringsDictPluralKey['format_key'];
        }

        $keyHash = $this->getKeyHash($id, $originalFileName);

        $key = $this->keys[$keyHash] ?? [
            'key' => $id,
            'plural' => $appleStringsDictPluralKey['type'] === 'translation' ? $id : null,
            'translation' => $appleStringsDictPluralKey !== null ? json_encode([], JSON_THROW_ON_ERROR) : null,
            'context' => $originalFileName,
            'comments' => [],
            'custom_attributes' => [],
            'translationChanged' => false,
            'transUnitApproved' => strtolower($transUnit->getAttribute('approved')) === 'yes',
            'sourceMismatch' => null,
            'targetState' => '',
        ];

        if (!$this->isOfflineXliff() && $this->getConfiguration()->userCanManageKeys()) {
            if ($transUnitParent) {
                $key['custom_attributes']['parent'] = $transUnitParent;
            }

            if ($transUnit->getAttribute('datatype')) {
                $key['custom_attributes']['datatype'] = $transUnit->getAttribute('datatype');
            }

            $this->processMaxWidthAttribute($transUnit, $key);
        }

        if (!isset($this->keysTranslations[$keyHash])) {
            $this->keysTranslations[$keyHash] = [];
        }

        $sourceNode = $transUnit->getElementsByTagName('source')[0] ?? null;
        $targetNode = $transUnit->getElementsByTagName('target')[0] ?? null;

        if (!($sourceNode instanceof DOMElement)) {
            throw new FileParseException("trans-unit {$id} is missing source node");
        }

        if (!($targetNode instanceof DOMElement)) {
            $targetNode = $this->dom->createElement('target');

            $transUnit->appendChild($targetNode);
        }

        $this->processSourceNode($sourceNode, $keyHash, $key, $appleStringsDictPluralKey);
        $this->processTargetNode($targetNode, $keyHash, $key, $appleStringsDictPluralKey);

        if ($key['translationChanged']) {
            // If target state is not set, clear reviewed and fuzzy flags
            $key['is_proofread'] = false;
            $key['fuzzy'] = false;
            $this->setProofreadFlag($key);
            $this->setFuzzyFlag($key);
        }

        $this->processTransUnitChildren($key, $transUnit);

        $this->keys[$keyHash] = $key;
    }

    private function getKeyHash(string $keyName, string $keyContext): string
    {
        return implode('', [md5($keyName), '-', md5($keyContext)]);
    }

    /**
     * @throws JsonException
     */
    private function processTranslation(string &$value, array &$key): string
    {
        if (is_json($value) && seems_like_plural(json_decode($value, true, 512, JSON_THROW_ON_ERROR))) {
            $value = json_decode($value, true, 512, JSON_THROW_ON_ERROR);
            $key['plural'] = $key['key'];

            return json_encode($value, JSON_THROW_ON_ERROR);
        }

        if ($this->getConfiguration()->isDetectIcu() && $pluralData = ICUMessageUtils::getICUPluralArray($value)) {
            // original should be original string not ICU processed array
            // store original before processing
            $key['icu_original'] = $value;
            $key['plural'] = $pluralData['name'];

            return json_encode($pluralData['plurals'], JSON_THROW_ON_ERROR);
        }

        return $value;
    }

    private function processMaxWidthAttribute(DOMElement $transUnit, array &$key): void
    {
        $maxWidth = (int) $transUnit->getAttribute('maxwidth');
        $sizeUnit = $transUnit->getAttribute('size-unit');

        if ($sizeUnit !== 'char') {
            return;
        }

        if ($maxWidth === 0) {
            $key['char_limit'] = null;

            return;
        }

        $key['char_limit'] = $maxWidth;
    }

    /**
     * @throws JsonException
     */
    private function processSourceNode(
        DOMElement $source,
        string $keyHash,
        array &$key,
        ?array $appleStringsDictPluralKey
    ): void {
        if (!$this->sourceLanguageId) {
            return;
        }

        $value = $source->nodeValue;
        $this->replacePlaceholdersWithValues($value);
        $value = $this->htmlSpecialCharsDecodeSavingXTags($value);

        if ($this->isOfflineXliff()) {
            $key['source'] = $value;
        }

        $keyId = str_replace(self::TRANS_UNIT_ID_PREFIX, '', $key['key']);

        $lokaliseSourceValue = $this->translationService->getByKeyIdsAndLangId(
            [$keyId],
            $this->sourceLanguageId
        )[0]['translation'] ?? null;

        if ($lokaliseSourceValue !== null && $value !== $lokaliseSourceValue) {
            $key['sourceMismatch'] = true;
        }

        if ($appleStringsDictPluralKey['type'] === 'translation') {
            if (!isset($this->keysTranslations[$keyHash][$this->sourceLanguageId])) {
                $this->keysTranslations[$keyHash][$this->sourceLanguageId] = [
                    'translation' => json_encode([], JSON_THROW_ON_ERROR),
                ];
            }

            $translation = json_decode(
                $this->keysTranslations[$keyHash][$this->sourceLanguageId]['translation'],
                true,
                512,
                JSON_THROW_ON_ERROR
            );
            $translation[$appleStringsDictPluralKey['plural_key']] = $value;
            $key['translation'] = json_encode($translation, JSON_THROW_ON_ERROR);
            $this->keysTranslations[$keyHash][$this->sourceLanguageId] = [
                'translation' => $key['translation'],
            ];

            return;
        }

        $translation = $this->processTranslation($value, $key);
        $key['translation'] = $translation;
        $this->keysTranslations[$keyHash][$this->sourceLanguageId] = [
            'translation' => $translation,
        ];
    }

    /**
     * @throws JsonException
     */
    private function processTargetNode(
        DOMElement $target,
        string $keyHash,
        array &$key,
        ?array $appleStringsDictPluralKey
    ): void {
        if (
            !$this->targetLanguageId
            || ($this->targetLanguageId === $this->sourceLanguageId && !$this->isOfflineXliff())
        ) {
            return;
        }

        $value = $target->nodeValue;
        $this->replacePlaceholdersWithValues($value);
        $value = $this->htmlSpecialCharsDecodeSavingXTags($value);
        $keyId = str_replace(self::TRANS_UNIT_ID_PREFIX, '', $key['key']);

        $lokaliseTranslationValue = $this->translationService->getByKeyIdsAndLangId(
            [$keyId],
            $this->targetLanguageId
        )[0]['translation'] ?? '';

        if ($lokaliseTranslationValue !== $value) {
            $key['translationChanged'] = true;
        }

        $key['targetState'] = strtolower($target->getAttribute('state'));

        if ($appleStringsDictPluralKey['type'] === 'translation') {
            if (!isset($this->keysTranslations[$keyHash][$this->targetLanguageId])) {
                $this->keysTranslations[$keyHash][$this->targetLanguageId] = [
                    'translation' => json_encode([], JSON_THROW_ON_ERROR),
                ];
            }

            $translation = json_decode(
                $this->keysTranslations[$keyHash][$this->targetLanguageId]['translation'],
                true,
                512,
                JSON_THROW_ON_ERROR
            );
            $translation[$appleStringsDictPluralKey['plural_key']] = $value;
            $key['translation'] = json_encode($translation, JSON_THROW_ON_ERROR);
            $this->keysTranslations[$keyHash][$this->targetLanguageId] = [
                'translation' => $key['translation'],
                'is_proofread' => in_array($key['targetState'], self::POSITIVE_PROOFREAD_STATES),
            ];

            return;
        }

        $translation = $this->processTranslation($value, $key);
        $key['translation'] = $translation;
        $this->keysTranslations[$keyHash][$this->targetLanguageId] = [
            'translation' => $translation,
            'is_proofread' => in_array($key['targetState'], self::POSITIVE_PROOFREAD_STATES),
        ];
    }

    private function htmlSpecialCharsDecodeSavingXTags(string $value): string
    {
        if ($this->getConfiguration()->getFormat() != FileFormat::AngularXliff) {
            return htmlspecialchars_decode($value, ENT_NOQUOTES);
        }

        preg_match_all('/<\/?(?!\d)x(?:\s+[^\s>\/=]+(?:=(?:("|\')(?:\\\[\s\S]|(?!\1)[^\\\])*\1|[^\s\'">=]+))?)*\s*\/?>/i', $value, $matches);

        if (empty($matches[0])) {
            return htmlspecialchars_decode($value, ENT_NOQUOTES);
        }

        $replaceMap = [];
        foreach ($matches[0] as $i => $match) {
            $replaceMap['___LOKALISE_' . $i . '_'] = $match;
        }

        $value = str_replace(
            array_values($replaceMap),
            array_keys($replaceMap),
            $value
        );

        $value = htmlspecialchars_decode($value, ENT_NOQUOTES);

        return str_replace(
            array_keys($replaceMap),
            array_values($replaceMap),
            $value
        );
    }

    private function setProofreadFlag(array &$key): void
    {
        if (!$key['transUnitApproved'] && $key['targetState'] === '') {
            return;
        }

        $key['is_proofread'] = $key['transUnitApproved']
            || $key['targetState'] === 'final'
            || $key['targetState'] === 'signed-off';
    }

    /**
     * This relies on is_proofread flag being correctly set,
     * so it needs to be called AFTER setProofreadFlag function
     */
    private function setFuzzyFlag(array &$key): void
    {
        if ($key['sourceMismatch']) {
            $key['fuzzy'] = true;

            return;
        }

        $fuzzyStates = [
            'needs-translation',
            'needs-adaptation',
            'needs-l10n',
            'needs-review-adaptation',
            'needs-review-l10n',
            'needs-review-translation',
        ];

        $key['fuzzy'] = in_array($key['targetState'], $fuzzyStates);
    }

    private function processTransUnitChildren(array &$key, DOMElement $transUnit): void
    {
        if ($this->isOfflineXliff() || !$this->getConfiguration()->userCanManageKeys()) {
            return;
        }

        foreach ($transUnit->childNodes as $node) {
            switch ($node->nodeName) {
                case 'context-group':
                    $group = $this->processContextGroup($node);

                    if (!empty($group)) {
                        if (!$key['custom_attributes']['context-groups']) {
                            $key['custom_attributes']['context-groups'] = [];
                        }
                        $key['custom_attributes']['context-groups'][] = $group;
                    }

                    break;
                case 'note':
                    $note = $this->processNote($node);

                    if (!isset($key['custom_attributes']['notes'])) {
                        $key['custom_attributes']['notes'] = [];
                    }

                    $this->replacePlaceholdersWithValues($note['value']);

                    if ($note['value'] && $note['value'] !== 'No comment provided by engineer.') {
                        $key['custom_attributes']['notes'][] = $note;
                        $key['comments'][] = $note['value'];
                    }

                    break;
            }
        }
    }

    private function processContextGroup(DOMElement $group): array
    {
        $data = [
            'contexts' => [],
        ];
        if ($group->getAttribute('purpose')) {
            $data['purpose'] = $group->getAttribute('purpose');
        }
        foreach ($group->childNodes as $node) {
            if ($node->nodeName === 'context' && $node->nodeValue && $node->getAttribute('context-type')) {
                $data['contexts'][] = [
                    'context-type' => $node->getAttribute('context-type'),
                    'value' => $node->nodeValue,
                ];
            }
        }

        return !empty($data['contexts']) ? $data : [];
    }

    private function processNote(DOMElement $note): array
    {
        $data = [
            'value' => $note->nodeValue,
        ];

        $priority = (int) $note->getAttribute('priority');
        if ($priority >= 1 && $priority <= 10) {
            $data['priority'] = $priority;
        }

        $from = $note->getAttribute('from');
        if ($from) {
            $data['from'] = $from;
        }

        return $data;
    }

    public function getLanguageIds(): array
    {
        $languageIds = [];
        if ($this->sourceLanguageId) {
            $languageIds['source'] = $this->sourceLanguageId;
        }
        if ($this->targetLanguageId) {
            $languageIds['target'] = $this->targetLanguageId;
        }

        return $languageIds;
    }

    public function getImportLanguage(): ?int
    {
        return $this->importLanguageId;
    }

    public function getKeysTranslations(): array
    {
        return $this->keysTranslations;
    }

    public function hasBaseLanguage(): bool
    {
        return in_array($this->getConfiguration()->getBaseLangId(), $this->getLanguageIds());
    }

    public function getTargetLanguageId(): ?int
    {
        return $this->targetLanguageId;
    }

    public function getSourceLanguageId(): ?int
    {
        return $this->sourceLanguageId;
    }

    public function initialize(ImportConfigurationInterface $configuration): void
    {
        parent::initialize($configuration);
        $this->sourceLanguageId = null;
        $this->importLanguageId = null;
        $this->targetLanguageId = null;
        $this->keys = [];
        $this->keysTranslations = [];
    }
}
