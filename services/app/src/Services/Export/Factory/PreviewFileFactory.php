<?php

declare(strict_types=1);

namespace Lokalise\Services\Export\Factory;

use Lokalise\Services\Export\ValueObject\PreviewFile;

class PreviewFileFactory
{
    /**
     * @return PreviewFile[]
     */
    public static function buildFromFilesArray(array $files): array
    {
        $previewFiles = [];

        foreach ($files as $filesPerLang) {
            foreach ($filesPerLang as $name => $fileData) {
                $previewFiles[] = new PreviewFile($name, $fileData['filename'], $fileData['content']);
            }
        }

        return $previewFiles;
    }
}
