<?php

declare(strict_types=1);

namespace Lokalise\Glossary\Transformer;

use DateTime;
use DateTimeZone;
use LanguageService;
use Lokalise\Glossary\Dto\TermTag as TermDto;

final readonly class Term
{
    public const string TIME_ZONE = 'Etc/UTC';
    public const string DATE_TIME_FORMAT = 'Y-m-d H:i:s (e)';

    public function __construct(private LanguageService $languageService)
    {
    }

    /**
     * @return array<string, mixed>
     */
    public function transform(TermDto $term): array
    {
        $definitions = json_decode($term->definitions, true) ?? [];
        $descriptions = json_decode($term->descriptions, true) ?? [];
        $languages = $this->languageService->getNamesAndIsoByIds(array_keys($definitions));
        $translations = [];
        $tags = array_map(static fn ($tag) => $tag->value, $term->tags ?? []);
        foreach ($languages as $language) {
            $langId = $language['id'];
            $translations[] = [
                'langId' => (int) $langId,
                'langName'  => (string) $language['name'],
                'langIso' => (string) $language['lang_iso'],
                'translation' => (string) $definitions[$langId],
                'description' => (string) $descriptions[$langId],
            ];
        }

        return [
            'id' => $term->id,
            'term' => $term->term,
            'description' => $term->description,
            'caseSensitive' => $term->caseSensitive,
            'translatable' => $term->translatable,
            'forbidden' => $term->forbidden,
            'translations' => $translations,
            'tags' => $tags,
            'projectId' => $term->projectId,
            'createdAt' => $this->getFormattedDateStr($term->createdAt),
            'updatedAt' => $this->getFormattedDateStr($term->updatedAt),
        ];
    }

    private function getFormattedDateStr(?string $dateStr): ?string
    {
        if (null === $dateStr) {
            return null;
        }

        $date = DateTime::createFromFormat('Y-m-d H:i:s', $dateStr);
        if ($date === false) {
            return $dateStr;
        }

        $date->setTimezone(new DateTimeZone(self::TIME_ZONE));

        return $date->format(self::DATE_TIME_FORMAT);
    }
}
