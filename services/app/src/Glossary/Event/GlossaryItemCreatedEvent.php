<?php

namespace Lokalise\Glossary\Event;

use Lokalise\Events\Project\AbstractProjectAuditEvent;
use Lokalise\Events\Project\ProjectEventTypeMap;

class GlossaryItemCreatedEvent extends AbstractProjectAuditEvent
{
    public const EVENT_NAME = 'project.glossary.item.created';

    public const EVENT_TYPE_ID = ProjectEventTypeMap::TYPE_MAP[self::EVENT_NAME];

    /**
     * @var int
     */
    private $termId;

    /**
     * @var array
     */
    private $originalData;

    /**
     * @var bool
     */
    private $isFromUploadedCsv;

    public function __construct(
        string $projectId,
        int $termId,
        array $originalData,
        bool $isFromUploadedCsv = false
    ) {
        parent::__construct(
            $projectId,
            null,
            false
        );
        $this->termId = $termId;
        $this->originalData = $originalData;
        $this->isFromUploadedCsv = $isFromUploadedCsv;
    }

    public function getAuditData(): array
    {
        return [
            'term_id' => $this->termId,
            'original_data' => $this->originalData,
            'is_from_csv' => $this->isFromUploadedCsv,
        ];
    }
}
