<?php

declare(strict_types=1);

namespace Lokalise\Security\Jwt;

use Lokalise\Security\Jwt\Dto\CommonTokenPayload;
use Lokalise\Services\Jwt\Payload\JwtAuthenticationInterface;
use Lokalise\Services\Jwt\Payload\JwtTokenInterface;
use Lokalise\ViewerUser\Model\UserId;
use RuntimeException;
use Symfony\Component\Uid\Uuid;
use Throwable;

class CommonToken implements JwtTokenInterface, JwtAuthenticationInterface
{
    public function __construct(
        private readonly UserId $userId,
        private readonly string $userUuid,
        private readonly string $userName,
        private readonly int $teamId,
        private readonly Uuid $teamUuid,
        private readonly string $userTeamRole,
        private readonly string $userEmail,
        private readonly int $userCurrentTeamId,
        private readonly int $planId,
        private readonly string $planName,
        private readonly bool $isProviderAlpha,
        private readonly bool $isFullyAuthenticated,
    ) {
    }

    public function getPayload(): CommonTokenPayload
    {
        return new CommonTokenPayload(
            userId: $this->userId->getValue(),
            userUuid: $this->userUuid,
            userName: $this->userName,
            teamId: $this->teamId,
            teamUuid: $this->teamUuid->toRfc4122(),
            userTeamRole: $this->userTeamRole,
            userEmail: $this->userEmail,
            userCurrentTeamId: $this->userCurrentTeamId,
            planId: $this->planId,
            planName: $this->planName,
            isProviderAlpha: $this->isProviderAlpha,
            isFullyAuthenticated: $this->isFullyAuthenticated,
        );
    }

    /**
     * @param object{
     *     userId: mixed,
     *     userUuid: string,
     *     userName: string,
     *     teamId: int,
     *     teamUuid: string,
     *     userTeamRole: string,
     *     userEmail: string,
     *     userCurrentTeamId: int,
     *     planId: int,
     *     planName: string,
     *     isProviderAlpha: bool,
     *     isFullyAuthenticated: bool
     * } $payload
     */
    public static function createFromPayload(object $payload): JwtTokenInterface
    {
        try {
            return new self(
                userId: new UserId($payload->userId),
                userUuid: $payload->userUuid,
                userName: $payload->userName,
                teamId: $payload->teamId,
                teamUuid: Uuid::fromString($payload->teamUuid),
                userTeamRole: $payload->userTeamRole,
                userEmail: $payload->userEmail,
                userCurrentTeamId: $payload->userCurrentTeamId,
                planId: $payload->planId,
                planName: $payload->planName,
                isProviderAlpha: $payload->isProviderAlpha,
                isFullyAuthenticated: $payload->isFullyAuthenticated,
            );
        } catch (Throwable $e) {
            throw new RuntimeException('Failed to create CommonToken from payload', 0, $e);
        }
    }

    public function getUserId(): UserId
    {
        return $this->userId;
    }

    public function getTeamId(): int
    {
        return $this->teamId;
    }

    public function getTeamUuid(): Uuid
    {
        return $this->teamUuid;
    }

    public function getUserTeamRole(): string
    {
        return $this->userTeamRole;
    }

    public function getUserEmail(): string
    {
        return $this->userEmail;
    }

    public function getUserCurrentTeamId(): int
    {
        return $this->userCurrentTeamId;
    }

    public function getPlanId(): int
    {
        return $this->planId;
    }

    public function getPlanName(): string
    {
        return $this->planName;
    }

    public function isProviderAlpha(): bool
    {
        return $this->isProviderAlpha;
    }

    public function getUserUuid(): string
    {
        return $this->userUuid;
    }

    public function isFullyAuthenticated(): bool
    {
        return $this->isFullyAuthenticated;
    }

    public function getUserName(): string
    {
        return $this->userName;
    }
}
