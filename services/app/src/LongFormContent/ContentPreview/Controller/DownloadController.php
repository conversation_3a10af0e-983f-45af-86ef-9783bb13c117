<?php

declare(strict_types=1);

namespace Lokalise\LongFormContent\ContentPreview\Controller;

use Lokalise\Client\Permission\PermissionClient;
use Lokalise\Common\Controller\BaseController;
use Lokalise\Common\Permission\Dto\Request\PermissionAccessRequest;
use Lokalise\Common\Permission\Enum\PermissionAction;
use Lokalise\Common\Permission\Enum\PermissionResourceType;
use Lokalise\Common\Permission\Enum\PermissionScopeType;
use Lokalise\Common\Project\ValueObject\ProjectId;
use Lokalise\Common\User\ValueObject\UserId;
use Lokalise\Factory\ProjectDataFactory;
use Lokalise\LongFormContent\ContentPreview\Exception\FileNotFoundException;
use Lokalise\LongFormContent\ContentPreview\Service\DownloadService;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Routing\Annotation\Route;

class DownloadController extends BaseController
{
    #[Route(path: '/projects/{projectId}/files/{fileName}/download', name: 'file_preview_download', methods: ['GET'])]
    public function downloadTargetFile(
        ProjectId $projectId,
        string $fileName,
        ProjectDataFactory $projectDataFactory,
        Request $request,
        DownloadService $downloadService,
        PermissionClient $permissionClient,
    ): Response {
        $langId = $request->query->getInt('langId');

        if ($langId === 0) {
            throw new BadRequestException('Missing required parameter "langId".');
        }

        $permissionClient->checkAccess(
            new PermissionAccessRequest(
                userId: UserId::fromSafe((string) $this->getCurrentUserId()),
                scopeType: PermissionScopeType::PROJECT,
                scopeId: $projectId->value(),
                resourceType: PermissionResourceType::FILE,
                action: PermissionAction::READ,
            )
        );

        $projectData = $projectDataFactory->createFromSymfonyRequest($request);

        try {
            $previewFile = $downloadService->exportFileAndGetPath($projectId, $fileName, $langId, $projectData);
        } catch (FileNotFoundException $downloadException) {
            throw $this->createNotFoundException($downloadException->getMessage(), previous: $downloadException);
        }

        $binaryFileResponse = new BinaryFileResponse($previewFile->path);
        $binaryFileResponse->deleteFileAfterSend();
        $binaryFileResponse->setContentDisposition(ResponseHeaderBag::DISPOSITION_ATTACHMENT, $previewFile->filename);

        return $binaryFileResponse;
    }
}
