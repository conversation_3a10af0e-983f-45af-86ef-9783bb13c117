<?php

declare(strict_types=1);

namespace Lokalise\LongFormContent\FilesManagement\Model;

use JsonSerializable;
use Lokalise\Common\ValueObjectTools\IntValueObjectTrait;

class FileId implements JsonSerializable
{
    use IntValueObjectTrait;

    public function __construct(int $value)
    {
        $this->value = $value;
    }

    public function jsonSerialize(): int
    {
        return $this->getValue();
    }
}
