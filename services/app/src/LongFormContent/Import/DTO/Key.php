<?php

declare(strict_types=1);

namespace Lokalise\LongFormContent\Import\DTO;

final class Key
{
    public function __construct(
        private readonly int $index,
        private readonly string $translation,
        private readonly string $prefix = '',
        private readonly string $suffix = '',
        private readonly ?string $context = null
    ) {
    }

    public function getIndex(): int
    {
        return $this->index;
    }

    public function getTranslation(): string
    {
        return $this->translation;
    }

    public function getPrefix(): string
    {
        return $this->prefix;
    }

    public function getSuffix(): string
    {
        return $this->suffix;
    }

    public function getContext(): ?string
    {
        return $this->context;
    }

    public function toArray(): array
    {
        return [
            'index' => $this->getIndex(),
            'translation' => $this->getTranslation(),
            'prefix' => $this->getPrefix(),
            'suffix' => $this->getSuffix(),
            'context' => $this->getContext(),
        ];
    }
}
