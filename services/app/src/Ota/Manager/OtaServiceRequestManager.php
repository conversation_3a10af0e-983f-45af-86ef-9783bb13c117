<?php

declare(strict_types=1);

namespace Lokalise\Ota\Manager;

use DateTimeImmutable;
use Lokalise\Ota\Dto\ServiceStatisticsDateItemDto;
use Lokalise\Ota\Dto\TeamDto;
use Lokalise\Ota\Services\OtaClientInterface;
use Lokalise\Services\Jwt\Enums\JwtTokenAction;
use Lokalise\Services\Jwt\Payload\ServiceOtaToken;
use Symfony\Component\Serializer\Normalizer\UnwrappingDenormalizer;
use Symfony\Component\Serializer\SerializerInterface;
use Throwable;

class OtaServiceRequestManager
{
    public function __construct(private readonly OtaClientInterface $otaClient, private readonly SerializerInterface $serializer)
    {
    }

    /**
     * @throws Throwable
     */
    public function sendCreateTeamRequest(int $teamId, bool $enabled = false): TeamDto
    {
        $token = new ServiceOtaToken($teamId, JwtTokenAction::CreateTeam);

        return $this->deserializeTeamData(
            $this->otaClient->sendServiceRequest(
                'post',
                '/v3/private/service/teams',
                $token,
                $this->serialize((object) ['externalId' => $teamId, 'isEnabled' => $enabled])
            )
        );
    }

    /**
     * @throws Throwable
     */
    public function sendDisableTeamRequest(int $teamId): TeamDto
    {
        $token = new ServiceOtaToken($teamId, JwtTokenAction::DisableOta);

        return $this->deserializeTeamData(
            $this->otaClient->sendServiceRequest(
                'put',
                '/v3/private/service/teams/' . $teamId,
                $token,
                $this->serialize((object) ['isEnabled' => false])
            )
        );
    }

    /**
     * @throws Throwable
     */
    public function sendEnableTeamRequest(int $teamId): TeamDto
    {
        $token = new ServiceOtaToken($teamId, JwtTokenAction::EnableOta);

        return $this->deserializeTeamData(
            $this->otaClient->sendServiceRequest(
                'put',
                '/v3/private/service/teams/' . $teamId,
                $token,
                $this->serialize((object) ['isEnabled' => true])
            )
        );
    }

    public function sendGetServiceMonthStatisticsRequest(DateTimeImmutable $lastDayOfMonth): ?ServiceStatisticsDateItemDto
    {
        $lastDayOfMonthString = $lastDayOfMonth->format('Y-m-d');
        $token = new ServiceOtaToken(null, JwtTokenAction::GetStatistics);

        $responsePayload = $this->otaClient->sendServiceRequest(
            'get',
            "/v3/private/service/statistics?dateFrom={$lastDayOfMonthString}&dateTo={$lastDayOfMonthString}",
            $token
        );

        return $this->serializer->deserialize(
            $responsePayload,
            ServiceStatisticsDateItemDto::class,
            'json'
        );
    }

    private function serialize(object $dataObject): string
    {
        return $this->serializer->serialize($dataObject, 'json');
    }

    private function deserializeTeamData($responseData): TeamDto
    {
        if ($responseData === null) {
            return new TeamDto();
        }

        return $this->serializer->deserialize($responseData, TeamDto::class, 'json', [UnwrappingDenormalizer::UNWRAP_PATH => '[data]']);
    }
}
