{"index.hero.title": "Translate your apps and web the modern way", "index.hero.subtitle": "Automating localization and translation workflow for software developers.", "index.hero.feature1.title": "1. Upload language files", "index.hero.feature1.subtitle": "We support all popular <strong>iOS</strong>, <strong>Android</strong> and <strong>web</strong> localization file formats.", "index.hero.feature2.title": "2. Get translated", "index.hero.feature2.subtitle": "<strong>Invite your team</strong> to work on translations or use <strong>professional translation services</strong> by our partners.", "index.hero.feature3.title": "3. Integrate", "index.hero.feature3.subtitle": "Use Lokalise <strong><a href=\"http://docs.lokalise.co/category/fg1V2PPSjI-ios-android-sdk\">iOS and Android SDK</a></strong> or <strong><a target=\"_blank\" href=\"/apidocs\">API</a></strong> to integrate or simply <strong>download</strong> the files.", "index.get_started.placeholder": "Enter your e-mail", "index.get_started.button.text": "TRY IT", "index.get_started.button.text.2": "GET STARTED", "index.get_started.book.demo": "BOOK A DEMO", "index.get_started.subtitle": "Easy set-up • Free 14-day full trial • Free plan available", "index.happy_customers": "Tons of time saved for people in", "index.how_it_works": "See how it works in 2 minutes", "features.f1.mobile_sdk.title": "iOS and Android SDK", "features.f1.api.title": "Advanced API", "features.f1.multiplatform.title": " Multiplatform", "features.f1.referencing.title": "Key referencing", "features.f1.placeholders.title": "Placeholder/HTML validation", "features.f1.custom_iso.title": "Custom ISO codes", "features.f1.screenshots.title": "Screenshots", "features.f1.plurals.title": "Plurals", "features.f1.tags.title": "Tags", "features.f1.snapshots.title": "Project snapshots", "features.f1.mobile_sdk.subtitle": "Play with how your translations look on iPhone or Android screens pre-release first. When ready, instantly push to all the devices with a single click. <a target=_new  href=\"http://docs.lokalise.co/category/fg1V2PPSjI-ios-android-sdk\">Documentation</a>.", "features.f1.api.subtitle": "Integrate with your existing setup using our <a target=_new href=\"https://lokalise.co/apidocs\">API</a> or <a target=_new href=\"http://docs.lokalise.co/article/44l4f1hiZM-lokalise-cli-tool\">command-line tool</a>.", "features.f1.multiplatform.subtitle": "In Lokalise you can keep all platform keys in the same project without redundancy, use universal placeholders and export to different platforms when needed. Lokalise may also merge similar keys during import.", "features.f1.referencing.subtitle": "Link keys with similar translations using special Lokalise <a target=_new href=\"http://docs.lokalise.co/article/KO5SZWLLsy-key-referencing\">placeholders</a> and save translators some time. ", "features.f1.custom_iso.subtitle": "Configure custom ISO per each file format you are exporting to, or even change the language itself if needed.", "features.f1.placeholders.subtitle": "We care about your code consistency and not just highlight the placeholders and HTML in your translations, but also notify you if some get lost in translation.", "features.f1.screenshots.subtitle": "Upload screenshots with our SDK or simply drag and drop image files. Lokalise automatically recognizes the texts on images and matches against the keys containing these texts.", "features.f1.plurals.subtitle": "Complete support for keys with plural forms. Add, delete or modify plural forms and their translations.", "features.f1.tags.subtitle": "The perfect way to group your keys for filtering and exporting. Keep it organized.", "features.f1.snapshots.subtitle": "Take a snapshot of your project or retrieve project copies from previous snaps.", "features.f2.convert.subtitle": "Import from <a target=_new href=\"http://docs.lokalise.co/category/Vjccih4yFX-supported-file-formats\">any format</a> and export to any format or platform. Let us know if any peculiar format we are missing and we will add it in no time.", "features.f2.meta.subtitle": "Import from the stores, edit, translate, and upload again either manually or using <a target=_new href=\"https://github.com/lokalise/lokalise-fastlane-actions\">fastlane</a> actions.", "features.f2.convert.title": "Convert between language file formats", "features.f2.meta.title": "Manage AppStore and Google Play metadata", "features.f1.title": "Developers first", "features.f2.title": "Automating translation workflow", "features.f3.title": "Powerful tools for translators", "features.f2.notifications.title": "Notifications", "features.f2.upvoting.title": "Translation upvoting and proofreading", "features.f2.chat.title": "Project chat", "features.f2.activity.title": "Project activity", "features.f2.stats.title": "Real-time statistics", "features.f2.order.title": "Order professional translations", "features.f2.notifications.subtitle": "Assign tasks to team members by creating <a target=_new href=\"http://docs.lokalise.co/article/zhHLIjwR1F-notifications\">notifications</a>. ", "features.f2.upvoting.subtitle": "Let your team decide which translation option deserves to go to production with translation <a target=_new href=\"http://docs.lokalise.co/article/QdiPOXw8TX-translation-upvoting\">upvoting and proofreading</a>.", "features.f2.chat.subtitle": "On-site collaboration in Slack-like chat. Comment particular keys and tag to send mentions.", "features.f2.stats.subtitle": "Comprehensive reports on languages, translations done and words translated.", "features.f2.activity.subtitle": "All your project history in one timeline.", "features.f2.order.subtitle": "With the help of translation partner network we can translate your app or website to all popular languages. Pay per words translated from $0.07/word.", "features.f3.multi.title": "Multilingual view", "features.f3.single.title": "Language pair view", "features.f3.inline_mt.title": "Inline machine translations", "features.f3.memory.title": "Translation memory", "features.f3.history.title": "Translation history", "features.f3.spelling.title": "Spelling and grammar check", "features.f3.glossary.title": "Glossary", "features.f3.multi.subtitle": "See all language translations for better control over project progress.", "features.f3.single.subtitle": "Focus on particular language translation, give yourself more space.", "features.f3.inline_mt.subtitle": "Instant translation by Google Translate (NMT) and Yandex Translate.", "features.f3.memory.subtitle": "Fuzzy translation memory with relevance percentage is shared between all your projects and all your team members.", "features.f3.history.subtitle": "We keep versions of each translation update you've made. See differences and roll back with a single click.", "features.f3.spelling.subtitle": "Professional spelling and grammar checker is built-in. Supports 20+ languages.", "features.f3.glossary.subtitle": "Keep translations in consistency by adding terms to the multi-language glossary.", "features.f2.dashboard.subtitle": "See overall project progress in the dashboard, drag projects around or resize for more compact view.", "features.f2.dashboard.title": "Dashboard", "features.f2.contributors.title": "Translate in team", "features.f2.contributors.subtitle": "Invite your team members or fellow translators to work on your projects. <PERSON> read-only or read-write rights per user, per language.", "index.quote1.quote": "Used Lokalise to create default i81n strings for HelpDocs in French, German, Spanish and Dutch. No effort, results in hours. Awesome! 👏", "index.quote2.quote": "Lokalise has proven to be a great addition to our toolset.  The product has the right feature mix with continued innovation and the support has been fantastic.", "index.quote1.signature": "<PERSON>,<br>\nCEO, HelpDocs", "index.quote2.signature": "<PERSON>,<br>\nCo-Founder & CTO at DigitalChalk"}