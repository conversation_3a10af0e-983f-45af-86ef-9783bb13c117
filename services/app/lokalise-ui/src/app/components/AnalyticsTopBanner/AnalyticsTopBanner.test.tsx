import { renderWithProviders, screen } from '@app/testing/utils';

import AnalyticsTopBanner from './AnalyticsTopBanner';

describe('<AnalyticsTopBanner />', () => {
    it('should render when feature flag is on', () => {
        renderWithProviders(<AnalyticsTopBanner>Test contents</AnalyticsTopBanner>, {
            preloadedState: { features: { perfFeatureReportingEngine: 'on' } },
        });

        expect(screen.getByLabelText('Analytics banner')).toBeInTheDocument();
        expect(screen.getByText('Test contents')).toBeInTheDocument();
        expect(screen.getByText('Learn more')).toBeInTheDocument();
    });

    it('should not render when feature flag is off', () => {
        renderWithProviders(<AnalyticsTopBanner>Test contents</AnalyticsTopBanner>);

        expect(screen.queryByLabelText('Analytics banner')).not.toBeInTheDocument();
    });
});
