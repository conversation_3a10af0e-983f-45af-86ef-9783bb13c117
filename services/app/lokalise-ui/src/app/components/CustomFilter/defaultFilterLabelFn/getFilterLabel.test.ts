import getFilterLabel from '.';
import type { FilterObjectData } from '..';

describe('getFilterLabel', () => {
    it('should return undefined if there are no filters', () => {
        expect(getFilterLabel([])).toBeUndefined();
    });

    it('should return count if there are multiple filters', () => {
        const input: FilterObjectData[] = [
            {
                field: 'dueDate',
                fieldLabel: 'Due date',
                matcher: 'isWithin',
                operator: null,
                value: {
                    from: new Date('Tue Jul 13 2021 12:00:00 GMT+0300 (Eastern European Summer Time)'),
                    to: new Date('Sat Jul 24 2021 12:00:00 GMT+0300 (Eastern European Summer Time)'),
                },
                valueLabel: undefined,
                valueType: 'dateRangePicker',
            },
            {
                field: 'dueDate',
                fieldLabel: 'Due date',
                matcher: 'isWithin',
                operator: null,
                value: '1',
                valueLabel: undefined,
                valueType: 'select',
            },
        ];

        expect(getFilterLabel(input)).toEqual('2');
    });

    it('should return valueLabel id there is one filter', () => {
        const input: FilterObjectData[] = [
            {
                field: 'progress',
                fieldLabel: 'Progress',
                matcher: 'closed',
                operator: null,
                value: '1',
                valueLabel: 'Closed',
                valueType: 'select',
            },
        ];

        expect(getFilterLabel(input)).toEqual('Progress (Closed)');
    });

    it('should return value id there is one filter and no valueLabel', () => {
        const input: FilterObjectData[] = [
            {
                field: 'progress',
                fieldLabel: 'Progress',
                matcher: 'closed',
                operator: null,
                value: 'closed',
                valueLabel: undefined,
                valueType: 'select',
            },
        ];

        expect(getFilterLabel(input)).toEqual('Progress (closed)');
    });

    it('should return field value with multiple values', () => {
        const input: FilterObjectData[] = [
            {
                field: 'progress',
                fieldLabel: 'Progress',
                matcher: 'closed',
                operator: null,
                value: ['closed', 'in progress'],
                valueLabel: undefined,
                valueType: 'select',
            },
        ];

        expect(getFilterLabel(input)).toEqual('Progress (2)');
    });

    it('should return matcher label', () => {
        const input: FilterObjectData[] = [
            {
                field: 'dueDate',
                fieldLabel: 'Due Date',
                matcher: 'isEmpty',
                operator: null,
                matcherLabel: 'Is Empty',
                valueType: 'none',
            },
        ];

        expect(getFilterLabel(input)).toEqual('Due Date (Is Empty)');
    });

    it('truncates text longer than 50 chars', () => {
        const input: FilterObjectData[] = [
            {
                field: 'dueDate',
                fieldLabel: 'Due Date',
                matcher: 'isEmpty',
                operator: null,
                matcherLabel:
                    'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec malesuada arcu non tellus commodo, quis ornare dolor finibus. Integer magna felis, lacinia nec cursus vel, pellentesque sit amet',
                valueType: 'none',
            },
        ];

        expect(getFilterLabel(input)).toEqual('Due Date (Lorem ipsum dolor sit amet, consect...)');
    });

    it('should return parsed date range', () => {
        const input: FilterObjectData[] = [
            {
                field: 'dueDate',
                fieldLabel: 'Due date',
                matcher: 'isWithin',
                operator: null,
                value: {
                    from: new Date('Tue Jul 13 2021 12:00:00 GMT+0300 (Eastern European Summer Time)'),
                    to: new Date('Sat Jul 24 2021 12:00:00 GMT+0300 (Eastern European Summer Time)'),
                },
                valueLabel: undefined,
                valueType: 'dateRangePicker',
            },
        ];

        expect(getFilterLabel(input)).toEqual('Due date (Jul 13, 2021 - Jul 24, 2021)');
    });

    it('should return parsed date', () => {
        const input: FilterObjectData[] = [
            {
                field: 'dueDate',
                fieldLabel: 'Due date',
                matcher: 'isWithin',
                operator: null,
                value: new Date('Tue Jul 13 2021 12:00:00 GMT+0300 (Eastern European Summer Time)'),
                valueLabel: undefined,
                valueType: 'dateRangePicker',
            },
        ];

        expect(getFilterLabel(input)).toEqual('Due date (Jul 13, 2021)');
    });
});
