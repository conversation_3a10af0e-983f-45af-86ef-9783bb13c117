import { mockValidSchema } from '@app/features/project/pages/Workflows/tests/mockSchema';
import type {
    AiMtTranslationStepForm,
    GetWorkflowListResponse,
    GetWorkflowResponse,
} from '@lokalise/maestro-expert-tenant-lib';
import type { AiMtDynamicDataResponse } from '@lokalise/types';

export const mockGetWorkflowResponse: GetWorkflowResponse['data'] = {
    id: 'e7eff086-6b49-419c-abbf-ddf04f907820',
    groupId: 'expert_250120_2048440066baca50e88c05.57027251',
    version: 1,
    status: 'Draft',
    title: 'Translation task → review task',
    schema: mockValidSchema as any,
    createdAt: '2024-09-04T12:31:07.909Z',
    updatedAt: '2024-09-04T12:31:07.920Z',
};

export const mockWorkflowsResponse: GetWorkflowListResponse = {
    data: [
        {
            id: '3083af00-cd36-4fad-86f2-de6d49b3461f',
            version: 16,
            status: 'Draft',
            title: 'Greylag goose',
            triggerType: 'Automated',
            createdAt: '2024-07-25T04:29:36.806Z',
            updatedAt: '2024-07-25T21:29:36.806Z',
            publishedAt: '2024-07-01T11:29:36.806Z',
            triggeredAt: '2024-07-02T11:29:36.806Z',
            targetLanguageIds: ['597', '640', '666'],
        },
        {
            id: 'ac3d1fd1-3029-4c32-9adf-8047c42f3342',
            version: 32,
            status: 'Paused',
            title: 'Elk, Wapiti',
            triggerType: 'Scheduled',
            createdAt: '2024-06-25T04:29:36.806Z',
            updatedAt: '2024-06-25T21:29:36.806Z',
            publishedAt: '2024-06-01T11:29:36.806Z',
            triggeredAt: '2024-06-02T11:29:36.806Z',
            targetLanguageIds: ['600', '700', '666', '642', '684'],
        },
        {
            id: '4733128b-7fbc-4e46-9087-ca5ed79261d8',
            version: 50,
            status: 'Archived',
            title: 'Lion, galapagos sea',
            triggerType: 'Scheduled',
            createdAt: '2024-06-25T04:29:36.806Z',
            updatedAt: '2024-06-25T21:29:36.806Z',
            publishedAt: '2024-06-01T11:29:36.806Z',
            triggeredAt: '2024-06-02T11:29:36.806Z',
            targetLanguageIds: ['744', '784', '675', '643'],
        },
        {
            id: 'c25fa7c6-d909-4be6-b76c-15718bb33192',
            version: 71,
            status: 'Draft',
            title: 'California sea lion',
            triggerType: 'Automated',
            createdAt: '2024-06-25T04:29:36.806Z',
            updatedAt: '2024-06-25T21:29:36.806Z',
            publishedAt: '2024-06-01T11:29:36.806Z',
            triggeredAt: '2024-06-02T11:29:36.806Z',
            targetLanguageIds: ['651', '684', '696', '655', '667'],
        },
        {
            id: '3803539c-7bf0-4f7d-9db0-a79b31f512c2',
            version: 11,
            status: 'Canceled',
            title: 'Cormorant, little',
            triggerType: 'Automated',
            createdAt: '2024-06-25T04:29:36.806Z',
            updatedAt: '2024-06-25T21:29:36.806Z',
            publishedAt: '2024-06-01T11:29:36.806Z',
            triggeredAt: '2024-06-02T11:29:36.806Z',
            targetLanguageIds: ['893', '803', '847', '842', '806'],
        },
        {
            id: '98aebb05-876d-4263-81d3-754e33edf678',
            version: 81,
            status: 'Live',
            title: "Cat, miner's",
            triggerType: 'Automated',
            createdAt: '2024-06-25T04:29:36.806Z',
            updatedAt: '2024-06-25T21:29:36.806Z',
            publishedAt: '2024-06-01T11:29:36.806Z',
            triggeredAt: '2024-06-02T11:29:36.806Z',
            targetLanguageIds: ['942', '935'],
        },
        {
            id: 'e412b216-595d-4910-b3bb-ca4db8208f54',
            version: 67,
            status: 'Draft',
            title: 'Plover, three-banded',
            triggerType: 'Automated',
            createdAt: '2024-06-25T04:29:36.806Z',
            updatedAt: '2024-06-25T21:29:36.806Z',
            publishedAt: '2024-06-01T11:29:36.806Z',
            triggeredAt: '2024-06-02T11:29:36.806Z',
            targetLanguageIds: ['840', '859', '897'],
        },
        {
            id: 'dd46a5b3-42c3-4f45-8671-eee09a8da814',
            version: 97,
            status: 'Live',
            title: 'Tern, arctic',
            triggerType: 'Manual',
            createdAt: '2024-06-25T04:29:36.806Z',
            updatedAt: '2024-06-25T21:29:36.806Z',
            publishedAt: '2024-06-01T11:29:36.806Z',
            triggeredAt: '2024-06-02T11:29:36.806Z',
            targetLanguageIds: ['877', '890', '931', '722', '644', '689', '630'],
        },
        {
            id: '9646456a-a7ba-4592-bfc9-8ff91032844c',
            version: 2,
            status: 'Draft',
            title: 'Pie, indian tree',
            triggerType: 'Scheduled',
            createdAt: '2024-06-25T04:29:36.806Z',
            updatedAt: '2024-06-25T21:29:36.806Z',
            publishedAt: '2024-06-01T11:29:36.806Z',
            triggeredAt: '2024-06-02T11:29:36.806Z',
            targetLanguageIds: ['611', '775', '658', '778', '666', '644', '825', '882', '899', '646'],
        },
        {
            id: '2df19e3a-f96d-42c5-a825-d39a8cd616fc',
            version: 81,
            status: 'Live',
            title: 'Otter, canadian river',
            triggerType: 'Scheduled',
            createdAt: '2024-06-25T04:29:36.806Z',
            updatedAt: '2024-06-25T21:29:36.806Z',
            publishedAt: '2024-06-01T11:29:36.806Z',
            triggeredAt: '2024-06-02T11:29:36.806Z',
            targetLanguageIds: ['632', '776', '710', '794', '799', '665'],
        },
    ],
};

export const mockedWorkflow: GetWorkflowResponse = {
    data: {
        id: '3083af00-cd36-4fad-86f2-de6d49b3461f',
        version: 16,
        status: 'Draft',
        title: 'Greylag goose',
        schema: [],
        groupId: 'group-id',
        createdAt: '2024-07-25T04:29:36.806Z',
        updatedAt: '2024-07-25T21:29:36.806Z',
        publishedAt: '2024-07-01T11:29:36.806Z',
    },
};

export const mockDynamicValues = {
    data: {
        stepType: 'trigger_step',
        baseProjectLanguage: {
            id: 640,
            name: 'English',
            languageCode: 'en',
        },
        languages: [
            {
                id: 640,
                name: 'English',
                languageCode: 'en',
            },
            {
                id: 717,
                name: 'Arabic (Egypt)',
                languageCode: 'ar_EG',
            },
        ],
        projectFilters: [
            {
                id: 'builtin_1',
                label: 'Untranslated',
                group: null,
            },
            {
                id: 'builtin_4',
                label: 'Unverified',
                group: null,
            },
        ],
    },
};

export const mockAIMTValues: AiMtTranslationStepForm = {
    stepTitle: 'AI/MT translation',
    translationEngine: 'ai',
    taskTitle: 'Test title',
    aiInstructions: undefined,
    tagKeysAfterClose: false,
    tagsToAddAfterClose: [],
    customTranslationStatusIds: [],
    hasCustomTranslationStatuses: false,
};

export const mockDynamicAIMTValues: AiMtDynamicDataResponse = {
    stepType: 'ai_mt_translation_step',
    tags: ['tag1', 'tag2'],
    customTranslationStatuses: {
        enabled: true,
        multiple: true,
        statuses: [
            { id: 1, title: 'Status 1', color: '' },
            { id: 2, title: 'Status 2', color: '' },
        ],
    },
    engines: ['ai'],
    engineSupportedLanguages: {
        ai: [597, 640],
        google: [],
        deepl: [597, 640],
    },
};
