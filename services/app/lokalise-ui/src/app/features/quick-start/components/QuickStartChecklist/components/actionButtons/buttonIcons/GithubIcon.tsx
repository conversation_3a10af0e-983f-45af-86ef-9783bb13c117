import { SvgIcon, type SvgIconProps } from '@lokalise/louis';

const GithubIcon = (props: SvgIconProps) => (
    <SvgIcon {...props}>
        <path d="M18.98,5.07c-.91-1.56-2.14-2.79-3.7-3.7-1.56-.91-3.26-1.36-5.1-1.36s-3.55,.45-5.1,1.36c-1.56,.91-2.79,2.14-3.7,3.7-.91,1.56-1.36,3.26-1.36,5.1,0,2.22,.65,4.21,1.94,5.98,1.29,1.77,2.96,2.99,5.01,3.67,.24,.04,.41,.01,.53-.09,.11-.11,.17-.24,.17-.4,0-.03,0-.26,0-.72,0-.45,0-.84,0-1.18l-.3,.05c-.19,.04-.44,.05-.73,.05-.3,0-.6-.04-.92-.09-.32-.06-.61-.19-.89-.4-.27-.21-.47-.48-.58-.81l-.13-.3c-.09-.2-.23-.43-.42-.68-.19-.25-.38-.41-.58-.5l-.09-.07c-.06-.04-.12-.1-.17-.16-.05-.06-.09-.12-.12-.19-.03-.06,0-.11,.07-.15s.2-.06,.38-.06l.26,.04c.18,.04,.39,.14,.66,.32,.26,.18,.47,.41,.64,.69,.2,.36,.45,.64,.73,.83,.29,.19,.58,.28,.87,.28s.54-.02,.75-.07c.21-.04,.41-.11,.6-.2,.08-.59,.3-1.05,.65-1.36-.5-.05-.96-.13-1.36-.24-.4-.11-.82-.28-1.24-.52-.43-.24-.78-.53-1.07-.89-.28-.35-.51-.82-.7-1.39-.18-.57-.27-1.24-.27-1.99,0-1.07,.35-1.98,1.05-2.73-.33-.8-.3-1.7,.09-2.7,.26-.08,.64-.02,1.14,.18,.5,.2,.87,.37,1.11,.51,.23,.14,.42,.26,.56,.36,.82-.23,1.67-.34,2.54-.34s1.72,.11,2.54,.34l.5-.32c.34-.21,.75-.41,1.22-.58,.47-.18,.83-.23,1.07-.15,.4,1,.43,1.9,.11,2.7,.7,.75,1.05,1.66,1.05,2.73,0,.75-.09,1.41-.27,1.99-.18,.58-.41,1.04-.7,1.39-.29,.35-.64,.64-1.07,.88-.43,.24-.84,.41-1.24,.52-.4,.11-.85,.19-1.36,.24,.46,.4,.69,1.02,.69,1.88v2.79c0,.16,.06,.29,.17,.4,.11,.11,.28,.14,.52,.09,2.05-.68,3.72-1.9,5.01-3.67,1.29-1.77,1.94-3.76,1.94-5.98,0-1.84-.46-3.55-1.36-5.1Z" />
    </SvgIcon>
);

export default GithubIcon;
