import { mockGetResponse, renderWithProviders, resolveActErrors, screen } from '@app/testing/utils';

import AppHeader from '.';
import { mockContentEngineContext } from '../../../__fixtures__/store';
import { ContentEngineProvider } from '../../../context';
import { ContentEngineEnvProvider } from '../../context';
import { envConfig, envData } from '../AppContent/__fixtures__/store';

const data = {
    title: 'Icon title',
    logo: '/logo.svg',
    logoDarkmode: '/dark-logo.svg',
};

describe('<AppHeader />', () => {
    beforeEach(() => {
        mockGetResponse('/env', envData);
    });

    it('should render AppHeader element', async () => {
        renderWithProviders(
            <ContentEngineEnvProvider value={envConfig}>
                <ContentEngineProvider value={mockContentEngineContext}>
                    <AppHeader appInfo={data} />
                </ContentEngineProvider>
            </ContentEngineEnvProvider>,
        );

        expect(screen.getByRole('img')).toHaveAttribute('src', 'https://cdn.url/logo.svg');
        expect(screen.getByText('Refresh')).toBeInTheDocument();
        expect(screen.getByText('Icon title')).toBeInTheDocument();
        expect(screen.getByAltText('Icon title')).toBeInTheDocument();

        await resolveActErrors();
    });

    it('should have refresh button always enabled', async () => {
        renderWithProviders(
            <ContentEngineEnvProvider value={envConfig}>
                <ContentEngineProvider value={mockContentEngineContext}>
                    <AppHeader appInfo={data} />
                </ContentEngineProvider>
            </ContentEngineEnvProvider>,
        );

        expect(await screen.findByRole('button', { name: 'Refresh' })).toBeEnabled();
    });
});
