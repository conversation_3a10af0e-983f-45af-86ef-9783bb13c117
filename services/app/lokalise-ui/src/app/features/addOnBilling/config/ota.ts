import { AddOnGroups } from '@app/api/addOnBilling';
import { formatNumber } from '@app/utils/i18n';

import type { AddOnCheckoutConfig } from '../types';

export const otaAddOnCheckoutConfig: AddOnCheckoutConfig = {
    modalTitle: 'Purchase additional OTA limit',
    packageSelectorHeader: 'Additional OTA limit to purchase',
    addOnGroup: AddOnGroups.OTATraffic,
    orderSummaryTitle: 'OTA add-on',
    getPackageOptionLabel: (option) => `${formatNumber(option.unitsIncluded)} GB`,
    purchaseSuccessMessage: (option) =>
        `You have completed the purchase for ${formatNumber(option.unitsIncluded)} GB OTA limit.`,
};
