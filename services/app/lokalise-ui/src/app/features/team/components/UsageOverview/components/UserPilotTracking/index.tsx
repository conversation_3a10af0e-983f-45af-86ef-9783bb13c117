import { useEffect } from 'react';

/**
 * Event needed to hide survey until user has viewed the usage-overview tab at least twice
 * If survey is no longer active, this can likely be removed.
 * At this time, <PERSON><PERSON> owns the survey
 * https://run.userpilot.io/surveys/16/details
 */
const UserPilotTracking = () => {
    // Tab switching controlled by jQuery .tab method
    // UsageOverview component always in the DOM even if not visible
    // Therefore need to listen for hash change to determine if the UsageOverview page is visible or not
    useEffect(() => {
        const handleHashChange = () => {
            const { hash } = new URL(window.location.href);

            if (hash === '#usage-overview') {
                window?.userpilot.track('Usage Overview Opened');
            }
        };

        window.addEventListener('hashchange', handleHashChange);

        return () => {
            window.removeEventListener('hashchange', handleHashChange);
        };
    }, []);

    return null;
};

export default UserPilotTracking;
