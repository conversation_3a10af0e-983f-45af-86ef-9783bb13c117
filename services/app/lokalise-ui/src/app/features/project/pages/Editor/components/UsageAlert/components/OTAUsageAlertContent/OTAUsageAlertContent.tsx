import type { AlertProps } from '@lokalise/louis';
import { Alert } from '@lokalise/louis';
import { useEffect, useState } from 'react';

import type { AddOnStatusResponse } from '@app/api/addOnBilling';
import { useGetCurrentTeamPlanQuery } from '@app/api/team';
import { AddOnCheckout } from '@app/features/addOnBilling/components/AddOnCheckout/AddOnCheckout';
import { AddOns } from '@app/features/addOnBilling/types';
import track, { AnalyticsEventName } from '@app/utils/analytics';
import { isFreePlan, isTrialPlan } from '@app/utils/plan';

import { OTAUsageAlertContentBody } from '@app/features/project/pages/Editor/components/UsageAlert/components/OTAUsageAlertContentBody/OTAUsageAlertContentBody';
import styles from './OTAUsageAlertContent.module.css';

const OTA_BANNER_USAGE_WARNING = 90;

type OTAUsageAlertContentProps = {
    percentageUsage: number;
    totalTrafficInGiBs: number;
    usageLimit: number;
    statusData: AddOnStatusResponse;
    openModal: () => void;
    closeModal: () => void;
    onAddOnCheckoutSuccess: () => void;
    openedBillingModal: boolean;
};

export const OTAUsageAlertContent = ({
    percentageUsage,
    closeModal,
    onAddOnCheckoutSuccess,
    openModal,
    totalTrafficInGiBs,
    openedBillingModal,
    usageLimit,
    statusData,
}: OTAUsageAlertContentProps) => {
    const [alertOpen, setAlertOpen] = useState(true);

    const { data: currentPlanData } = useGetCurrentTeamPlanQuery();

    const alertStatus: AlertProps['status'] = percentageUsage >= OTA_BANNER_USAGE_WARNING ? 'error' : 'warning';

    const usage = totalTrafficInGiBs.toFixed(2);

    // Using test id as Alert is not supporting role, and it is hard to use it in tests
    const testId: string =
        percentageUsage >= OTA_BANNER_USAGE_WARNING ? 'ota-usage-alert-error' : 'ota-usage-alert-warning';

    const handleCloseClick = () => {
        setAlertOpen(false);
    };

    const getAlertText = (planId: number) => {
        if (isFreePlan(planId)) {
            return `You have used ${usage} GB out of your ${usageLimit} GB OTA limit on your free plan.`;
        }

        if (isTrialPlan(planId)) {
            return `You have used ${usage} GB out of your ${usageLimit} GB OTA limit on your free trial.`;
        }

        return `You have used ${usage} GB out of your ${usageLimit} GB OTA limit.`;
    };

    const trackAlertDisplayed = (planId: number) => {
        const alertText = getAlertText(planId);
        track(AnalyticsEventName.USAGE_ALERT_DISPLAYED, {
            alert_text: alertText,
            usage,
            limit: usageLimit,
            add_on: AddOns.OTA,
        });
    };

    useEffect(() => {
        if (currentPlanData) {
            trackAlertDisplayed(currentPlanData.subscription.planId);
        }
    }, [currentPlanData]);

    if (!alertOpen || !currentPlanData) {
        return null;
    }

    return (
        <>
            <Alert
                status={alertStatus}
                data-testid={testId}
                onClose={handleCloseClick}
                iconButtonProps={{
                    tooltipPlacement: 'left',
                }}
                className={styles.alertContainer}
            >
                <OTAUsageAlertContentBody
                    usage={usage}
                    planId={currentPlanData.subscription.planId}
                    status={currentPlanData.subscription.status}
                    openModal={openModal}
                    usageLimit={usageLimit}
                    statusData={statusData}
                />
            </Alert>

            <AddOnCheckout
                addOn={AddOns.OTA}
                modalOpened={openedBillingModal}
                onModalClose={closeModal}
                onCheckoutSuccess={onAddOnCheckoutSuccess}
            />
        </>
    );
};
