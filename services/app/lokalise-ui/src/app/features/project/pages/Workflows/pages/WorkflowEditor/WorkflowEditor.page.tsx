import WorkflowLimitNearingBanner from '@app/features/project/pages/Workflows/components/WorkflowLimitNearingBanner/WorkflowLimitNearingBanner';
import { WorkflowLimitReachedBanner } from '@app/features/project/pages/Workflows/components/WorkflowLimitReachedBanner/WorkflowLimitReachedBanner';
import track, { AnalyticsEventName } from '@app/utils/analytics';
import { Loading } from '@lokalise/louis';
import { Workflow, generateLinearLayout } from '@lokalise/workflows';
import { ReactFlowProvider } from '@xyflow/react';
import { useCallback, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useWorkflowGlobalContext } from '../../context/WorkflowGlobalContext';
import { useWorkflowsContext } from '../../context/WorkflowsContext';
import { updateLanguageAssigneesInSchema, updateSchemaStep } from '../../utils/schemaUtils';
import classes from './WorkflowEditor.module.css';
import StepConfigurationSidebar from './components/StepConfigurationSidebar';

const WorkflowEditorPage = () => {
    const {
        workflow,
        workflowLoading,
        workflowError,
        userErrors,
        workflowRefetching,
        currentSchema,
        updateSchema,
        schemaChanged,
        isCustomisable
    } = useWorkflowGlobalContext();

    const navigate = useNavigate();
    const { projectId } = useParams();

    const { initialNodes, initialEdges, originalNodes } = useMemo(() => {
        if (!currentSchema) {
            return { initialNodes: [], initialEdges: [], originalNodes: [] };
        }

        const { nodes, edges } = generateLinearLayout(currentSchema, false);
        return {
            initialNodes: nodes,
            initialEdges: edges,
            originalNodes: [...nodes],
        };
    }, [currentSchema]);

    if (workflowLoading) {
        return (
            <div className={classes.container}>
                <Loading />
            </div>
        );
    }

    if (workflowError) {
        navigate(`/workflows/${projectId}/`);
        return null;
    }

    if (!workflow?.schema || !currentSchema) {
        return null;
    }

    const shouldShowLimitBanner = userErrors.length === 0 && workflow.status !== 'Live';

    return (
        <div className={classes.container}>
            {workflowRefetching && <Loading />}
            {shouldShowLimitBanner && (
                <div className={classes.workflowLimitReachedBannerContainer}>
                    <WorkflowLimitReachedBanner />
                </div>
            )}
            {shouldShowLimitBanner && (
                <div className={classes.workflowLimitNearingBannerContainer}>
                    <WorkflowLimitNearingBanner />
                </div>
            )}

            <ReactFlowProvider>
                <WorkflowEditorInner
                    customisable={isCustomisable}
                    workflow={workflow}
                    currentSchema={currentSchema}
                    updateSchema={updateSchema}
                    schemaChanged={schemaChanged}
                    initialNodes={initialNodes}
                    initialEdges={initialEdges}
                    originalNodes={originalNodes}
                />
            </ReactFlowProvider>
        </div>
    );
};

const WorkflowEditorInner = ({
                                 customisable,
    workflow,
    currentSchema,
    updateSchema,
    schemaChanged,
    initialNodes,
    initialEdges,
    originalNodes,
}: {
    customisable: boolean,
    workflow: any;
    currentSchema: any;
    updateSchema: (newSchema: any) => void;
    schemaChanged: boolean;
    initialNodes: any[];
    initialEdges: any[];
    originalNodes: any[];
}) => {
    const { sourceLanguage } = useWorkflowsContext();
    const [selectedStepId, setSelectedStepId] = useState<string | undefined>();

    const selectedStep = useMemo(() => {
        if (!selectedStepId || !currentSchema) return undefined;
        const step = currentSchema.find((s: any) => s.id === selectedStepId);
        return step?.type;
    }, [selectedStepId, currentSchema]);

    const handleNodeClick = useCallback((_: any, node: any) => {
        track(AnalyticsEventName.WORKFLOW_STEP_CARD_CLICKED, { step: node.type });

        setSelectedStepId(node.id);
    }, []);

    const handlePaneClick = useCallback(() => {
        setSelectedStepId(undefined);
    }, []);

    const handleSchemaUpdate = useCallback(
        (stepId: string, newValues: any) => {
            let updatedSchema = updateSchemaStep(currentSchema, stepId, newValues);

            const updatedStep = updatedSchema.find((step: any) => step.id === stepId);
            if (updatedStep?.type === 'trigger_step' && newValues.targetLanguageIds) {
                updatedSchema = updateLanguageAssigneesInSchema(
                    updatedSchema,
                    newValues.targetLanguageIds,
                    sourceLanguage?.id,
                );
            }

            updateSchema(updatedSchema);
        },
        [currentSchema, updateSchema, sourceLanguage, schemaChanged],
    );

    return (
        <>
            <Workflow
                selectedNode={selectedStepId}
                layout={currentSchema}
                onNodeClick={handleNodeClick}
                onPaneClick={handlePaneClick}
            />
            <StepConfigurationSidebar
                open={selectedStep !== undefined}
                selectedStep={selectedStep}
                selectedStepId={selectedStepId}
                onStepChange={(stepId) => {
                    setSelectedStepId(stepId);
                }}
                onSchemaUpdate={handleSchemaUpdate}
            />
        </>
    );
};

export default WorkflowEditorPage;
