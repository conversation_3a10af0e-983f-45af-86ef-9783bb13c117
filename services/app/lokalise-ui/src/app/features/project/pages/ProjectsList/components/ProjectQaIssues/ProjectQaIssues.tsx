import type { FunctionComponent } from 'react';

import type { Project } from '../../../../types/project';
import { qaIssueFilterMap, qaIssueNameMap } from './constants/qaIssues';
import { Container } from './styles';

export interface Props {
    project: Pick<Project, 'id' | 'stats'>;
}

type StatsDisplayKeys = keyof Exclude<Props['project']['stats'], null>['display'];

const ProjectQaIssues: FunctionComponent<Props> = ({ project }: Props) => {
    if (!project.stats) {
        return null;
    }

    return (
        <>
            {Object.entries(project.stats.display).map(([stats, value]) => (
                <Container key={stats}>
                    {qaIssueNameMap[stats as StatsDisplayKeys]}{' '}
                    <a
                        href={`/project/${project.id}/?view=multi&filter=${
                            qaIssueFilterMap[stats as StatsDisplayKeys]
                        }`}
                    >
                        {value}
                    </a>
                </Container>
            ))}
        </>
    );
};

export default ProjectQaIssues;
