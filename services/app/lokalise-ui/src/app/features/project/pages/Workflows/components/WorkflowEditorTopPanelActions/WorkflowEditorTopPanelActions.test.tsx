import { mockGetWorkflowResponse } from '@app/externalApi/workflows/mockWorkflows';
import { generateSessionStore } from '@app/features/session';
import { generateTeamStore } from '@app/features/team';
import {
    mockDeleteResponse,
    mockGetResponse,
    mockPatchResponse,
    mockPostResponse,
    mockPutResponse,
    renderWithProviders,
    resolveActErrors,
    screen,
    userEvent,
} from '@app/testing/utils';
import { within } from '@testing-library/react';
import type { ReactElement } from 'react';
import type { useNavigate } from 'react-router-dom';
import type { WorkflowGlobalContextType } from '../../context/WorkflowGlobalContext';
import * as manualRunHooks from '../../hooks/useManualRunCooldown';
import WorkflowEditorProviders, { defaultValuesWorkflowEditor } from '../../tests/WorkflowEditorProviders';
import WorkflowEditorTopPanelActions from './WorkflowEditorTopPanelActions';

jest.mock('../../hooks/clientApi/useGetLiveWorkflowsQuery', () => ({
    __esModule: true,
    default: () => ({
        data: {
            data: [],
        },
    }),
}));

jest.mock('@app/api/team', () => ({
    ...jest.requireActual('@app/api/team'),
    useGetWorkflowsNumberLimitQuery: () => ({
        data: {
            workflows_number_limit: 10,
        },
    }),
}));

jest.mock('../../hooks/clientApi/useWorkflowEntitlementsQuery', () => ({
    __esModule: true,
    default: () => ({
        data: {
            workflowsTemplateGateLevel: 1,
            workflowsNumberLimit: 10,
        },
    }),
}));

const mockUseNavigate = jest.fn<ReturnType<typeof useNavigate>, Parameters<typeof useNavigate>>();

jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useParams: () => ({ projectId: 'projectId', workflowId: 'workflowId' }),
    useLocation: () => ({
        pathname: '/workflows/projectId/workflows/workflowId',
    }),
    useNavigate: jest.fn(() => mockUseNavigate),
}));

const wrapper = (children: ReactElement, values?: Partial<WorkflowGlobalContextType>) => {
    return (
        <WorkflowEditorProviders value={{ ...defaultValuesWorkflowEditor, ...values }}>
            {children}
        </WorkflowEditorProviders>
    );
};
describe('WorkflowEditorTopPanelActions', () => {
    beforeEach(() => {
        window.preloadedState = {
            ...window.preloadedState,
            team: generateTeamStore({
                id: 'teamId',
            }),
            session: generateSessionStore({
                userId: 'userId',
                projectId: 'projectId',
            }),
        };

        mockGetResponse('/projects/projectId/languages', {
            languages: {},
        });

        mockGetResponse('/team/current-plan');

        mockPostResponse('http://localhost/projects/projectId/actions/get-user-token', { accessToken: 'accessToken' });

        mockGetResponse(
            `${window.env.maestroApiBaseUrl}/v1/groups/expert_teamId_projectId/workflows/workflowId`,
            mockGetWorkflowResponse,
        );
    });

    describe('render', () => {
        it('draft', async () => {
            const user = userEvent.setup();
            renderWithProviders(
                wrapper(<WorkflowEditorTopPanelActions status="Draft" lastTriggeredAt="" />, {
                    userErrors: ['Run machine translation', 'Review task for team member', 'End'],
                }),
            );

            expect(screen.getByRole('button', { name: 'Save Draft' })).toBeInTheDocument();
            expect(screen.getByRole('button', { name: 'Activate workflow' })).toBeDisabled();

            expect(screen.getByRole('button', { name: 'More options' })).toBeInTheDocument();

            await user.hover(screen.getByRole('button', { name: 'Activate workflow' }));

            const tooltip = await screen.findByRole('tooltip');

            expect(within(tooltip).getByText('Missing details')).toBeInTheDocument();

            expect(
                within(tooltip).getByText('Please add details to the following steps before activating the workflow:'),
            ).toBeInTheDocument();
            expect(within(tooltip).getByText('Run machine translation')).toBeInTheDocument();
            expect(within(tooltip).getByText('Review task for team member')).toBeInTheDocument();
            expect(within(tooltip).getByText('End')).toBeInTheDocument();
        });

        it('draft with changes', async () => {
            renderWithProviders(wrapper(<WorkflowEditorTopPanelActions status="Draft" lastTriggeredAt="" />));

            expect(await screen.findByRole('button', { name: 'Save Draft' })).toBeInTheDocument();
            expect(screen.getByRole('button', { name: 'Activate workflow' })).toBeEnabled();
        });

        it('live', async () => {
            renderWithProviders(wrapper(<WorkflowEditorTopPanelActions status="Live" lastTriggeredAt="" />));

            expect(await screen.findByRole('button', { name: 'Push changes to workflow' })).toBeDisabled();
        });

        it('live with changes', async () => {
            renderWithProviders(wrapper(<WorkflowEditorTopPanelActions status="Live" lastTriggeredAt="" />));

            expect(await screen.findByRole('button', { name: 'Push changes to workflow' })).toBeInTheDocument();
        });

        it('paused', async () => {
            renderWithProviders(wrapper(<WorkflowEditorTopPanelActions status="Paused" lastTriggeredAt="" />));

            expect(await screen.findByRole('button', { name: 'Reactivate workflow' })).toBeInTheDocument();
        });

        it('canceled', async () => {
            const user = userEvent.setup();

            renderWithProviders(wrapper(<WorkflowEditorTopPanelActions status="Canceled" lastTriggeredAt="" />), {
                preloadedState: {
                    team: generateTeamStore({
                        id: 'teamId',
                    }),
                    session: generateSessionStore({
                        userId: 'userId',
                        projectId: 'projectId',
                    }),
                },
            });

            expect(await screen.findByRole('button', { name: 'Reactivate workflow' })).toBeInTheDocument();

            const warningIcon = screen.getByLabelText('Workflow error');

            expect(warningIcon).toBeInTheDocument();

            await user.click(warningIcon);

            const popover = screen.getByRole('dialog');

            expect(popover).toBeInTheDocument();
            expect(
                within(popover).getByRole('link', {
                    name: 'Why do workflows get canceled?',
                }),
            ).toBeInTheDocument();
        });

        it('archived', async () => {
            renderWithProviders(wrapper(<WorkflowEditorTopPanelActions status="Archived" lastTriggeredAt="" />), {
                preloadedState: {
                    team: generateTeamStore({
                        id: 'teamId',
                    }),
                    session: generateSessionStore({
                        userId: 'userId',
                        projectId: 'projectId',
                    }),
                },
            });

            expect(await screen.findByText('This workflow has been archived')).toBeInTheDocument();
        });
    });

    describe('actions', () => {
        it('delete workflow', async () => {
            mockDeleteResponse(
                `${window.env.maestroApiBaseUrl}/v1/groups/expert_teamId_projectId/workflows/workflowId`,
                null,
            );
            const user = userEvent.setup();
            renderWithProviders(
                wrapper(<WorkflowEditorTopPanelActions status="Draft" lastTriggeredAt="" />, {
                    workflow: mockGetWorkflowResponse,
                }),
                {
                    preloadedState: {
                        team: generateTeamStore({
                            id: 'teamId',
                        }),
                        session: generateSessionStore({
                            userId: 'userId',
                            projectId: 'projectId',
                        }),
                    },
                },
            );

            expect(screen.getByRole('button', { name: 'More options' })).toBeInTheDocument();

            await user.click(screen.getByRole('button', { name: 'More options' }));
            await user.click(screen.getByRole('menuitem', { name: 'Delete' }));

            const modal = await screen.findByRole('dialog');

            expect(modal).toBeInTheDocument();
            expect(
                await screen.findByRole('heading', {
                    level: 2,
                    name: 'Are you sure you want to delete this workflow?',
                }),
            ).toBeInTheDocument();
            expect(
                screen.getByText('If you delete this workflow, you won’t be able to recover it.'),
            ).toBeInTheDocument();
            expect(screen.getByRole('button', { name: 'Cancel' })).toBeInTheDocument();
            expect(screen.getByRole('button', { name: 'Delete workflow' })).toBeInTheDocument();

            await user.click(screen.getByRole('button', { name: 'Delete workflow' }));

            expect(
                await screen.findByText(`The workflow ${mockGetWorkflowResponse.title} has been deleted.`),
            ).toBeInTheDocument();

            expect(mockUseNavigate).toHaveBeenCalledWith('/workflows/projectId/');
        });

        it('pause workflow', async () => {
            mockPatchResponse(
                `${window.env.maestroApiBaseUrl}/v1/groups/expert_teamId_projectId/workflows/workflowId/actions/pause`,
                null,
            );
            const user = userEvent.setup();
            renderWithProviders(
                wrapper(<WorkflowEditorTopPanelActions status="Live" lastTriggeredAt="" />, {
                    workflow: mockGetWorkflowResponse,
                }),
                {
                    preloadedState: {
                        team: generateTeamStore({
                            id: 'teamId',
                        }),
                        session: generateSessionStore({
                            userId: 'userId',
                            projectId: 'projectId',
                        }),
                    },
                },
            );

            await user.click(screen.getByRole('button', { name: 'More options' }));
            await user.click(screen.getByRole('menuitem', { name: 'Pause workflow' }));

            expect(
                screen.getByRole('heading', {
                    name: 'Are you sure you want to pause the workflow?',
                }),
            ).toBeInTheDocument();
            expect(
                screen.getByText('All active runs using this workflow will still be completed.'),
            ).toBeInTheDocument();
            expect(screen.getByRole('button', { name: 'Cancel' })).toBeInTheDocument();
            expect(screen.getByRole('button', { name: 'Pause this workflow' })).toBeInTheDocument();

            await user.click(screen.getByRole('button', { name: 'Pause this workflow' }));

            expect(
                await screen.findByText(`The workflow ${mockGetWorkflowResponse.title} has been paused.`),
            ).toBeInTheDocument();
        });

        it('activates the workflow and tracks', async () => {
            window.userpilot = {
                trigger: jest.fn(),
                track: jest.fn(),
                on: jest.fn(),
            };

            mockPostResponse(
                `${window.env.maestroApiBaseUrl}/v1/groups/expert_teamId_projectId/workflows/workflowId/actions/activate`,
                null,
            );

            const user = userEvent.setup();

            renderWithProviders(
                wrapper(<WorkflowEditorTopPanelActions status="Draft" lastTriggeredAt="" />, {
                    workflow: mockGetWorkflowResponse,
                }),
                {
                    preloadedState: {
                        team: generateTeamStore({
                            id: 'teamId',
                        }),
                        session: generateSessionStore({
                            userId: 'userId',
                            projectId: 'projectId',
                        }),
                    },
                },
            );

            await user.click(screen.getByRole('button', { name: 'Activate workflow' }));
            await user.click(screen.getByRole('menuitem', { name: 'Activate workflow' }));

            expect(window.userpilot.track).toHaveBeenCalledTimes(1);
            expect(window.userpilot.track).toHaveBeenCalledWith('workflow_set_to_live');
        });

        it('reactivate workflow without changes', async () => {
            mockPutResponse(
                `${window.env.maestroApiBaseUrl}/v1/groups/expert_teamId_projectId/workflows/workflowId/live`,
                null,
            );
            const user = userEvent.setup();
            renderWithProviders(
                wrapper(<WorkflowEditorTopPanelActions status="Paused" lastTriggeredAt="" />, {
                    workflow: mockGetWorkflowResponse,
                }),
                {
                    preloadedState: {
                        team: generateTeamStore({
                            id: 'teamId',
                        }),
                        session: generateSessionStore({
                            userId: 'userId',
                            projectId: 'projectId',
                        }),
                    },
                },
            );

            await user.click(screen.getByRole('button', { name: 'Reactivate workflow' }));

            const modal = await screen.findByRole('dialog');

            expect(modal).toBeInTheDocument();
            expect(
                await screen.findByRole('heading', {
                    level: 2,
                    name: 'Are you sure you want to reactivate the workflow?',
                }),
            ).toBeInTheDocument();
            expect(
                screen.getByText(
                    'If there are changes to the workflow, they will apply to content added or updated in the future.',
                ),
            ).toBeInTheDocument();
            expect(within(modal).getByRole('button', { name: 'Cancel' })).toBeInTheDocument();
            expect(within(modal).getByRole('button', { name: 'Reactivate workflow' })).toBeInTheDocument();

            await user.click(within(modal).getByRole('button', { name: 'Reactivate workflow' }));

            expect(
                await screen.findByText(`The workflow ${mockGetWorkflowResponse.title} has been reactivated.`),
            ).toBeInTheDocument();
        });

        it('reactivate workflow with changes', async () => {
            mockPutResponse(
                `${window.env.maestroApiBaseUrl}/v1/groups/expert_teamId_projectId/workflows/workflowId/live`,
                null,
            );
            const user = userEvent.setup();
            renderWithProviders(
                wrapper(<WorkflowEditorTopPanelActions status="Paused" lastTriggeredAt="" />, {
                    workflow: mockGetWorkflowResponse,
                    schemaChanged: true,
                }),
                {
                    preloadedState: {
                        team: generateTeamStore({
                            id: 'teamId',
                        }),
                        session: generateSessionStore({
                            userId: 'userId',
                            projectId: 'projectId',
                        }),
                    },
                },
            );

            await user.click(screen.getByRole('button', { name: 'Reactivate workflow' }));

            const modal = await screen.findByRole('dialog');

            expect(modal).toBeInTheDocument();
            expect(
                await screen.findByRole('heading', {
                    level: 2,
                    name: 'Are you sure you want to reactivate the workflow?',
                }),
            ).toBeInTheDocument();
            expect(
                screen.getByText(
                    'If there are changes to the workflow, they will apply to content added or updated in the future.',
                ),
            ).toBeInTheDocument();
            expect(within(modal).getByRole('button', { name: 'Cancel' })).toBeInTheDocument();
            expect(within(modal).getByRole('button', { name: 'Reactivate workflow' })).toBeInTheDocument();

            await user.click(within(modal).getByRole('button', { name: 'Reactivate workflow' }));

            expect(
                await screen.findByText(
                    `The workflow ${mockGetWorkflowResponse.title} has been reactivated and changes have been pushed.`,
                ),
            ).toBeInTheDocument();
        });

        it('push changes', async () => {
            mockPutResponse(
                `${window.env.maestroApiBaseUrl}/v1/groups/expert_teamId_projectId/workflows/workflowId/live`,
                null,
            );

            const user = userEvent.setup();
            renderWithProviders(
                wrapper(<WorkflowEditorTopPanelActions status="Live" lastTriggeredAt="" />, {
                    schemaChanged: true,
                    workflow: mockGetWorkflowResponse,
                }),
                {
                    preloadedState: {
                        team: generateTeamStore({
                            id: 'teamId',
                        }),
                        session: generateSessionStore({
                            userId: 'userId',
                            projectId: 'projectId',
                        }),
                    },
                },
            );

            await user.click(screen.getByRole('button', { name: 'Push changes to workflow' }));

            const modal = await screen.findByRole('dialog');
            expect(modal).toBeInTheDocument();

            expect(
                within(modal).getByRole('heading', {
                    name: 'Push changes to the active workflow?',
                }),
            ).toBeInTheDocument();
            expect(
                within(modal).getByText('The changes will apply to content added or updated in the future.'),
            ).toBeInTheDocument();
            expect(within(modal).getByRole('button', { name: 'Cancel' })).toBeInTheDocument();
            expect(within(modal).getByRole('button', { name: 'Push changes to workflow' })).toBeInTheDocument();

            await user.click(within(modal).getByRole('button', { name: 'Push changes to workflow' }));

            expect(await screen.findByText('The changes have been pushed.')).toBeInTheDocument();
        });

        it('run workflow', async () => {
            mockPostResponse(
                `${window.env.maestroApiBaseUrl}/v1/groups/expert_teamId_projectId/workflows/workflowId/actions/run`,
                null,
            );
            const user = userEvent.setup();
            renderWithProviders(
                wrapper(<WorkflowEditorTopPanelActions status="Live" lastTriggeredAt="" />, {
                    workflow: mockGetWorkflowResponse,
                }),
                {
                    preloadedState: {
                        team: generateTeamStore({
                            id: 'teamId',
                        }),
                        session: generateSessionStore({
                            userId: 'userId',
                            projectId: 'projectId',
                        }),
                    },
                },
            );

            const runButton = screen.getByRole('button', { name: 'Run workflow' });
            expect(runButton).toBeEnabled();

            await user.click(runButton);

            const modal = await screen.findByRole('dialog');
            expect(modal).toBeInTheDocument();
            expect(within(modal).getByText('Are you sure you want to run the workflow?')).toBeInTheDocument();
            expect(within(modal).getByText('This will trigger a new workflow run immediately.')).toBeInTheDocument();

            await user.click(within(modal).getByRole('button', { name: 'Run this workflow' }));
            //todo disabled for now, need to investigate why it's not working
            // expect(await screen.findByText('The workflow has been triggered.')).toBeInTheDocument();
        });

        it('should disable run button when workflow was recently triggered', async () => {
            const user = userEvent.setup();
            const recentTime = new Date();
            recentTime.setSeconds(recentTime.getMinutes() - 2);

            jest.spyOn(manualRunHooks, 'useManualRunCooldown').mockReturnValue({
                canRun: false,
                remainingSeconds: 120,
            });

            renderWithProviders(
                wrapper(<WorkflowEditorTopPanelActions status="Live" lastTriggeredAt={recentTime.toISOString()} />, {
                    workflow: mockGetWorkflowResponse,
                }),
                {
                    preloadedState: {
                        team: generateTeamStore({
                            id: 'teamId',
                        }),
                        session: generateSessionStore({
                            userId: 'userId',
                            projectId: 'projectId',
                        }),
                    },
                },
            );

            const runButton = screen.getByRole('button', { name: 'Run workflow' });

            expect(runButton).toBeDisabled();

            await user.hover(runButton);
            expect(await screen.findByText(/Next manual run available in/)).toBeInTheDocument();

            jest.restoreAllMocks();
        });

        it('should not show run button for non-Live workflows', async () => {
            renderWithProviders(
                wrapper(<WorkflowEditorTopPanelActions status="Draft" lastTriggeredAt="" />, {
                    workflow: mockGetWorkflowResponse,
                }),
                {
                    preloadedState: {
                        team: generateTeamStore({
                            id: 'teamId',
                        }),
                        session: generateSessionStore({
                            userId: 'userId',
                            projectId: 'projectId',
                        }),
                    },
                },
            );

            await resolveActErrors();

            expect(screen.queryByRole('button', { name: 'Run workflow' })).not.toBeInTheDocument();
        });

        it('run button is disabled during cooldown', async () => {
            const lastTriggeredAt = new Date().toISOString();

            jest.spyOn(manualRunHooks, 'useManualRunCooldown').mockReturnValue({
                canRun: false,
                remainingSeconds: 120,
            });

            renderWithProviders(
                wrapper(<WorkflowEditorTopPanelActions status="Live" lastTriggeredAt={lastTriggeredAt} />, {
                    workflow: mockGetWorkflowResponse,
                }),
                {
                    preloadedState: {
                        team: generateTeamStore({
                            id: 'teamId',
                        }),
                        session: generateSessionStore({
                            userId: 'userId',
                            projectId: 'projectId',
                        }),
                    },
                },
            );

            const runButton = screen.getByRole('button', { name: 'Run workflow' });
            expect(runButton).toBeDisabled();

            const user = userEvent.setup();
            await user.hover(runButton);

            const tooltip = await screen.findByRole('tooltip');
            expect(tooltip).toBeInTheDocument();
            expect(tooltip.textContent).toMatch(/Next manual run available in \d+:\d+/);

            jest.restoreAllMocks();
        });

        it('run button is enabled after cooldown', async () => {
            const lastTriggeredAt = new Date(Date.now() - 1000 * 3600).toISOString(); // 1 hour ago

            jest.spyOn(manualRunHooks, 'useManualRunCooldown').mockReturnValue({
                canRun: true,
                remainingSeconds: 0,
            });

            renderWithProviders(
                wrapper(<WorkflowEditorTopPanelActions status="Live" lastTriggeredAt={lastTriggeredAt} />, {
                    workflow: mockGetWorkflowResponse,
                }),
                {
                    preloadedState: {
                        team: generateTeamStore({
                            id: 'teamId',
                        }),
                        session: generateSessionStore({
                            userId: 'userId',
                            projectId: 'projectId',
                        }),
                    },
                },
            );

            await resolveActErrors();

            const runButton = screen.getByRole('button', { name: 'Run workflow' });
            expect(runButton).toBeEnabled();

            jest.restoreAllMocks();
        });

        it('run button is disabled when workflow has changes', async () => {
            renderWithProviders(
                wrapper(<WorkflowEditorTopPanelActions status="Live" lastTriggeredAt="" />, {
                    workflow: mockGetWorkflowResponse,
                    schemaChanged: true,
                }),
                {
                    preloadedState: {
                        team: generateTeamStore({
                            id: 'teamId',
                        }),
                        session: generateSessionStore({
                            userId: 'userId',
                            projectId: 'projectId',
                        }),
                    },
                },
            );

            await resolveActErrors();

            const runButton = screen.getByRole('button', { name: 'Run workflow' });
            expect(runButton).toBeDisabled();
        });
    });
});
