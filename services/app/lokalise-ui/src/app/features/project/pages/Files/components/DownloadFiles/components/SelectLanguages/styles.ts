import CheckboxField from '@app/components/form/CheckboxField';
import { styled } from '@app/styled';
import { spacing, typography } from '@app/theme';

export const LanguagesList = styled.div`
    display: flex;
    flex-direction: column;
    gap: ${spacing(2)};
    max-height: 200px;
    overflow: auto;
    width: 100%;
`;

export const LanguageCheckbox = styled(CheckboxField)`
    padding-top: ${spacing(1)};
    ${typography('custom.description')}
`;

export const AllLanguagesContents = styled.span`
    ${typography('body.small.strong')}
`;
