import { MultipleGroupWarningMessage } from '@app/features/project/pages/Contributors/components/UserGroupRow/UserGroupContributors/MultipleGroupWarningMessage';
import { useUserGroupContributorStats } from '@app/features/project/pages/Contributors/hooks/useUserGroupContributorStats';
import type { UserGroupContributor } from '@app/features/project/pages/Contributors/models/userGroupContributor';
import pluralCheck from '@app/utils/string/pluralCheck';
import { Avatar, Button, Flex, InfoIcon, LocateUserIcon, TableCell, TableRow, Tooltip } from '@lokalise/louis';
import clsx from 'clsx';
import { useContributorsContext } from '../../../context/ContributorsContext';
import { Email, Name } from '../../ContributorCell/styles';
import styles from './UserGroupContributors.module.css';

type UserGroupContributorsRowProps = {
    isExpanded: boolean;
    groupId: number;
    groupName: string;
    contributors: UserGroupContributor[];
    onContributorFocus: (id: number) => void;
    onCollapsedRowClick?: () => void;
};

export const UserGroupContributors = ({
    isExpanded,
    groupId,
    groupName,
    contributors,
    onContributorFocus,
    onCollapsedRowClick,
}: UserGroupContributorsRowProps) => {
    const { contributors: projectContributors } = useContributorsContext();

    const { statsByContributorId: stats, hasAnyWarnings } = useUserGroupContributorStats({
        userGroupContributors: contributors,
        projectContributors,
    });

    const ariaLabel = `${groupName} has ${contributors.length} member${pluralCheck(contributors.length)}`;

    if (!isExpanded && !hasAnyWarnings) {
        return (
            <TableRow aria-label={ariaLabel} className={styles.row} onClick={onCollapsedRowClick}>
                <TableCell colSpan={5} className={styles.cell} />
            </TableRow>
        );
    }

    if (!isExpanded && hasAnyWarnings) {
        return (
            <TableRow aria-label={ariaLabel} className={styles.row} onClick={onCollapsedRowClick}>
                <TableCell className={clsx(styles.cell, styles.warningCell)} />
                <TableCell colSpan={4} className={clsx([styles.cell, styles.warningCell])}>
                    <Flex className={styles.info} align="center" gap={2}>
                        <InfoIcon size="16px" />
                        <p>Expand to see extra information about permissions and languages...</p>
                    </Flex>
                </TableCell>
            </TableRow>
        );
    }

    return (
        <>
            {contributors.map(({ id, name, email, avatar }, i) => (
                <TableRow key={id} aria-label={ariaLabel} className={clsx([styles.row, styles.highlight])}>
                    <TableCell
                        className={clsx(styles.cell, styles.padded, {
                            [styles.excluded]: stats[id]?.isIndividualContributor,
                        })}
                        align="center"
                    >
                        <Flex direction="row" gap={4}>
                            <Avatar alt={name} size={40} src={avatar} />
                            <Flex direction="column">
                                <Name>{name}</Name>
                                <Email>{email}</Email>
                            </Flex>
                        </Flex>
                    </TableCell>
                    <TableCell colSpan={3} className={styles.cell}>
                        <Flex align="center" aria-label={`Member ${i + 1}: ${name}`}>
                            <Flex direction="column">
                                {stats[id]?.isInMultipleGroups && !stats[id]?.isIndividualContributor && (
                                    <Flex className={styles.info} align="center" gap={2}>
                                        <InfoIcon size="16px" />
                                        <MultipleGroupWarningMessage
                                            otherGroups={stats[id].groupNamesExcluding(groupId)}
                                        />
                                    </Flex>
                                )}
                                {stats[id]?.isIndividualContributor && (
                                    <Flex className={styles.info} align="center" gap={2}>
                                        <InfoIcon size="16px" />
                                        {stats[id]?.isOwner ? (
                                            <p>This user is a team owner, they have full access to the project.</p>
                                        ) : (
                                            <p>Individual contributor permissions override group permissions</p>
                                        )}
                                    </Flex>
                                )}
                            </Flex>
                        </Flex>
                    </TableCell>
                    <TableCell className={styles.cell} align="center">
                        {stats[id]?.isIndividualContributor && (
                            <Tooltip
                                classNames={{ content: styles.tooltip }}
                                tooltip={
                                    <>
                                        Scroll to <br />
                                        {name}
                                    </>
                                }
                            >
                                <Button className={styles.locateUser} onClick={() => onContributorFocus(id)}>
                                    <LocateUserIcon size="20px" />
                                </Button>
                            </Tooltip>
                        )}
                    </TableCell>
                </TableRow>
            ))}
        </>
    );
};
