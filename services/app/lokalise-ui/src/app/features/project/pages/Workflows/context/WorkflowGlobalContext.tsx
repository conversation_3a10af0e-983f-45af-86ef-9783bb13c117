import noop from '@app/utils/noop';
import type { GetWorkflowResponse } from '@lokalise/maestro-expert-tenant-lib';
import { createContext, useContext } from 'react';

export type WorkflowGlobalContextType = {
    workflow?: GetWorkflowResponse['data'];
    workflowLoading: boolean;
    workflowRefetching: boolean;
    workflowError: boolean;

    title?: string;
    setTitle: (title: string) => void;

    userErrors: string[];
    setUserErrors: (errors: string[]) => void;

    currentSchema?: any;
    updateSchema: (newSchema: any) => void;
    schemaChanged: boolean;
};

export const WorkflowGlobalContext = createContext<WorkflowGlobalContextType>({
    workflow: undefined,
    workflowLoading: false,
    workflowRefetching: false,
    workflowError: false,
    title: '',
    setTitle: noop,
    userErrors: [],
    setUserErrors: noop,
    currentSchema: undefined,
    updateSchema: noop,
    schemaChanged: false,
});

export const useWorkflowGlobalContext = () => {
    const context = useContext(WorkflowGlobalContext);

    if (context === undefined) {
        throw new Error('useWorkflowGlobalContext must be used within a WorkflowGlobalContext');
    }

    return context;
};
