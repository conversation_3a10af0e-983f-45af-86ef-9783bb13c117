import type { ProjectLanguage } from '@app/api/projects';
import { useGetDynamicDataQuery } from '@app/api/workflows';
import type { GetWorkflowResponse } from '@lokalise/maestro-expert-tenant-lib';
import type { ScheduledTriggerStepForm } from '@lokalise/maestro-expert-tenant-lib';
import { type PropsWithChildren, useCallback, useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import useWorkflowEntitlementsQuery from '../hooks/clientApi/useWorkflowEntitlementsQuery';
import useWorkflowQuery from '../hooks/clientApi/useWorkflowQuery';
import stepsValuesValidation from '../utils/stepsValuesValidation';
import { WorkflowGlobalContext } from './WorkflowGlobalContext';
import { useWorkflowsContext } from './WorkflowsContext';

const removeRedundantLanguages = (
    data: GetWorkflowResponse['data'],
    projectLanguages?: Record<string, ProjectLanguage>,
): GetWorkflowResponse['data'] => {
    if (!projectLanguages) {
        return data;
    }

    const updatedSchema = { ...data }.schema.map((schema) => {
        if (schema.type === 'trigger_step') {
            return {
                ...schema,
                values: {
                    ...schema.values,
                    targetLanguageIds: schema.values.targetLanguageIds.filter((id) => projectLanguages[id]),
                },
            };
        }

        if (schema.type === 'review_task_step' || schema.type === 'translate_task_step') {
            return {
                ...schema,
                values: {
                    ...schema.values,
                    languageAssignees: schema.values.languageAssignees.filter(
                        (language) => projectLanguages[language.languageId],
                    ),
                },
            };
        }

        return schema;
    });

    return { ...data, schema: updatedSchema };
};

export const WorkflowGlobalContextProvider = ({ children }: PropsWithChildren) => {
    const { languageByIndex } = useWorkflowsContext();
    const { projectId } = useParams();

    const {
        data: workflowData,
        isLoading: workflowLoading,
        isRefetching: workflowRefetching,
        isError: workflowError,
    } = useWorkflowQuery({
        options: {
            select: (workflow) => {
                const updatedData = removeRedundantLanguages(
                    workflow.data as GetWorkflowResponse['data'],
                    languageByIndex,
                );

                return { ...workflow, data: updatedData as GetWorkflowResponse['data'] };
            },
        },
    });
    const { data: entitlements } = useWorkflowEntitlementsQuery();

    const [title, setTitle] = useState<string>();
    const [userErrors, setUserErrors] = useState<string[]>([]);
    const [currentSchema, setCurrentSchema] = useState<any>();

    // Get trigger step values for dynamic data
    const triggerValues = currentSchema?.find((step: any) => step.type === 'trigger_step')
        ?.values as ScheduledTriggerStepForm;

    const { data: dynamicData } = useGetDynamicDataQuery(
        {
            projectId: projectId as string,
            stepType: 'ai_mt_translation_step',
            monitoredLanguage: triggerValues?.monitoredLanguageId ?? '',
            targetLanguages: triggerValues?.targetLanguageIds ?? [],
        },
        {
            skip: !workflowData?.data,
            refetchOnMountOrArgChange: true,
        },
    );

    useEffect(() => {
        if (workflowData?.data) {
            setTitle(workflowData.data.title);

            setCurrentSchema(workflowData.data.schema);
        }
    }, [workflowData?.data]);

    useEffect(() => {
        if (currentSchema) {
            const formErrors = stepsValuesValidation(
                currentSchema as any, // TODO: Fix type after migration
                languageByIndex ?? {},
                dynamicData,
            );
            setUserErrors(formErrors);
        }
    }, [currentSchema, dynamicData, languageByIndex]);

    const schemaChanged = useMemo(() => {
        if (!workflowData?.data?.schema || !currentSchema) return false;
        return JSON.stringify(workflowData.data.schema) !== JSON.stringify(currentSchema);
    }, [workflowData?.data?.schema, currentSchema]);

    const updateSchema = useCallback((newSchema: any) => {
        setCurrentSchema(newSchema);
    }, []);

    const value = useMemo(
        () => ({
            title,
            setTitle,
            userErrors,
            setUserErrors,
            workflow: workflowData?.data as GetWorkflowResponse['data'],
            workflowError,
            workflowLoading,
            workflowRefetching,
            currentSchema,
            updateSchema,
            schemaChanged,
            isCustomisable: entitlements?.isWorkflowCustomizationAvailable,
        }),
        [
            title,
            userErrors,
            workflowData?.data,
            workflowError,
            workflowLoading,
            workflowRefetching,
            currentSchema,
            updateSchema,
            schemaChanged,
            entitlements,
        ],
    );

    return <WorkflowGlobalContext.Provider value={value}>{children}</WorkflowGlobalContext.Provider>;
};
