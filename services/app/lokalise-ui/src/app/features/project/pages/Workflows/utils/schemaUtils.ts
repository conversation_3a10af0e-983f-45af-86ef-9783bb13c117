import type { GetWorkflowResponse } from '@lokalise/maestro-expert-tenant-lib';

export const updateSchemaStep = (
    schema: GetWorkflowResponse['data']['schema'],
    stepId: string,
    newValues: any,
): GetWorkflowResponse['data']['schema'] => {
    if (!schema) return [];

    return schema.map((step) => {
        if (step.id === stepId) {
            return {
                ...step,
                values: {
                    ...step.values,
                    ...newValues,
                },
            };
        }

        return step;
    });
};

export const updateLanguageAssigneesInSchema = (
    schema: GetWorkflowResponse['data']['schema'],
    targetLanguageIds: number[],
    monitoredLanguage?: number,
): GetWorkflowResponse['data']['schema'] => {
    if (!schema) return [];

    return schema.map((step) => {
        const isTaskStep = step.type === 'translate_task_step' || step.type === 'review_task_step';
        if (!isTaskStep) return step;

        return {
            ...step,
            values: {
                ...step.values,
                languageAssignees: updateLanguageAssignees(
                    step.values.languageAssignees,
                    [...targetLanguageIds, ...(monitoredLanguage !== undefined ? [monitoredLanguage] : [])],
                    monitoredLanguage,
                ),
            },
        };
    });
};

const updateLanguageAssignees = (currentAssignees: any[], allLanguageIds: number[], monitoredLanguage?: number) => {
    const existingAssignees = currentAssignees.filter((assignee) => allLanguageIds.includes(assignee.languageId));

    const existingLanguageIds = existingAssignees.map((a) => a.languageId);
    const newAssignees = allLanguageIds
        .filter((langId) => !existingLanguageIds.includes(langId))
        .map((langId) => ({
            languageId: langId,
            isSource: langId === monitoredLanguage,
            userIds: [],
            userGroupIds: [],
        }));

    return [...existingAssignees, ...newAssignees];
};
