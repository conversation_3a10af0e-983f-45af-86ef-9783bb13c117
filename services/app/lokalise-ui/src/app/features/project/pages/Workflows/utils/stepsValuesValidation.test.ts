import type { ProjectLanguage } from '@app/api/projects';
import { mockDynamicAIMTValues } from '@app/externalApi/workflows/mockWorkflows';
import type { FrontendLayoutStep } from '@lokalise/maestro-expert-tenant-lib';
import { mockEmptyStepLabel, mockMissingDataInSchema, mockValidSchema } from '../tests/mockSchema';
import stepsValuesValidation from './stepsValuesValidation';

const mockedLanguages: Record<string, ProjectLanguage> = {
    1: {
        id: 1,
        iso: 'en',
        isDefault: true,
        name: 'English',
        ccIso: 'en',
        isRtl: false,
        defaultPlural: null,
        overwrittenPlural: null,
    },
    717: {
        id: 717,
        iso: 'ru',
        isDefault: false,
        name: 'Russian',
        ccIso: 'ru',
        isRtl: false,
        defaultPlural: null,
        overwrittenPlural: null,
    },
    3: {
        id: 3,
        iso: 'de',
        isDefault: false,
        name: 'German',
        ccIso: 'de',
        isRtl: false,
        defaultPlural: null,
        overwrittenPlural: null,
    },
};

describe('stepsValuesValidation', () => {
    it('should return an empty array when all steps are valid', () => {
        const result = stepsValuesValidation(mockValidSchema, {});
        expect(result).toEqual([]);
    });

    it('should return step titles for invalid steps', () => {
        const result = stepsValuesValidation(mockMissingDataInSchema, {});
        expect(result).toEqual(['Invalid Trigger Step', 'Invalid Translation Step', 'Invalid Review Step']);
    });

    it('should use step label when stepTitle is missing or empty', () => {
        const result = stepsValuesValidation(mockEmptyStepLabel, {});
        expect(result).toEqual(['Trigger and scope', 'Translation', 'Review']);
    });

    it('should handle empty steps array', () => {
        const steps: FrontendLayoutStep<string>[] = [];

        const result = stepsValuesValidation(steps, {});
        expect(result).toEqual([]);
    });

    it('should return invalid steps with ai_mt_translation_step', () => {
        const result = stepsValuesValidation(mockValidSchema, mockedLanguages, mockDynamicAIMTValues);
        expect(result).toEqual(['Translation']);
    });
});
