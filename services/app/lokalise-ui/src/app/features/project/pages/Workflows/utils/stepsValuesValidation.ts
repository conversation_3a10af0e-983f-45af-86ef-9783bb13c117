import type { ProjectLanguage } from '@app/api/projects';
import {
    AI_MT_TRANSLATION_STEP_FORM_SCHEMA,
    APPLY_TM_STEP_FORM_SCHEMA,
    CLEAR_TRANSLATIONS_STEP_FORM_SCHEMA,
    END_STEP_FORM_SCHEMA,
    type FrontendLayoutStep,
    REVIEW_TASK_STEP_FORM_SCHEMA,
    SCHEDULED_TRIGGER_STEP_FORM_SCHEMA,
    type ScheduledTriggerStepForm,
    type StepType,
    TRANSLATE_TASK_STEP_FORM_SCHEMA,
    type TRANSLATION_ENGINE,
} from '@lokalise/maestro-expert-tenant-lib';
import type { AiMtDynamicDataResponse, StepDynamicDataResponse } from '@lokalise/types';
import type { LanguageByIndex } from '@lokalise/workflows';
import { stepLabel } from '@lokalise/workflows';
import type { ZodSchema, z } from 'zod';

export type Engine = z.infer<typeof TRANSLATION_ENGINE>;

const stepSchemaMap: Record<StepType, ZodSchema> = {
    trigger_step: SCHEDULED_TRIGGER_STEP_FORM_SCHEMA,
    translate_task_step: TRANSLATE_TASK_STEP_FORM_SCHEMA,
    ai_mt_translation_step: AI_MT_TRANSLATION_STEP_FORM_SCHEMA,
    review_task_step: REVIEW_TASK_STEP_FORM_SCHEMA,
    clear_translations_step: CLEAR_TRANSLATIONS_STEP_FORM_SCHEMA,
    apply_tm_step: APPLY_TM_STEP_FORM_SCHEMA,
    end_step: END_STEP_FORM_SCHEMA,
};

export const getUnsupportedLanguages = (
    targetLanguageIds: number[],
    languages: LanguageByIndex,
    engineSupportedLanguages?: AiMtDynamicDataResponse['engineSupportedLanguages'],
    selectedEngine?: Engine,
): ProjectLanguage[] => {
    if (!selectedEngine || !engineSupportedLanguages) {
        return [];
    }

    const unsupportedLanguages: ProjectLanguage[] = [];

    for (const languageId of targetLanguageIds) {
        const language = languages[languageId];
        if (!language) {
            throw new Error(`Language with ID ${languageId} does not exist in the dataset.`);
        }

        const supportedForEngine = engineSupportedLanguages[selectedEngine];

        if (!supportedForEngine || !supportedForEngine.includes(Number(languageId))) {
            unsupportedLanguages.push(language);
        }
    }

    return unsupportedLanguages;
};

export const stepsValuesValidation = (
    steps: FrontendLayoutStep<string>[],
    languages: Record<string, ProjectLanguage>,
    dynamicData?: StepDynamicDataResponse,
): string[] => {
    const stepsWithErrors: string[] = [];

    for (const { type, values } of steps) {
        const stepSchema = stepSchemaMap[type];

        const result = stepSchema.safeParse(values);

        if (type === 'ai_mt_translation_step' && dynamicData && dynamicData?.stepType === 'ai_mt_translation_step') {
            const triggerValues = steps.find((step) => step.type === 'trigger_step')
                ?.values as ScheduledTriggerStepForm;
            const unsupportedLanguages = getUnsupportedLanguages(
                triggerValues.targetLanguageIds,
                languages,
                dynamicData.engineSupportedLanguages,
                (values as any).translationEngine,
            );

            if (unsupportedLanguages.length > 0) {
                stepsWithErrors.push(stepLabel[type]);
            }
        }

        if (!result.success) {
            const stepHasTitle = 'stepTitle' in values && (values as any).stepTitle !== '';
            stepsWithErrors.push(stepHasTitle ? (values as any).stepTitle : stepLabel[type]);
        }
    }

    return stepsWithErrors;
};

export default stepsValuesValidation;
