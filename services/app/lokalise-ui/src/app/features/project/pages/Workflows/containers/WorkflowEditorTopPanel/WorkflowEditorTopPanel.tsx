import { ArrowLeftIcon, Flex, IconButton } from '@lokalise/louis';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import DiscardChangesModal from '../../components/DiscardChangesModal/DiscardChangesModal';
import WorkflowEditorTopPanelActions from '../../components/WorkflowEditorTopPanelActions/WorkflowEditorTopPanelActions';
import WorkflowEditorTopPanelTitle from '../../components/WorkflowEditorTopPanelTitle/WorkflowEditorTopPanelTitle';
import { WorkflowsStatus } from '../../components/WorkflowsStatus/WorkflowsStatus';
import { useWorkflowGlobalContext } from '../../context/WorkflowGlobalContext';

import track, { AnalyticsEventName } from '@app/utils/analytics';
import styles from './WorkflowEditorTopPanel.module.css';

const WorkflowEditorTopPanel = () => {
    const navigate = useNavigate();
    const { workflow, schemaChanged } = useWorkflowGlobalContext();
    const [isLeaveWithoutSavingModalOpen, setIsLeaveWithoutSavingModalOpen] = useState(false);

    const onBackToPrevious = () => {
        if (!schemaChanged) {
            navigate(-1);
            return;
        }

        setIsLeaveWithoutSavingModalOpen(true);
    };

    const onCloseWithoutSaving = () => {
        track(AnalyticsEventName.WORKFLOW_LEFT_WITHOUT_SAVING);

        setIsLeaveWithoutSavingModalOpen(false);
        navigate(-1);
    };

    if (!workflow || !workflow.schema) {
        return null;
    }

    return (
        <div className={styles.container}>
            <Flex gap={4} align="center">
                <IconButton size="md" ariaLabel="Back to previous page" onClick={onBackToPrevious}>
                    <ArrowLeftIcon size="24px" />
                </IconButton>

                <WorkflowEditorTopPanelTitle title={workflow.title} />

                <WorkflowsStatus status={workflow.status} error={workflow.error} />
            </Flex>

            <WorkflowEditorTopPanelActions status={workflow.status} lastTriggeredAt={workflow.triggeredAt} />

            {isLeaveWithoutSavingModalOpen && (
                <DiscardChangesModal
                    onCancel={() => {
                        setIsLeaveWithoutSavingModalOpen(false);
                    }}
                    onCloseWithoutSaving={onCloseWithoutSaving}
                />
            )}
        </div>
    );
};

export default WorkflowEditorTopPanel;
