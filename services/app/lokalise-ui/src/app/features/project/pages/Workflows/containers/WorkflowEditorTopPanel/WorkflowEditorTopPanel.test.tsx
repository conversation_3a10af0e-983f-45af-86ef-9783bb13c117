import { mockedWorkflow } from '@app/externalApi/workflows/mockWorkflows';
import { mockGetResponse, renderWithProviders, screen, userEvent } from '@app/testing/utils';
import type { ReactElement } from 'react';
import type { WorkflowGlobalContextType } from '../../context/WorkflowGlobalContext';
import WorkflowEditorProviders, { defaultValuesWorkflowEditor } from '../../tests/WorkflowEditorProviders';
import { mockUseNavigate } from '../../tests/utils';
import WorkflowEditorTopPanel from './WorkflowEditorTopPanel';

jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useParams: () => ({ projectId: '1', workflowId: '12' }),
    useLocation: jest.fn().mockReturnValue({}),
    useNavigate: jest.fn(() => mockUseNavigate),
}));

jest.mock('../../hooks/clientApi/useGetLiveWorkflowsQuery', () => ({
    __esModule: true,
    default: () => ({
        data: {
            data: [],
        },
    }),
}));

jest.mock('@app/api/team', () => ({
    ...jest.requireActual('@app/api/team'),
    useGetWorkflowsNumberLimitQuery: () => ({
        data: {
            workflows_number_limit: 10,
        },
    }),
    useGetCurrentTeamPlanQuery: () => ({}),
}));

const wrapper = (children: ReactElement, values?: Partial<WorkflowGlobalContextType>) => {
    return (
        <WorkflowEditorProviders value={{ ...defaultValuesWorkflowEditor, ...values }}>
            {children}
        </WorkflowEditorProviders>
    );
};

const workflow = mockedWorkflow.data;

const preloadedState = {
    team: {
        id: '1',
        role: 'owner' as const,
        admin: true,
        name: 'Test Team',
        owners: [],
        projects: [{ id: '1', name: 'Test Project' }],
    },
};

describe('WorkflowEditorTopPanel', () => {
    beforeEach(() => {
        mockGetResponse('/projects/1/languages', {
            languages: {},
        });
    });

    it('should render workflow data', async () => {
        renderWithProviders(
            wrapper(<WorkflowEditorTopPanel />, {
                workflow,
                workflowError: false,
                workflowLoading: false,
                schemaChanged: false,
            }),
            { preloadedState },
        );

        expect(await screen.findByText('Greylag goose')).toBeInTheDocument();
        expect(screen.getByLabelText('Back to previous page')).toBeInTheDocument();
        expect(screen.getByText('DRAFT')).toBeInTheDocument();

        expect(screen.getByRole('button', { name: 'Save Draft' })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: 'Activate workflow' })).toBeEnabled();
    });

    it('should open the leave without saving modal', async () => {
        const user = userEvent.setup();
        renderWithProviders(
            wrapper(<WorkflowEditorTopPanel />, {
                workflow,
                workflowError: false,
                workflowLoading: false,
                schemaChanged: true,
            }),
            { preloadedState },
        );

        expect(screen.getByLabelText('Back to previous page')).toBeInTheDocument();

        await user.click(screen.getByLabelText('Back to previous page'));

        expect(
            screen.getByRole('heading', { level: 2, name: 'Close without pushing changes to workflow?' }),
        ).toBeInTheDocument();
        expect(
            screen.getByText(`The changes will be discarded if you don't push them to the workflow.`),
        ).toBeInTheDocument();
        expect(screen.getByRole('button', { name: 'Cancel' })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: 'Close and discard changes' })).toBeInTheDocument();

        await user.click(screen.getByRole('button', { name: 'Cancel' }));

        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });

    it('should close the leave without saving modal', async () => {
        const user = userEvent.setup();

        renderWithProviders(
            wrapper(<WorkflowEditorTopPanel />, {
                workflow,
                workflowError: false,
                workflowLoading: false,
                schemaChanged: true,
            }),
            { preloadedState },
        );

        expect(screen.getByLabelText('Back to previous page')).toBeInTheDocument();

        await user.click(screen.getByLabelText('Back to previous page'));

        expect(
            screen.getByRole('heading', { level: 2, name: 'Close without pushing changes to workflow?' }),
        ).toBeInTheDocument();
        expect(
            screen.getByText(`The changes will be discarded if you don't push them to the workflow.`),
        ).toBeInTheDocument();
        expect(screen.getByRole('button', { name: 'Cancel' })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: 'Close and discard changes' })).toBeInTheDocument();

        await user.click(screen.getByRole('button', { name: 'Close and discard changes' }));

        expect(mockUseNavigate).toHaveBeenCalledWith(-1);
    });

    it('should navigate back', async () => {
        const user = userEvent.setup();

        renderWithProviders(
            wrapper(<WorkflowEditorTopPanel />, {
                workflow,
                workflowError: false,
                workflowLoading: false,
                schemaChanged: false,
            }),
            { preloadedState },
        );

        expect(screen.getByLabelText('Back to previous page')).toBeInTheDocument();

        await user.click(screen.getByLabelText('Back to previous page'));

        expect(mockUseNavigate).toHaveBeenCalledWith(-1);
    });
});
