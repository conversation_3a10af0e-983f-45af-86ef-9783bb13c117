import type { PostWorkflowResponse } from '@lokalise/maestro-expert-tenant-lib';

export const mockWorkflowPostResponse: PostWorkflowResponse = {
    data: {
        id: 'ad3204e0-e251-4b7e-ad76-921f95b90859',
        groupId: 'expert_250120_2048440066baca50e88c05.57027251',
        version: 1,
        status: 'Draft',
        title: 'Translation task → review task',
        schema: [
            {
                values: {
                    stepTitle: 'Trigger and scope',
                    scheduleFrequencyExpression: '00 09 */1 * *',
                    scheduleStartingDate: new Date().toISOString().split('T')[0],
                    monitoredLanguageId: 1,
                    targetLanguageIds: [717],
                    monitoredKeyType: 'new' as const,
                },
                type: 'trigger_step' as const,
                triggerType: 'Scheduled' as const,
                id: 'TriggerStep',
            },
        ],
        error: null,
        createdAt: '2024-10-24T10:59:45.720Z',
        updatedAt: '2024-10-24T10:59:45.730Z',
    },
};
