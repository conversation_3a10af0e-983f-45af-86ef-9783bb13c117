import { useSetTranslationVerifiedMutation } from '@app/api/editor';
import track, { AnalyticsEventName } from '@app/utils/analytics';

import ToggleMarkAsVerifiedButton from './ToggleMarkAsVerifiedButton';

type MarkAsVerifiedProps = {
    markedAsVerified: boolean;
    translationId: number;
    onTranslationUpdated: () => void;
};

const ToggleMarkAsVerified = ({ translationId, onTranslationUpdated, markedAsVerified }: MarkAsVerifiedProps) => {
    const [toggleMarkAsVerified, { isLoading }] = useSetTranslationVerifiedMutation();

    const handleToggle = async () => {
        const response = await toggleMarkAsVerified({
            translationId,
        });

        if ('error' in response || !response?.data) {
            return;
        }

        onTranslationUpdated();
        if (response.data.unverified === 0) {
            track(AnalyticsEventName.TRANSLATION_VERIFIED, {
                action: 'keypress',
            });
        }
    };

    return (
        <ToggleMarkAsVerifiedButton
            onClick={async () => {
                await handleToggle();
            }}
            loading={isLoading}
            markedAsVerified={markedAsVerified}
        />
    );
};
export default ToggleMarkAsVerified;
