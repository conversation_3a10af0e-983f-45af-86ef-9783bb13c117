import { saveDraftWorkflow } from '@app/externalApi/workflows';
import useSelector from '@app/hooks/useSelector';
import { getTeamId } from '@app/selectors';
import type { PutWorkflowDraftResponse } from '@lokalise/maestro-expert-tenant-lib';
import { type UseMutationOptions, useMutation } from '@tanstack/react-query';
import { useParams } from 'react-router-dom';
import type { WretchError } from 'wretch/types';

type UseWorkflowProps = {
    options?: UseMutationOptions<PutWorkflowDraftResponse, WretchError, { schema: any }>;
};

const useSaveDraftWorkflowMutation = ({ options }: UseWorkflowProps = {}) => {
    const { projectId = '', workflowId = '' } = useParams();
    const teamId = useSelector(getTeamId);
    const groupId = `expert_${teamId}_${projectId}`;

    return useMutation({
        mutationKey: ['workflow-save-draft', groupId, workflowId],
        mutationFn: ({ schema }: { schema: any }) =>
            saveDraftWorkflow({
                path: { groupId, workflowId },
                body: { schema },
            }),
        ...options,
    });
};

export default useSaveDraftWorkflowMutation;
