import { Flex, Loading } from '@lokalise/louis';
import { useEffect, useMemo, useState } from 'react';

import {
    type ErrorMessage,
    type TaskItemStatus,
    useSaveTranslationMutation,
    useSetTranslationReviewedMutation,
} from '@app/api/editor';
import emitSocketEvent from '@app/sockets/emitSocketEvent';
import useEditorContext from '../../../../hooks/useEditorContext';
import type { RawSpellchecks } from '../../../../utils/types';
import type { MismatchResults, QaResult } from '../../types/translation';
import TranslationActions, { type CustomTranslationStatusesState } from './components/TranslationActions';
import TranslationEditorClosed from './components/TranslationEditorClosed';
import TranslationEditorOpened from './components/TranslationEditorOpened';
import useTranslationInEditMode from './hooks/useTranslationInEditMode';
import { TranslationEditorWrapper } from './styles';
import type { ActiveTask } from './types';
import getAugmentedTokens from './utils/getAugmentedTokens';

export type TranslationEditorProps = {
    translationId: number;
    value: string | null;
    sourceValue: string;
    languageId: number;
    segmentCharacterCount: number;
    autoReview: boolean;
    isBaseLanguage: boolean;
    isReviewingAllowed: boolean;
    activeTask: ActiveTask;
    onSaveTranslation: (completed: boolean) => void;
    isPlural: boolean;
    itemId: number;
    characterLimit: number;
    spellCheckMatches: RawSpellchecks;
    qaResults: readonly QaResult[];

    keyId: number;
    taskId: number;
    taskItemId: number;
    referenceLanguageId: number;
    translationVerified: boolean;
    taskItemStatus: TaskItemStatus | null;
    segmentNumber: number;
    enableRequestCorrections: boolean;
    showReviewButton: boolean;
    reviewed: boolean;
    showGengoCommentsButton: boolean;
    onCompleteTaskSuccess: (
        closedLanguage: Record<string, never> | null,
        closedTask: Record<string, never> | null,
        status: TaskItemStatus,
        translationId: number,
        projectId: string,
        branchId: string | null,
        taskId: number,
        taskItemId: number,
    ) => void;
    onTranslationUpdated: (translationId: number, projectId: string, branchId: string | null) => void;
    customTranslationStatuses: CustomTranslationStatusesState | null;
    canIgnoreQATest: boolean;
    onQATestIgnored: (test: { testNumber: number }) => void;
    mismatchResults: MismatchResults;
    isHtmlMismatchErrorsVisible: boolean;
    isPlaceholdersMismatchErrorsVisible: boolean;
    onMismatchErrorsIgnored: () => void;
    onHtmlPlaceholdersVisibilityChanged: (visible: boolean) => void;
    branchId: number | null;
    currentLockTitle?: string;
    rtl: boolean;
};

const TranslationEditor = ({
    translationId,
    value,
    sourceValue,
    segmentCharacterCount,
    languageId,
    autoReview,
    isBaseLanguage,
    isReviewingAllowed,
    isPlural,
    activeTask,
    onSaveTranslation,
    itemId,
    characterLimit,
    spellCheckMatches,
    qaResults,

    keyId,
    taskId,
    taskItemId,
    referenceLanguageId,
    translationVerified,
    taskItemStatus,
    segmentNumber,
    enableRequestCorrections,
    showReviewButton,
    reviewed,
    showGengoCommentsButton,
    onCompleteTaskSuccess,
    onTranslationUpdated,
    customTranslationStatuses,
    canIgnoreQATest,
    onQATestIgnored,
    mismatchResults,
    isHtmlMismatchErrorsVisible,
    isPlaceholdersMismatchErrorsVisible,
    onMismatchErrorsIgnored,
    onHtmlPlaceholdersVisibilityChanged,

    currentLockTitle,
    branchId = null,
    rtl,
}: TranslationEditorProps) => {
    const [draftValue, setDraftValue] = useState<string | null | undefined>(value);
    const [lastSavedValue, setLastSavedValue] = useState<string | null | undefined>(value);
    const [isEditing, setIsEditing] = useState(false);
    const [saveTranslation, saveTranslationState] = useSaveTranslationMutation();
    const [setTranslationReviewed, setTranslationReviewedState] = useSetTranslationReviewedMutation();
    const needsReview = autoReview && isReviewingAllowed && draftValue === lastSavedValue;
    const isLoading = saveTranslationState.isLoading || setTranslationReviewedState.isLoading;

    const translationInEditMode = useTranslationInEditMode(translationId);

    const { id: projectId } = useEditorContext();

    // TODO: Investigate if there is a better way to communicate with editor actions
    useEffect(() => {
        document.dispatchEvent(
            new CustomEvent('editor-opened', {
                detail: { isEditing, translationId, toLanguageId: languageId },
            }),
        );
    }, [isEditing, translationId, languageId]);

    const onStartEditing = () => {
        if (translationInEditMode || currentLockTitle) {
            return;
        }
        emitSocketEvent('translation edit start', {
            translationId,
            projectId,
            branchId,
        });
        setTimeout(() => setIsEditing(true), 0);

        // Clear out any old response data
        saveTranslationState.reset();
        setTranslationReviewedState.reset();
    };

    const handleSaveTranslation = async (
        updatedValue: string | null | undefined,
        setError: (message: string) => void,
        isReviewed = autoReview,
        markItemAsCompleted = false,
    ) => {
        setDraftValue(updatedValue);

        const saveTranslationResponse = await saveTranslation({
            translationId,
            translation: updatedValue || '',
            languageId,
            markItemAsCompleted,
            isReviewed: isReviewed && isReviewingAllowed,
        });

        if (!('error' in saveTranslationResponse)) {
            setIsEditing(false);
            setLastSavedValue(updatedValue);
        }

        if ('error' in saveTranslationResponse) {
            setError((saveTranslationResponse as ErrorMessage)?.error?.data?.error?.message);

            return;
        }

        // review condition
        if (needsReview) {
            const setTranslationReviewedResponse = await setTranslationReviewed({
                translationId,
                isReviewed: true,
            });
            // eslint doesn't like using setTranslationReviewedResponse['err'],
            // and TS doesn't like using setTranslationReviewedResponse.err.
            // Hence, this for now.
            if (
                'err' in setTranslationReviewedResponse &&
                (setTranslationReviewedResponse as { err: number }).err === 1
            ) {
                // Error
            }
        }

        if (isBaseLanguage) {
            document.dispatchEvent(
                new CustomEvent('source_value_changed', {
                    detail: { itemId, updatedValue },
                }),
            );
        }

        onSaveTranslation(markItemAsCompleted);

        emitSocketEvent('translation edit end', {
            translationId,
            projectId,
            branchId,
        });
    };

    const onCancelTranslation = () => {
        setTimeout(() => setIsEditing(false), 0);
        setDraftValue(lastSavedValue);
        emitSocketEvent('translation edit end', {
            translationId,
            projectId,
            branchId,
        });
    };

    const baseAugmentedTokens = useMemo(
        () => getAugmentedTokens(sourceValue || '', spellCheckMatches),
        [sourceValue, spellCheckMatches],
    );

    const baseWraps = !isBaseLanguage && !isEditing ? baseAugmentedTokens : null;

    return (
        <TranslationEditorWrapper direction="row" wrap="nowrap" align="start" fullWidth justify="space-between">
            {isEditing ? (
                <TranslationEditorOpened
                    sourceValue={sourceValue}
                    segmentCharacterCount={segmentCharacterCount}
                    characterLimit={characterLimit}
                    isBaseLanguage={isBaseLanguage}
                    isPlural={isPlural}
                    onSave={handleSaveTranslation}
                    onCancel={onCancelTranslation}
                    activeTask={activeTask}
                    needsReview={needsReview}
                    saveError={saveTranslationState.error}
                    value={draftValue || ''}
                    qaResults={qaResults}
                    baseWraps={baseWraps}
                    keyId={keyId}
                    translationId={translationId}
                    languageId={languageId}
                    spellCheckMatches={spellCheckMatches}
                />
            ) : (
                <Flex
                    wrap="nowrap"
                    align="center"
                    gap={8}
                    direction={rtl ? 'row-reverse' : 'row'}
                    dir={rtl ? 'rtl' : 'inherit'}
                >
                    <TranslationEditorClosed
                        value={lastSavedValue}
                        onOpen={onStartEditing}
                        baseWraps={baseWraps}
                        spellCheckMatches={spellCheckMatches}
                        translationId={translationId}
                        isBaseLanguage={isBaseLanguage}
                        isLocked={!!currentLockTitle}
                    />
                    <TranslationActions
                        keyId={keyId}
                        languageId={languageId}
                        taskId={taskId}
                        taskItemId={taskItemId}
                        referenceLanguageId={referenceLanguageId}
                        translationId={translationId}
                        taskItemStatus={taskItemStatus}
                        onTranslationUpdated={onTranslationUpdated}
                        onCompleteTaskSuccess={onCompleteTaskSuccess}
                        segmentNumber={segmentNumber}
                        qaResults={qaResults}
                        canIgnoreQATest={canIgnoreQATest}
                        onQATestIgnored={onQATestIgnored}
                        mismatchResults={mismatchResults}
                        isHtmlMismatchErrorsVisible={isHtmlMismatchErrorsVisible}
                        isPlaceholdersMismatchErrorsVisible={isPlaceholdersMismatchErrorsVisible}
                        onMismatchErrorsIgnored={onMismatchErrorsIgnored}
                        onHtmlPlaceholdersVisibilityChanged={onHtmlPlaceholdersVisibilityChanged}
                        isPlural={isPlural}
                        showReviewButton={showReviewButton}
                        reviewed={reviewed}
                        translationVerified={translationVerified}
                        customTranslationStatuses={customTranslationStatuses}
                        enableRequestCorrections={enableRequestCorrections}
                        showGengoCommentsButton={showGengoCommentsButton}
                        lockTitle={currentLockTitle}
                    />
                </Flex>
            )}
            {isLoading && <Loading />}
        </TranslationEditorWrapper>
    );
};

export default TranslationEditor;
