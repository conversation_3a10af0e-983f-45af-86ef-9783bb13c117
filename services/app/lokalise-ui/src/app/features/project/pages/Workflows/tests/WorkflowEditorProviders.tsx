import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import type { ReactElement } from 'react';
import { BrowserRouter } from 'react-router-dom';

import { WorkflowGlobalContext, type WorkflowGlobalContextType } from '../context/WorkflowGlobalContext';
import { WorkflowsContextProvider } from '../context/WorkflowsContextProvider';

const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            retry: false,
            refetchOnWindowFocus: false,
        },
    },
});

export const defaultValuesWorkflowEditor: WorkflowGlobalContextType = {
    workflow: undefined,
    workflowLoading: false,
    workflowRefetching: false,
    workflowError: false,
    currentSchema: [],
    updateSchema: jest.fn(),
    schemaChanged: false,
    userErrors: [],
    setUserErrors: jest.fn(),
    title: '',
    setTitle: jest.fn(),
};

export const WorkflowEditorProviders = ({
    children,
    value = defaultValuesWorkflowEditor,
}: {
    children: ReactElement;
    value?: WorkflowGlobalContextType;
}) => (
    <QueryClientProvider client={queryClient}>
        <BrowserRouter>
            <WorkflowsContextProvider>
                <WorkflowGlobalContext.Provider value={value}>{children}</WorkflowGlobalContext.Provider>
            </WorkflowsContextProvider>
        </BrowserRouter>
    </QueryClientProvider>
);

export default WorkflowEditorProviders;
