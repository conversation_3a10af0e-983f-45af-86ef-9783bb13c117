import { useGetWorkflowsNumberLimitQuery } from '@app/api/team';
import {
    Button,
    CaretDownIcon,
    Flex,
    IconButton,
    Link,
    Loading,
    Menu,
    MenuDotsIcon,
    MenuItem,
    MenuList,
    Popover,
    Tooltip,
    WarningIcon,
    showToast,
} from '@lokalise/louis';
import type { WorkflowStatus } from '@lokalise/maestro-expert-tenant-lib';
import { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import useSelector from '@app/hooks/useSelector';
import { getTeamId } from '@app/selectors';
import track, { AnalyticsEventName } from '@app/utils/analytics';
import { useQueryClient } from '@tanstack/react-query';
import { useWorkflowGlobalContext } from '../../context/WorkflowGlobalContext';
import { useWorkflowsContext } from '../../context/WorkflowsContext';
import useActivateWorkflowMutation from '../../hooks/clientApi/useActivateWorkflowMutation';
import useDeleteWorkflowMutation from '../../hooks/clientApi/useDeleteWorkflowMutation';
import useManualRunWorkflowMutation from '../../hooks/clientApi/useManualRunWorkflowMutation';
import usePauseWorkflowMutation from '../../hooks/clientApi/usePauseWorkflowMutation';
import usePushChangesWorkflowMutation from '../../hooks/clientApi/usePushChangesWorkflowMutation';
import useSaveDraftWorkflowMutation from '../../hooks/clientApi/useSaveDraftWorkflowMutation';
import { useManualRunCooldown } from '../../hooks/useManualRunCooldown';
import useShouldShowWorkflowLimitBanner from '../../hooks/useShouldShowWorkflowLimitBanner';
import { getLayoutGateLevel } from '../../utils/getLayoutGateLevel';
import { getTooltipContent } from '../../utils/getTooltipContent';
import DeleteWorkflowModal from '../DeleteWorkflowModal/DeleteWorkflowModal';
import PauseWorkflowModal from '../PauseWorkflowModal/PauseWorkflowModal';
import PushChangesModal from '../PushChangesModal/PushChangesModal';
import ReactivateWorkflowModal from '../ReactivateWorkflowModal/ReactivateWorkflowModal';
import RunWorkflowModal from '../RunWorkflowModal/RunWorkflowModal';
import styles from './WorkflowEditorTopPanelActions.module.css';

type BuilderWorkflowActionsProps = {
    status: WorkflowStatus;
    lastTriggeredAt?: string | null;
};

const WorkflowEditorTopPanelActions = ({ status, lastTriggeredAt = '' }: BuilderWorkflowActionsProps) => {
    const navigate = useNavigate();
    const { projectId, workflowId } = useParams();
    const teamId = useSelector(getTeamId);
    const groupId = `expert_${teamId}_${projectId}`;
    const queryClient = useQueryClient();

    const [deleteModalOpened, setDeleteModalOpened] = useState(false);
    const [pushChangesModalOpened, setPushChangesModalOpened] = useState(false);
    const [pauseWorkflowModalOpened, setPauseWorkflowModalOpened] = useState(false);
    const [reactivateWorkflowModalOpened, setReactivateWorkflowModalOpened] = useState(false);
    const [runWorkflowModalOpened, setRunWorkflowModalOpened] = useState(false);

    const { workflow, userErrors, schemaChanged, currentSchema } = useWorkflowGlobalContext();
    const schema = currentSchema || workflow?.schema;

    const { workflowsTemplateGateLevel } = useWorkflowsContext();

    const isActionGated = getLayoutGateLevel(schema) > workflowsTemplateGateLevel;

    const toolTipStyle = { zIndex: 'var(--lok-z-index-toast)' };

    const invalidateWorkflows = () => {
        queryClient.invalidateQueries({
            queryKey: ['workflow', workflowId, groupId],
        });

        queryClient.invalidateQueries({
            queryKey: ['workflow-runs', groupId],
            exact: false,
        });
    };

    const { mutate: onSaveDraft, isPending: isSaveDraftLoading } = useSaveDraftWorkflowMutation({
        options: {
            onError: (error) => {
                showToast({ type: 'error', title: error.json.message });
            },
            onSuccess: () => {
                track(AnalyticsEventName.WORKFLOW_SAVE_DRAFT);

                invalidateWorkflows();
                showToast({
                    type: 'success',
                    description: 'The workflow draft was saved.',
                    title: 'Success',
                });
            },
        },
    });

    const { mutate: onActivateWorkflow, isPending: isActivateLoading } = useActivateWorkflowMutation({
        options: {
            onError: (error) => {
                showToast({ type: 'error', title: error.json.message });
            },
            onSuccess: (data, variables, context) => {
                track(AnalyticsEventName.WORKFLOW_ACTIVATED, {
                    runNow: !!variables.runNow,
                });

                invalidateWorkflows();

                navigate(`/workflows/${projectId}/`);

                showToast({
                    type: 'success',
                    description: `The workflow ${workflow?.title} has been activated.`,
                    title: 'Success',
                });

                window?.userpilot.track('workflow_set_to_live');
            },
        },
    });

    const { mutate: onReactivateWorkflow, isPending: isReactivateLoading } = usePushChangesWorkflowMutation({
        options: {
            onError: (error) => {
                showToast({ type: 'error', title: error.json.message });
            },
            onSuccess: () => {
                invalidateWorkflows();
                setPushChangesModalOpened(false);
                setReactivateWorkflowModalOpened(false);

                const message = schemaChanged
                    ? `The workflow ${workflow?.title} has been reactivated and changes have been pushed.`
                    : `The workflow ${workflow?.title} has been reactivated.`;

                showToast({
                    type: 'success',
                    description: message,
                    title: 'Success',
                });
            },
        },
    });

    const { mutate: onPauseWorkflow, isPending: isPauseLoading } = usePauseWorkflowMutation({
        options: {
            onError: (error) => {
                showToast({ type: 'error', title: error.json.message });
            },
            onSuccess: () => {
                invalidateWorkflows();
                setPauseWorkflowModalOpened(false);

                showToast({
                    type: 'success',
                    description: `The workflow ${workflow?.title} has been paused.`,
                    title: 'Success',
                });
            },
        },
    });
    const { mutate: onDeleteWorkflow, isPending: isDeleteLoading } = useDeleteWorkflowMutation({
        options: {
            onSuccess: () => {
                track(AnalyticsEventName.WORKFLOW_DELETED);

                invalidateWorkflows();
                setDeleteModalOpened(false);

                navigate(`/workflows/${projectId}/`);

                showToast({
                    type: 'success',
                    description: `The workflow ${workflow?.title} has been deleted.`,
                    title: 'Success',
                });
            },
        },
    });
    const { mutate: onPushChangesWorkflow, isPending: isPushChangesLoading } = usePushChangesWorkflowMutation({
        options: {
            onError: (error) => {
                showToast({ type: 'error', title: error.json.message });
            },
            onSuccess: () => {
                track(AnalyticsEventName.WORKFLOW_PUSH_CHANGES);

                invalidateWorkflows();
                setPushChangesModalOpened(false);
                setReactivateWorkflowModalOpened(false);

                showToast({
                    type: 'success',
                    description: 'The changes have been pushed.',
                    title: 'Success',
                });
            },
        },
    });

    const { mutate: onManualRunWorkflow, isPending: isManualRunWorkflowLoading } = useManualRunWorkflowMutation({
        options: {
            onError: (error) => {
                showToast({ type: 'error', title: error.json.message });
            },
            onSuccess: () => {
                invalidateWorkflows();
                showToast({
                    type: 'success',
                    description: 'The workflow has been triggered.',
                    title: 'Success',
                });
                track(AnalyticsEventName.WORKFLOW_MANUAL_RUN_STARTED, { source: 'Workflows editor' });
            },
        },
    });

    const isLive = status === 'Live';
    const isArchived = status === 'Archived';
    const isPaused = status === 'Paused';
    const isDraft = status === 'Draft';
    const isCanceled = status === 'Canceled';

    const hasUserErrors = userErrors.length > 0;

    const actionButtonLoading =
        isActivateLoading || isReactivateLoading || isPauseLoading || isDeleteLoading || isPushChangesLoading;

    const { shouldShowLimitReachedBanner: hasReachedLimit } = useShouldShowWorkflowLimitBanner();
    const { data: workflowsNumberLimit } = useGetWorkflowsNumberLimitQuery();

    const { canRun, remainingSeconds } = useManualRunCooldown(lastTriggeredAt);

    if (isArchived) {
        return (
            <Flex gap={4} align="center">
                This workflow has been archived
            </Flex>
        );
    }

    const tooltipContent = getTooltipContent({
        isGated: isActionGated,
        hasReachedLimit,
        noChanges: !schemaChanged,
        cooldown: !canRun,
        isSaveDraftLoading,
        userErrors,
        workflowsNumberLimit,
        canRun,
        remainingSeconds,
    });

    return (
        <Flex gap={4} align="center">
            {actionButtonLoading && <Loading />}

            {isCanceled && (
                <Tooltip tooltip={tooltipContent} style={toolTipStyle}>
                    <Button
                        disabled={hasUserErrors}
                        loading={isPushChangesLoading}
                        variant="primary"
                        onClick={() => schema && onPushChangesWorkflow({ schema, runNow: false })}
                    >
                        Reactivate workflow
                    </Button>
                </Tooltip>
            )}

            {isDraft && (
                <>
                    <Button
                        variant="secondary"
                        disabled={isSaveDraftLoading}
                        loading={isSaveDraftLoading}
                        onClick={() => {
                            schema && onSaveDraft({ schema });
                        }}
                    >
                        Save Draft
                    </Button>
                    <Menu
                        menuButton={
                            <Tooltip style={toolTipStyle} tooltip={tooltipContent} placement="bottom">
                                <Button
                                    variant="primary"
                                    disabled={hasUserErrors || hasReachedLimit || isSaveDraftLoading}
                                >
                                    <Flex align="center" gap={2}>
                                        Activate workflow
                                        <CaretDownIcon size="12px" />
                                    </Flex>
                                </Button>
                            </Tooltip>
                        }
                    >
                        <MenuList placement="bottom-end" className={styles.menuList}>
                            <MenuItem
                                onClick={() => {
                                    schema && onActivateWorkflow({ schema });
                                }}
                                disabled={hasReachedLimit}
                            >
                                Activate workflow
                            </MenuItem>
                            <MenuItem
                                onClick={() => {
                                    schema && onActivateWorkflow({ schema, runNow: true });
                                }}
                                disabled={hasReachedLimit}
                            >
                                Activate and run now
                            </MenuItem>
                        </MenuList>
                    </Menu>
                </>
            )}

            {isLive && (
                <>
                    <Tooltip style={toolTipStyle} tooltip={tooltipContent}>
                        <Button
                            variant="secondary"
                            onClick={() => {
                                setRunWorkflowModalOpened(true);
                            }}
                            disabled={!canRun || schemaChanged || hasUserErrors || isPushChangesLoading}
                        >
                            Run workflow
                        </Button>
                    </Tooltip>
                    <Tooltip style={toolTipStyle} tooltip={tooltipContent}>
                        <Button
                            variant="primary"
                            disabled={!schemaChanged || hasUserErrors || isPushChangesLoading || isActionGated}
                            onClick={() => {
                                setPushChangesModalOpened(true);
                            }}
                        >
                            Push changes to workflow
                        </Button>
                    </Tooltip>
                </>
            )}

            {isPaused && (
                <Tooltip style={toolTipStyle} tooltip={tooltipContent}>
                    <Button
                        disabled={hasUserErrors || isActionGated}
                        loading={isPushChangesLoading}
                        variant="primary"
                        onClick={() => setReactivateWorkflowModalOpened(true)}
                    >
                        Reactivate workflow
                    </Button>
                </Tooltip>
            )}

            {isCanceled && (
                <Popover
                    withArrow
                    content={
                        <p className={styles.popoverContent}>
                            {workflow?.error?.message}.{' '}
                            <Link
                                href="https://docs.lokalise.com/en/articles/9582608-workflows#h_4c5117e5c9"
                                target="_blank"
                            >
                                Why do workflows get canceled?
                            </Link>
                        </p>
                    }
                >
                    <IconButton ariaLabel="Workflow error" tooltipOverride="">
                        <WarningIcon color="var(--lok-color-text-danger)" />
                    </IconButton>
                </Popover>
            )}

            <Menu
                menuButton={({ opened }) => (
                    <IconButton tooltipPlacement="left" active={opened} ariaLabel="More options" size="md">
                        <MenuDotsIcon size="20px" />
                    </IconButton>
                )}
            >
                <MenuList className={styles.dottedMenu} placement="bottom-end">
                    {isLive && <MenuItem onClick={() => setPauseWorkflowModalOpened(true)}>Pause workflow</MenuItem>}
                    <MenuItem
                        className={styles.dangerMenuItem}
                        onClick={() => {
                            setDeleteModalOpened(true);
                        }}
                    >
                        Delete
                    </MenuItem>
                </MenuList>
            </Menu>

            {deleteModalOpened && (
                <DeleteWorkflowModal
                    loading={isDeleteLoading}
                    onCancel={() => setDeleteModalOpened(false)}
                    onDeleteWorkflow={() => {
                        workflowId && onDeleteWorkflow({ groupId, workflowId });
                    }}
                />
            )}

            {pushChangesModalOpened && (
                <PushChangesModal
                    loading={isPushChangesLoading}
                    onCancel={() => {
                        setPushChangesModalOpened(false);
                    }}
                    onPushChanges={() => {
                        schema && onPushChangesWorkflow({ schema });
                    }}
                />
            )}

            {pauseWorkflowModalOpened && (
                <PauseWorkflowModal
                    loading={isPauseLoading}
                    onCancel={() => {
                        setPauseWorkflowModalOpened(false);
                    }}
                    onPause={() => (workflowId ? onPauseWorkflow({ groupId, workflowId }) : undefined)}
                />
            )}

            {reactivateWorkflowModalOpened && (
                <ReactivateWorkflowModal
                    workflowId={workflowId}
                    loading={isPushChangesLoading}
                    onCancel={() => {
                        setReactivateWorkflowModalOpened(false);
                    }}
                    onReactivate={() => (schema ? onReactivateWorkflow({ schema, runNow: false }) : undefined)}
                />
            )}

            {runWorkflowModalOpened && (
                <RunWorkflowModal
                    loading={isManualRunWorkflowLoading}
                    onCancel={() => {
                        setRunWorkflowModalOpened(false);
                    }}
                    onRun={() => {
                        workflowId && onManualRunWorkflow({ groupId, workflowId });
                        setRunWorkflowModalOpened(false);
                    }}
                />
            )}
        </Flex>
    );
};

export default WorkflowEditorTopPanelActions;
