import { Outlet } from 'react-router-dom';

import WorkflowEditorTopPanel from '../../containers/WorkflowEditorTopPanel/WorkflowEditorTopPanel';
import { WorkflowGlobalContextProvider } from '../../context/WorkflowGlobalContextProvider';

import classes from './WorkflowLayout.module.css';

export const WorkflowLayout = () => (
    <WorkflowGlobalContextProvider>
        <div className={classes.container}>
            <div className={classes.topPanelContainer}>
                <WorkflowEditorTopPanel />
            </div>
            <div className={classes.outletContainer}>
                <Outlet />
            </div>
        </div>
    </WorkflowGlobalContextProvider>
);

export default WorkflowLayout;
