import { useGetDynamicDataQuery } from '@app/api/workflows';
import { useWorkflowGlobalContext } from '@app/features/project/pages/Workflows/context/WorkflowGlobalContext';
import { useWorkflowsContext } from '@app/features/project/pages/Workflows/context/WorkflowsContext';

import useGetTeamWorkflowsEntitlements from '@app/features/project/pages/Workflows/hooks/clientApi/useGetWorkflowsEntitlements';
import useIsFeatureEnabled from '@app/hooks/useIsFeatureEnabled';
import { Loading, showToast } from '@lokalise/louis';
import type {
    AiMtTranslationStepForm,
    ApplyTmStepForm,
    ReviewTaskStepForm,
    ScheduledTriggerStepForm,
    StepType,
    TranslateTaskStepForm,
} from '@lokalise/maestro-expert-tenant-lib';
import {
    AIMTStepForm,
    ClearTranslationStepForm,
    EndStepForm,
    ReviewStepForm,
    StartStepForm,
    TranslationMemoryStepForm,
    TranslationStepForm,
} from '@lokalise/workflows';
import clsx from 'clsx';
import { useCallback } from 'react';
import { useParams } from 'react-router-dom';
import classes from './StepConfigurationSidebar.module.css';

import { STEP_CAN_BE_RENDERED_WITHOUT_DATA, getDynamicDataRequestInfo, updateLanguageAssignees } from './helpers';

interface StepConfigurationSidebarProps {
    open: boolean;
    selectedStep?: StepType;
    selectedStepId?: string;
    onStepChange?: (stepId: string, stepType: string) => void;
    onSchemaUpdate?: (stepId: string, newValues: any) => void;
}

export const StepConfigurationSidebar = ({
    open,
    selectedStep,
    selectedStepId,
    onStepChange,
    onSchemaUpdate,
}: StepConfigurationSidebarProps) => {
    const params = useParams();
    const { currentSchema } = useWorkflowGlobalContext();
    const schema = currentSchema;

    const isTemplateTriggerGatesEnabled = useIsFeatureEnabled('metroFeatureTemplateTriggerGates');
    const { data: entitlements } = useGetTeamWorkflowsEntitlements();

    const { languageByIndex, sourceLanguage } = useWorkflowsContext();
    const monitoredLanguage = sourceLanguage?.id;

    const selectedStepSchema = schema?.find(({ id }: any) => id === selectedStepId);

    const { additionalQueryParamsRequired, targetLanguages } = getDynamicDataRequestInfo({
        schema,
        selectedStepSchema,
    });

    const { data, isError, isFetching } = useGetDynamicDataQuery(
        {
            projectId: params.projectId as string,
            stepType: selectedStep as StepType,
            ...(additionalQueryParamsRequired && {
                monitoredLanguage,
                targetLanguages: [...targetLanguages, ...(monitoredLanguage !== undefined ? [monitoredLanguage] : [])],
            }),
        },
        {
            refetchOnMountOrArgChange: true,
            skip:
                selectedStep === undefined ||
                selectedStep === 'end_step' ||
                selectedStep === 'clear_translations_step' ||
                selectedStep === 'apply_tm_step',
        },
    );

    const onFormChange = useCallback(
        (formValues: Partial<ScheduledTriggerStepForm | TranslateTaskStepForm | ReviewTaskStepForm>) => {
            if (!schema) return;
            let newSchema = [...schema];
            const updatedSchemaIndex = newSchema.findIndex(({ id }) => id === selectedStepId);

            if (updatedSchemaIndex === -1) return;
            const updatedStep = {
                ...newSchema[updatedSchemaIndex],
                values: {
                    ...newSchema[updatedSchemaIndex].values,
                    ...formValues,
                },
            } as (typeof newSchema)[number];

            newSchema[updatedSchemaIndex] = updatedStep;

            if (updatedStep.type === 'trigger_step') {
                const targetLanguageIds = updatedStep.values.targetLanguageIds;

                newSchema = newSchema.map((step) => {
                    const isTaskStep = step.type === 'translate_task_step' || step.type === 'review_task_step';
                    if (!isTaskStep) return step;
                    return {
                        ...step,
                        values: {
                            ...step.values,
                            languageAssignees: updateLanguageAssignees(
                                step.values.languageAssignees,
                                [...targetLanguageIds, ...(monitoredLanguage !== undefined ? [monitoredLanguage] : [])],
                                monitoredLanguage,
                            ),
                        },
                    };
                });
            }

            if (JSON.stringify(schema) !== JSON.stringify(newSchema) && onSchemaUpdate && selectedStepId) {
                onSchemaUpdate(selectedStepId, formValues);
            }
        },
        [schema, selectedStepId, monitoredLanguage, onSchemaUpdate],
    );

    if (!params.projectId || !selectedStep) {
        return null;
    }

    if (isError) {
        showToast({
            type: 'error',
            title: 'Failed to fetch step task configuration!',
        });
        return null;
    }

    const handleOnStepNavigation = (stepId: string) => {
        const stepType = schema?.find(({ id }: any) => id === stepId)?.type;
        if (onStepChange && stepType) {
            onStepChange(stepId, stepType);
        }
    };

    const renderStepForm = () => {
        if (STEP_CAN_BE_RENDERED_WITHOUT_DATA.includes(selectedStep)) {
            switch (selectedStep) {
                case 'review_task_step': {
                    return (
                        <ReviewStepForm
                            values={selectedStepSchema?.values as ReviewTaskStepForm}
                            onChange={onFormChange}
                            data={data?.stepType === selectedStep ? data : undefined}
                            languages={languageByIndex ?? {}}
                            monitoredLanguage={monitoredLanguage}
                            onStepNavigation={handleOnStepNavigation}
                        />
                    );
                }
                case 'ai_mt_translation_step': {
                    return (
                        <AIMTStepForm
                            values={selectedStepSchema?.values as AiMtTranslationStepForm}
                            onChange={onFormChange}
                            data={data?.stepType === selectedStep ? data : undefined}
                            languages={languageByIndex ?? {}}
                            onStepNavigation={handleOnStepNavigation}
                            targetLanguageIds={targetLanguages}
                        />
                    );
                }
                case 'translate_task_step': {
                    return (
                        <TranslationStepForm
                            values={selectedStepSchema?.values as TranslateTaskStepForm}
                            onChange={onFormChange}
                            data={data?.stepType === selectedStep ? data : undefined}
                            languages={languageByIndex ?? {}}
                            onStepNavigation={handleOnStepNavigation}
                        />
                    );
                }
                case 'apply_tm_step':
                    return (
                        <TranslationMemoryStepForm
                            values={selectedStepSchema?.values as ApplyTmStepForm}
                            onChange={onFormChange}
                        />
                    );
                case 'clear_translations_step':
                    return <ClearTranslationStepForm />;
                case 'end_step':
                    return <EndStepForm />;
            }
        }

        if (data) {
            switch (selectedStep) {
                case 'trigger_step':
                    return (
                        <StartStepForm
                            isTemplateTriggerGatesEnabled={isTemplateTriggerGatesEnabled}
                            entitlements={entitlements}
                            branchName={window.preloadedState.branch.branchName}
                            documentOrContentProject={window.preloadedState.project.documentOrContentProject}
                            projectId={params.projectId as string}
                            values={selectedStepSchema?.values as ScheduledTriggerStepForm}
                            onChange={(formValues) => {
                                if (data.stepType === selectedStep) {
                                    onFormChange({
                                        ...formValues,
                                        monitoredLanguageId: data.baseProjectLanguage.id,
                                    });
                                }
                            }}
                            data={data.stepType === selectedStep ? data : undefined}
                        />
                    );
            }
        }

        return null;
    };

    return (
        <div
            className={clsx({
                [classes.container]: true,
                [classes.active]: open,
            })}
        >
            {isFetching ? <Loading /> : renderStepForm()}
        </div>
    );
};

export default StepConfigurationSidebar;
