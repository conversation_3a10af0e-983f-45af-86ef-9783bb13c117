import {
    mockDynamicAIMTValues,
    mockDynamicValues,
    mockGetWorkflowResponse,
    mockWorkflowsResponse,
} from '@app/externalApi/workflows/mockWorkflows';
import {
    mockGetResponse,
    mockPostResponse,
    renderWithProviders,
    resolveActErrors,
    screen,
    userEvent,
} from '@app/testing/utils';

import projectFilterDataFixture from '@app/components/AdvancedFilterDropdown/__fixtures__/filterData';
import { generateBranchStoreState, generateProjectStoreState } from '@app/features/project';
import WorkflowEditorProviders, {
    defaultValuesWorkflowEditor,
} from '@app/features/project/pages/Workflows/tests/WorkflowEditorProviders';
import { waitFor } from '@testing-library/react';
import WorkflowEditorPage from './WorkflowEditor.page';

jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useParams: () => ({ projectId: 'projectId', workflowId: 'workflowId' }),
    useLocation: () => ({
        pathname: '/workflows/projectId/workflow/workflowId/edit',
    }),
}));

jest.mock('../../hooks/clientApi/useWorkflowEntitlementsQuery', () => ({
    __esModule: true,
    default: () => ({
        data: {
            workflowsTemplateGateLevel: 1,
            workflowsNumberLimit: 10,
            isWorkflowsWeeklyTriggerAvailable: true,
        },
    }),
}));

const preloadedState = {
    team: {
        id: 'teamId',
        role: 'owner' as const,
        admin: true,
        name: 'Test Team',
        owners: [],
        projects: [{ id: 'projectId', name: 'Test Project' }],
    },
};

describe('WorkflowEditorPage', () => {
    const teamId = 'teamId';
    const groupId = `expert_${teamId}_projectId`;

    beforeEach(() => {
        window.preloadedState = {
            ...window.preloadedState,
            branch: generateBranchStoreState(),
            project: generateProjectStoreState(),
        };

        mockGetResponse('/projects/projectId/languages', {
            languages: {},
        });
        mockPostResponse('http://localhost/projects/projectId/actions/get-user-token', { accessToken: 'accessToken' });

        mockGetResponse('http://localhost/team-plan-entitlements/limit/workflows_number_limit', {
            limit: 10,
            current: 5,
        });

        mockGetResponse(`${window.env.maestroApiBaseUrl}/v1/owners/${teamId}/workflows/live`, {
            workflows: [],
        });

        mockGetResponse(`${window.env.maestroApiBaseUrl}/v1/entitlements/${teamId}`, {
            workflowsWeeklyTrigger: 1,
        });

        mockGetResponse(`http://localhost/team/current-plan`, {
            subscription: {
                planId: 152,
                planNickname: 'Essential',
            },
        });
    });

    it('show loading state', async () => {
        mockGetResponse(
            `${window.env.maestroApiBaseUrl}/v1/groups/${groupId}/workflows/workflowId`,
            mockWorkflowsResponse,
        );
        mockGetResponse('/projects/projectId/languages', {
            languages: {},
        });

        renderWithProviders(
            <WorkflowEditorProviders value={{ ...defaultValuesWorkflowEditor, workflowLoading: true }}>
                <WorkflowEditorPage />
            </WorkflowEditorProviders>,
            { preloadedState },
        );

        await resolveActErrors();

        expect(screen.getByRole('status', { name: 'Loading...' })).toBeInTheDocument();
    });

    it('hide workflow steps until schema is ready', async () => {
        mockGetResponse(
            `${window.env.maestroApiBaseUrl}/v1/groups/${groupId}/workflows/workflowId`,
            mockWorkflowsResponse,
        );

        renderWithProviders(
            <WorkflowEditorProviders
                value={{
                    ...defaultValuesWorkflowEditor,
                    workflow: mockGetWorkflowResponse,
                    currentSchema: undefined,
                    workflowLoading: false,
                }}
            >
                <WorkflowEditorPage />
            </WorkflowEditorProviders>,
            { preloadedState },
        );

        await waitFor(() => {
            expect(screen.queryByLabelText('Start step')).not.toBeInTheDocument();
        });
        expect(screen.queryByLabelText('Translation step')).not.toBeInTheDocument();
        expect(screen.queryByLabelText('Translation step')).not.toBeInTheDocument();
        expect(screen.queryByLabelText('Review step')).not.toBeInTheDocument();
        expect(screen.queryByLabelText('End step')).not.toBeInTheDocument();
    });

    it('show workflow steps', async () => {
        mockGetResponse(
            `${window.env.maestroApiBaseUrl}/v1/groups/${groupId}/workflows/workflowId`,
            mockWorkflowsResponse,
        );

        mockGetResponse(
            '/workflows/projects/projectId/step-configuration/ai_mt_translation_step/',
            mockDynamicAIMTValues,
        );

        renderWithProviders(
            <WorkflowEditorProviders
                value={{
                    ...defaultValuesWorkflowEditor,
                    workflow: mockGetWorkflowResponse,
                    currentSchema: mockGetWorkflowResponse.schema,
                    title: mockGetWorkflowResponse.title,
                    workflowLoading: false,
                }}
            >
                <WorkflowEditorPage />
            </WorkflowEditorProviders>,
            { preloadedState },
        );

        await waitFor(() => {
            expect(screen.queryByRole('status', { name: 'Loading...' })).not.toBeInTheDocument();
        });

        expect(screen.getByLabelText('Start step')).toBeInTheDocument();
        expect(screen.getAllByLabelText('Translation step')).toHaveLength(2);
        expect(screen.getByLabelText('Review step')).toBeInTheDocument();
        expect(screen.getByLabelText('End step')).toBeInTheDocument();
    });

    it('show update workflow steps', async () => {
        const user = userEvent.setup();
        mockGetResponse('/project/projectId/filter/data', { data: projectFilterDataFixture });
        mockGetResponse(
            `${window.env.maestroApiBaseUrl}/v1/groups/${groupId}/workflows/workflowId`,
            mockWorkflowsResponse,
        );
        mockGetResponse('/workflows/projects/projectId/step-configuration/trigger_step/', mockDynamicValues);
        mockGetResponse(`${window.env.maestroApiBaseUrl}/v1/entitlements/${teamId}`, {
            workflowsWeeklyTrigger: 1,
        });

        renderWithProviders(
            <WorkflowEditorProviders
                value={{
                    ...defaultValuesWorkflowEditor,
                    workflow: mockGetWorkflowResponse,
                    currentSchema: mockGetWorkflowResponse.schema,
                    title: mockGetWorkflowResponse.title,
                    workflowLoading: false,
                }}
            >
                <WorkflowEditorPage />
            </WorkflowEditorProviders>,
            { preloadedState },
        );

        // Wait for the workflow steps to be rendered
        await waitFor(() => {
            expect(screen.queryByRole('status', { name: 'Loading...' })).not.toBeInTheDocument();
        });

        // Verify workflow steps are rendered
        expect(screen.getByLabelText('Start step')).toBeInTheDocument();
        expect(screen.getAllByLabelText('Translation step')).toHaveLength(2);
        expect(screen.getByLabelText('Review step')).toBeInTheDocument();
        expect(screen.getByLabelText('End step')).toBeInTheDocument();
    });
});
