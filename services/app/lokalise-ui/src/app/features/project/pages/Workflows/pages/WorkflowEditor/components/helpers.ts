import {
    type FrontendLayoutStep,
    STEP_TYPES,
    type ScheduledTriggerStepForm,
    type StepType,
} from '@lokalise/maestro-expert-tenant-lib';

type LanguageAssignees = {
    languageId: number;
    userIds?: number[];
    userGroupIds?: number[];
    isSource: boolean;
};

export const updateLanguageAssignees = (
    currentAssignees: LanguageAssignees[],
    targetLanguageIds: number[],
    monitoredLanguage?: number,
) => {
    const assigneeMap = new Map(currentAssignees.map((assignee) => [assignee.languageId, assignee]));

    return targetLanguageIds.map(
        (languageId) =>
            assigneeMap.get(languageId) || {
                languageId,
                userIds: undefined,
                userGroupIds: undefined,
                isSource: languageId === monitoredLanguage,
            },
    );
};

export const STEP_CAN_BE_RENDERED_WITHOUT_DATA: Partial<StepType>[] = [
    STEP_TYPES[1],
    STEP_TYPES[2],
    STEP_TYPES[3],
    STEP_TYPES[4],
    STEP_TYPES[5],
    STEP_TYPES[6],
];

export const getDynamicDataRequestInfo = ({
    schema,
    selectedStepSchema,
}: {
    schema?: FrontendLayoutStep<string>[];
    selectedStepSchema?: FrontendLayoutStep<string>;
}) => {
    const scheduledTriggerStepForm = schema?.find(({ type }) => type === STEP_TYPES[0])
        ?.values as ScheduledTriggerStepForm;

    const hasTargetLanguages = !!scheduledTriggerStepForm?.targetLanguageIds?.length;

    const monitoredLanguage = scheduledTriggerStepForm?.monitoredLanguageId;

    const additionalQueryParamsRequired =
        !!selectedStepSchema &&
        STEP_CAN_BE_RENDERED_WITHOUT_DATA.includes(selectedStepSchema.type) &&
        !!monitoredLanguage &&
        hasTargetLanguages;

    const dynamicDataQuerySkipped =
        !selectedStepSchema ||
        (STEP_CAN_BE_RENDERED_WITHOUT_DATA.includes(selectedStepSchema.type) &&
            (!hasTargetLanguages || !monitoredLanguage));

    const targetLanguages = hasTargetLanguages ? scheduledTriggerStepForm.targetLanguageIds : [];

    return {
        additionalQueryParamsRequired,
        dynamicDataQuerySkipped,
        targetLanguages,
        monitoredLanguage,
    };
};
