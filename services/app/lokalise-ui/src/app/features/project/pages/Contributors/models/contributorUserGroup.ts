import { createMockFactoryWithDefaults } from '@app/utils/testing/createMockFactoryWithDefaults';

export type ContributorUserGroup = {
    id: string;
    name: string;
    avatar: string;
};

export const createMockContributorUserGroup = createMockFactoryWithDefaults<ContributorUserGroup>({
    id: '1',
    name: '<PERSON>',
    avatar: 'https://test.com/avatar.jpg',
});

export const createMockContributorUserGroupList = (count: number) =>
    Array.from({ length: count }, (_, index) =>
        createMockContributorUserGroup({
            id: index.toString(),
            name: `Group ${index}`,
            avatar: `https://test.com/avatar${index}.jpg`,
        }),
    );
