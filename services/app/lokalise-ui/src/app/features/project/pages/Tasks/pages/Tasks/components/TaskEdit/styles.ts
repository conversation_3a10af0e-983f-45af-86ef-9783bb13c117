import styled from '@app/styled';
import { color, spacing, typography } from '@app/theme';

export const Container = styled.div``;
export const InputWrapper = styled.div`
    width: 100%;
    margin-bottom: ${spacing(6)};
`;

export const LabelText = styled.span`
    ${typography('custom.descriptionMedium')};
    margin-bottom: ${spacing(1)};
    display: block;
    width: 100%;
`;

export const DatePickerWrapper = styled.div`
    width: 100%;
    max-width: 176px;
`;

export const DueDateWrapper = styled.div`
    display: flex;
    align-items: center;
    margin-top: ${spacing(1)};
`;

export const DatePickerReset = styled.div`
    margin-left: ${spacing(2)};
`;

export const CheckboxWrapper = styled.div`
    margin: ${spacing(1, 0)};
`;

export const NoResults = styled.div`
    ${typography('custom.description')};
    color: ${color('text.light.default')};
    padding: ${spacing(2, 3)};
`;
