import { mockDynamicValues, mockGetWorkflowResponse } from '@app/externalApi/workflows/mockWorkflows';
import { mockGetResponse, renderWithProviders, screen } from '@app/testing/utils';

import projectFilterDataFixture from '@app/components/AdvancedFilterDropdown/__fixtures__/filterData';
import { generateBranchStoreState, generateProjectStoreState } from '@app/features/project';
import { waitFor } from '@testing-library/react';
import WorkflowEditorProviders, { defaultValuesWorkflowEditor } from '../../../tests/WorkflowEditorProviders';
import StepConfigurationSidebar from './StepConfigurationSidebar';

jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useParams: () => ({ projectId: 'projectId', workflowId: 'workflowId' }),
    useLocation: () => ({
        pathname: '/workflows/projectId/workflow/workflowId/edit',
    }),
}));

const preloadedState = {
    team: {
        id: 'teamId',
        role: 'owner' as const,
        admin: true,
        name: 'Test Team',
        owners: [],
        projects: [{ id: 'projectId', name: 'Test Project' }],
    },
};

describe('StepConfigurationSidebar', () => {
    beforeEach(() => {
        mockGetResponse('/team/current-plan');
        mockGetResponse('/projects/projectId/languages', {
            languages: {},
        });

        window.preloadedState = {
            ...window.preloadedState,
            branch: generateBranchStoreState(),
            project: generateProjectStoreState(),
        };
    });

    it('show be closed', async () => {
        renderWithProviders(
            <WorkflowEditorProviders
                value={{
                    ...defaultValuesWorkflowEditor,
                    workflow: mockGetWorkflowResponse,
                    currentSchema: mockGetWorkflowResponse.schema,
                    title: mockGetWorkflowResponse.title,
                    workflowLoading: false,
                }}
            >
                <StepConfigurationSidebar open={false} />
            </WorkflowEditorProviders>,
            { preloadedState },
        );

        await waitFor(() => {
            expect(screen.queryByRole('tab', { name: 'Setup' })).not.toBeInTheDocument();
        });
    });

    it('show be opened with trigger step', async () => {
        mockGetResponse('/workflows/projects/projectId/step-configuration/trigger_step/', mockDynamicValues);
        mockGetResponse('/project/projectId/filter/data', { data: projectFilterDataFixture });

        renderWithProviders(
            <WorkflowEditorProviders
                value={{
                    ...defaultValuesWorkflowEditor,
                    workflow: mockGetWorkflowResponse,
                    currentSchema: mockGetWorkflowResponse.schema,
                    title: mockGetWorkflowResponse.title,
                    workflowLoading: false,
                }}
            >
                <StepConfigurationSidebar open={true} selectedStep="trigger_step" selectedStepId="TriggerStep" />
            </WorkflowEditorProviders>,
            { preloadedState },
        );

        expect(screen.getByRole('status', { name: 'Loading...' })).toBeInTheDocument();

        expect(await screen.findByRole('tab', { name: 'Setup' })).toBeInTheDocument();
        expect(screen.getByText('Step details (Start)')).toBeInTheDocument();
        expect(screen.getByText('Scheduled trigger settings')).toBeInTheDocument();
        expect(screen.getByText('Starts on')).toBeInTheDocument();

        expect(screen.getByText('Scope')).toBeInTheDocument();
        expect(screen.getByText('Target language(s)')).toBeInTheDocument();
        expect(screen.getByText('Monitored key type')).toBeInTheDocument();
        expect(screen.getByText('Filters (optional)')).toBeInTheDocument();
    });

    it('show be opened with review step', async () => {
        mockGetResponse('/workflows/projects/projectId/step-configuration/review_task_step/', mockDynamicValues);

        renderWithProviders(
            <WorkflowEditorProviders
                value={{
                    ...defaultValuesWorkflowEditor,
                    workflow: mockGetWorkflowResponse,
                    currentSchema: mockGetWorkflowResponse.schema,
                    title: mockGetWorkflowResponse.title,
                    workflowLoading: false,
                }}
            >
                <StepConfigurationSidebar open={true} selectedStep="review_task_step" selectedStepId="ReviewTaskStep" />
            </WorkflowEditorProviders>,
            { preloadedState },
        );

        expect(await screen.findByRole('tab', { name: 'Setup' })).toBeInTheDocument();
        expect(screen.getByText('Step details (Review task for contributor)')).toBeInTheDocument();
        expect(screen.getByText('Task title (optional)')).toBeInTheDocument();
        expect(screen.getByText('Task description (optional)')).toBeInTheDocument();

        expect(screen.getByText('Due date')).toBeInTheDocument();
    });

    it('show be opened with translation step', async () => {
        mockGetResponse('/workflows/projects/projectId/step-configuration/translate_task_step/', mockDynamicValues);

        renderWithProviders(
            <WorkflowEditorProviders
                value={{
                    ...defaultValuesWorkflowEditor,
                    workflow: mockGetWorkflowResponse,
                    currentSchema: mockGetWorkflowResponse.schema,
                    title: mockGetWorkflowResponse.title,
                    workflowLoading: false,
                }}
            >
                <StepConfigurationSidebar
                    open={true}
                    selectedStep="translate_task_step"
                    selectedStepId="TranslateTaskStep"
                />
            </WorkflowEditorProviders>,
            { preloadedState },
        );

        expect(await screen.findByRole('tab', { name: 'Setup' })).toBeInTheDocument();
        expect(screen.getByText('Step details (Translation task for contributor)')).toBeInTheDocument();
        expect(screen.getByText('Task title (optional)')).toBeInTheDocument();
        expect(screen.getByText('Task description (optional)')).toBeInTheDocument();

        expect(screen.getByText('Due date')).toBeInTheDocument();
    });
});
