import { type PropsWithChildren, useMemo, useState } from 'react';

import { useGetProjectLanguagesQuery } from '@app/api/projects';
import { useGetCurrentTeamPlanQuery } from '@app/api/team';

import useSelector from '@app/hooks/useSelector';
import { getProject } from '@app/selectors';
import type { Template, TemplateId } from '@lokalise/maestro-expert-tenant-lib';

import useGetTeamWorkflowsEntitlements from '../hooks/clientApi/useGetWorkflowsEntitlements';

import { WorkflowsContext } from './WorkflowsContext';

const WorkflowsContextProvider = ({ children }: PropsWithChildren) => {
    const { id } = useSelector(getProject);

    const [newWorkflowModalOpened, setNewWorkflowModalOpened] = useState(false);
    const [previewTemplate, setPreviewTemplate] = useState<Template<TemplateId>>();

    const { data } = useGetProjectLanguagesQuery(
        { projectId: id },
        {
            skip: !id,
        },
    );
    const { data: entitlements } = useGetTeamWorkflowsEntitlements();
    const { data: currentPlanData } = useGetCurrentTeamPlanQuery();
    const sourceLanguage = Object.values(data?.data.languages ?? {}).find(({ isDefault }) => isDefault);

    const value = useMemo(
        () => ({
            sourceLanguage,
            languageByIndex: data?.data.languages ?? {},
            newWorkflowModalOpened,
            setNewWorkflowModalOpened,
            previewTemplate,
            setPreviewTemplate,
            workflowsTemplateGateLevel: entitlements?.workflowsTemplateGateLevel ?? 1,
            workflowsNumberLimit: entitlements?.workflowsNumberLimit,
            subscription: currentPlanData?.subscription,
        }),
        [data, newWorkflowModalOpened, previewTemplate, entitlements, currentPlanData],
    );

    return <WorkflowsContext.Provider value={value}>{children}</WorkflowsContext.Provider>;
};

export { WorkflowsContextProvider };
