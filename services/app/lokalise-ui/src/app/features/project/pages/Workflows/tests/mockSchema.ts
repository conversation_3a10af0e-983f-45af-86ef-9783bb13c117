import type { FrontendLayoutStep } from '@lokalise/maestro-expert-tenant-lib';

export const mockValidSchema: FrontendLayoutStep<string>[] = [
    {
        id: 'TriggerStep',
        type: 'trigger_step',
        values: {
            stepTitle: 'Trigger and scope',
            monitoredKeyType: 'new',
            targetLanguageIds: [717],
            monitoredLanguageId: 1,
            scheduleFrequencyExpression: '00 09 */1 * *',
            scheduleStartingDate: new Date().toISOString().split('T')[0],
        },
        triggerType: 'Scheduled',
    },
    {
        id: 'TranslateTaskStep',
        type: 'translate_task_step',
        values: {
            stepTitle: 'Translation task for contributors',
            autoCloseTask: true,
            autoCloseItems: true,
            lockTranslations: false,
            languageAssignees: [{ languageId: 1, userIds: [1], isSource: false }],
            tagKeysAfterClose: false,
            autoCloseLanguages: false,
            tagsToAddAfterClose: [],
            customTranslationStatusIds: [],
            hasCustomTranslationStatuses: false,
        },
        taskType: 'translation',
    },
    {
        id: 'ReviewTaskStep',
        type: 'review_task_step',
        values: {
            stepTitle: 'Review task for contributors',
            autoCloseTask: true,
            lockTranslations: false,
            languageAssignees: [{ languageId: 717, userIds: [1], isSource: false }],
            tagKeysAfterClose: false,
            autoCloseLanguages: false,
            tagsToAddAfterClose: [],
            customTranslationStatusIds: [],
            hasCustomTranslationStatuses: false,
        },
        taskType: 'review',
    },
    {
        id: 'AiMtTranslationStep',
        type: 'ai_mt_translation_step',
        taskType: 'translation',
        values: {
            stepTitle: 'AI MT Translation Step',
            translationEngine: 'google',
            tagKeysAfterClose: false,
            tagsToAddAfterClose: [],
            customTranslationStatusIds: [],
            hasCustomTranslationStatuses: false,
        },
    },
    {
        id: 'EndStep',
        type: 'end_step',
        values: {},
    },
];

export const mockMissingDataInSchema: FrontendLayoutStep<string>[] = [
    {
        id: 'TriggerStep',
        type: 'trigger_step',
        values: {
            stepTitle: 'Invalid Trigger Step',
            monitoredKeyType: 'new',
            monitoredLanguageId: 1,
            targetLanguageIds: [],
            scheduleStartingDate: '',
            scheduleFrequencyExpression: '0 9 * * *',
        },
        triggerType: 'Scheduled',
    },
    {
        id: 'TranslateTaskStep',
        type: 'translate_task_step',
        values: {
            stepTitle: 'Invalid Translation Step',
            autoCloseTask: true,
            autoCloseItems: true,
            lockTranslations: false,
            languageAssignees: [{ languageId: 1, isSource: false }],
            tagKeysAfterClose: false,
            autoCloseLanguages: false,
            tagsToAddAfterClose: [],
            customTranslationStatusIds: [],
            hasCustomTranslationStatuses: false,
        },
        taskType: 'translation',
    },
    {
        id: 'ReviewTaskStep',
        type: 'review_task_step',
        values: {
            stepTitle: 'Invalid Review Step',
            autoCloseTask: true,
            lockTranslations: false,
            languageAssignees: [{ languageId: 717, isSource: false }],
            tagKeysAfterClose: false,
            autoCloseLanguages: false,
            tagsToAddAfterClose: [],
            customTranslationStatusIds: [],
            hasCustomTranslationStatuses: false,
        },
        taskType: 'review',
    },
    {
        id: 'EndStep',
        type: 'end_step',
        values: {},
    },
];

export const mockEmptyStepLabel: FrontendLayoutStep<string>[] = [
    {
        id: 'TriggerStep',
        type: 'trigger_step',
        values: {
            stepTitle: 'Trigger and scope',
            monitoredKeyType: 'new',
            targetLanguageIds: [717],
            monitoredLanguageId: 1,
            scheduleStartingDate: '',
            scheduleFrequencyExpression: '00 09 */1 * *',
        },
        triggerType: 'Scheduled',
    },
    {
        id: 'TranslateTaskStep',
        type: 'translate_task_step',
        values: {
            stepTitle: '',
            autoCloseTask: true,
            autoCloseItems: true,
            lockTranslations: false,
            languageAssignees: [{ languageId: 1, userIds: [1], isSource: false }],
            tagKeysAfterClose: false,
            autoCloseLanguages: false,
            tagsToAddAfterClose: [],
            customTranslationStatusIds: [],
            hasCustomTranslationStatuses: false,
        },
        taskType: 'translation',
    },
    {
        id: 'ReviewTaskStep',
        type: 'review_task_step',
        values: {
            stepTitle: '',
            autoCloseTask: true,
            lockTranslations: false,
            languageAssignees: [{ languageId: 717, userIds: [1], isSource: false }],
            tagKeysAfterClose: false,
            autoCloseLanguages: false,
            tagsToAddAfterClose: [],
            customTranslationStatusIds: [],
            hasCustomTranslationStatuses: false,
        },
        taskType: 'review',
    },
    {
        id: 'EndStep',
        type: 'end_step',
        values: {},
    },
];
