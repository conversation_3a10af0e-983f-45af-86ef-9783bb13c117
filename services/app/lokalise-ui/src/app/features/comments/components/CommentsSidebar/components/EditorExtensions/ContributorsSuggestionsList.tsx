import type { SuggestionType } from '@app/api/collaboration';

import { Flex } from '@lokalise/louis';
import MentionItem from './MentionItem';
import { MentionListGroupTitle } from './styles';

type ContributorsSuggestionsListProps = {
    suggestions: SuggestionType[];
    query: string;
    onClick: (event: { id: number; label: string }) => void;
    onMouseEnter: (hoverIndex: number) => void;
    hoverIndex: number;
    isSearching: boolean;
};

const gravatar = (hash: string) => `https://gravatar.com/avatar/${hash}?d=retro`;

const ContributorsSuggestionsList = ({
    suggestions,
    query,
    onClick,
    onMouseEnter,
    hoverIndex,
    isSearching,
}: ContributorsSuggestionsListProps) => {
    const isListEmpty = suggestions.length === 0;

    if (isListEmpty || isSearching) {
        return null;
    }

    return (
        <Flex gap={4} direction="column" fullWidth>
            <MentionListGroupTitle>Contributors</MentionListGroupTitle>
            {suggestions.map(({ userId, uid, value, gravatarHash }, index) => (
                <MentionItem
                    key={userId}
                    avatar={gravatar(gravatarHash)}
                    name={value}
                    email={uid}
                    query={query}
                    onClick={() => onClick({ id: userId, label: value })}
                    onMouseEnter={() => onMouseEnter(index)}
                    isActive={index === hoverIndex}
                />
            ))}
        </Flex>
    );
};

export default ContributorsSuggestionsList;
