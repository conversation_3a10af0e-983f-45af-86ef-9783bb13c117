import { type FC, type ReactNode, createContext, useContext, useMemo, useState } from 'react';

import noop from '@app/utils/noop';

export type CommentElevationContext = {
    isHeaderElevated: boolean;
    isFooterElevated: boolean;
    setIsHeaderElevated: (isElevated: boolean) => void;
    setIsFooterElevated: (isElevated: boolean) => void;
};

export const CommentElevationContext = createContext<CommentElevationContext>({
    isFooterElevated: false,
    isHeaderElevated: false,
    setIsFooterElevated: noop,
    setIsHeaderElevated: noop,
});

type CommentElevationContextProviderProps = {
    children: ReactNode;
};

export const CommentElevationContextProvider: FC<CommentElevationContextProviderProps> = ({ children }) => {
    const [isHeaderElevated, setIsHeaderElevated] = useState(false);
    const [isFooterElevated, setIsFooterElevated] = useState(false);

    const context = useMemo(
        () => ({
            isHeaderElevated,
            isFooterElevated,
            setIsHeaderElevated,
            setIsFooterElevated,
        }),
        [isHeaderElevated, isFooterElevated, setIsHeaderElevated, setIsFooterElevated],
    );

    return <CommentElevationContext.Provider value={context}>{children}</CommentElevationContext.Provider>;
};

export const useCommentElevationContext = () => useContext(CommentElevationContext);
