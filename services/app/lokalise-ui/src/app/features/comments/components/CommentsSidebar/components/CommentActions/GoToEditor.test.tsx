import { CommentList } from '@app/features/comments/components/CommentsSidebar/types';
import * as useDispatch from '@app/hooks/useDispatch';
import { fireEvent, renderWithProviders, screen, userEvent } from '@app/testing/utils';
import noop from '@app/utils/noop';

import { keyCommentsData } from '../../__fixtures__/comments.data';
import { generateCommentsStore, generateProjectStack } from '../../__fixtures__/commentsStore';
import { CommentContextProvider } from '../Comment';
import GoToEditor from './GoToEditor';

const comment = keyCommentsData[0];
const preloadedState = {
    comments: generateCommentsStore({
        commentListStack: [generateProjectStack({ projectId: comment.projectId })],
    }),
};

describe('<GoToEditor />', () => {
    it('should not show if no comment context', async () => {
        renderWithProviders(
            <CommentContextProvider value={{ comment: null, commentIndex: 0, editComment: noop, deleteComment: noop }}>
                <GoToEditor />
            </CommentContextProvider>,
            { preloadedState },
        );

        expect(screen.queryByLabelText('View key in editor')).not.toBeInTheDocument();
    });

    it('should show go to editor action button', async () => {
        renderWithProviders(
            <CommentContextProvider
                value={{ comment, commentIndex: undefined, editComment: noop, deleteComment: noop }}
            >
                <GoToEditor />
            </CommentContextProvider>,
            { preloadedState },
        );

        expect(await screen.findByLabelText('View key in editor')).toBeInTheDocument();
    });

    it('should show correct tooltip on hover', async () => {
        const user = userEvent.setup();

        renderWithProviders(
            <CommentContextProvider
                value={{ comment, commentIndex: undefined, editComment: noop, deleteComment: noop }}
            >
                <GoToEditor />
            </CommentContextProvider>,
            { preloadedState },
        );

        await user.hover(screen.getByLabelText('View key in editor'));

        expect(await screen.findByRole('tooltip', { name: 'View key in editor' })).toBeInTheDocument();
    });

    it('should dispatch commentFocusedInEditor action on click', async () => {
        const useDispatchSpy = jest.spyOn(useDispatch, 'default');
        const mockDispatchFn = jest.fn();
        useDispatchSpy.mockReturnValue(mockDispatchFn);
        renderWithProviders(
            <CommentContextProvider value={{ comment, commentIndex: 0, editComment: noop, deleteComment: noop }}>
                <GoToEditor />
            </CommentContextProvider>,
            { preloadedState },
        );

        fireEvent.click(await screen.findByLabelText('View key in editor'));

        expect(mockDispatchFn).toBeCalledWith({
            type: 'comments/commentFocusedInEditor',
            payload: { comment, nextListType: CommentList.Key, commentIndex: 0 },
        });
    });
});
