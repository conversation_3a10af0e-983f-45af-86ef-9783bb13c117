import { screen } from '@testing-library/react';
import { describe, expect, vi } from 'vitest';
import { renderWithProviders } from '../../../tests/utils';
import { TRANSLATION_ENGINES, TRANSLATION_ENGINE_DOCUMENTATION_LINK } from '../../utils/constants';
import { TranslationEngineErrorModal } from './TranslationEngineErrorModal';

describe('<TranslationEngineErrorModal />', () => {
	it('should render', () => {
		renderWithProviders(
			<TranslationEngineErrorModal
				engine={TRANSLATION_ENGINES[1]}
				unsupportedLanguages={[
					{
						id: 640,
						name: 'Russian',
						iso: 'ru',
						isDefault: false,
						ccIso: 'ru',
						isRtl: false,
						defaultPlural: null,
						overwrittenPlural: null,
					},
				]}
				onClose={vi.fn()}
			/>,
		);

		const modal = screen.getByRole('dialog');

		expect(modal).toBeInTheDocument();
		expect(screen.getByText('Unsupported languages by Google')).toBeInTheDocument();
		expect(
			screen.getByText(
				'Unfortunately, Google doesn’t support the following languages in this workflow:',
			),
		).toBeInTheDocument();
		expect(screen.getByText('Russian')).toBeInTheDocument();
		expect(
			screen.getByText(
				'Select a different engine or create a separate workflow for the unsupported languages.',
			),
		).toBeInTheDocument();
		expect(screen.getByText('I understand')).toBeInTheDocument();
	});

	it('should not render', () => {
		renderWithProviders(
			<TranslationEngineErrorModal
				engine={TRANSLATION_ENGINES[1]}
				unsupportedLanguages={[]}
				onClose={vi.fn()}
			/>,
		);

		expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
	});

	it('should render link', () => {
		renderWithProviders(
			<TranslationEngineErrorModal
				engine={TRANSLATION_ENGINES[1]}
				unsupportedLanguages={[
					{
						id: 640,
						name: 'Russian',
						iso: 'ru',
						isDefault: false,
						ccIso: 'ru',
						isRtl: false,
						defaultPlural: null,
						overwrittenPlural: null,
					},
				]}
				onClose={vi.fn()}
			/>,
		);

		const link = screen.getByRole('link');

		expect(link).toBeInTheDocument();
		expect(link).toHaveAttribute(
			'href',
			TRANSLATION_ENGINE_DOCUMENTATION_LINK[TRANSLATION_ENGINES[1].value],
		);
	});
});
