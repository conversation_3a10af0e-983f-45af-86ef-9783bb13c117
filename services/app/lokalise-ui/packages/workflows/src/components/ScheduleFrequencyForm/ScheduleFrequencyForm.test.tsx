import { screen } from '@testing-library/react';
import selectEvent from 'react-select-event';
import { expect, vi } from 'vitest';
import { renderWithProviders } from '../../../tests/utils';
import ScheduleFrequencyForm from './ScheduleFrequencyForm';

describe('<ScheduleFrequencyForm />', () => {
	it('should render with default values', () => {
		const onChange = vi.fn();

		renderWithProviders(
			<ScheduleFrequencyForm
				onChange={onChange}
				isTemplateTriggerGatesEnabled={false}
				entitlements={{
					isWorkflowsWeeklyTriggerAvailable: true,
					workflowsNumberLimit: 10,
					workflowsTemplateGateLevel: 1,
				}}
			/>,
		);

		expect(screen.getByText('Daily')).toBeInTheDocument();
		expect(screen.getByText('09:00 (UTC)')).toBeInTheDocument();

		expect(screen.queryAllByRole('checkbox')).toHaveLength(0);
	});

	it('should render with daily values prop', () => {
		const onChange = vi.fn();

		renderWithProviders(
			<ScheduleFrequencyForm
				value="30 14 * * *"
				onChange={onChange}
				isTemplateTriggerGatesEnabled={false}
				entitlements={{
					isWorkflowsWeeklyTriggerAvailable: true,
					workflowsNumberLimit: 10,
					workflowsTemplateGateLevel: 1,
				}}
			/>,
		);

		expect(screen.getByText('Daily')).toBeInTheDocument();
		expect(screen.getByText('14:30 (UTC)')).toBeInTheDocument();
	});

	it('should render with weekly values prop', () => {
		const onChange = vi.fn();

		renderWithProviders(
			<ScheduleFrequencyForm
				value="30 14 * * 1,2,3"
				onChange={onChange}
				isTemplateTriggerGatesEnabled={false}
				entitlements={{
					isWorkflowsWeeklyTriggerAvailable: true,
					workflowsNumberLimit: 10,
					workflowsTemplateGateLevel: 1,
				}}
			/>,
		);

		expect(screen.getByText('Weekly')).toBeInTheDocument();
		expect(screen.getByText('14:30 (UTC)')).toBeInTheDocument();
		const days = screen.getAllByRole('checkbox');
		const selectedDays = days.filter((day) => day.getAttribute('aria-checked') === 'true');
		expect(selectedDays).toHaveLength(3);
	});

	it('should report input changes', async () => {
		const onChange = vi.fn();

		renderWithProviders(
			<ScheduleFrequencyForm
				value={'30 14 * * *'}
				onChange={onChange}
				isTemplateTriggerGatesEnabled={false}
				entitlements={{
					isWorkflowsWeeklyTriggerAvailable: true,
					workflowsNumberLimit: 10,
					workflowsTemplateGateLevel: 1,
				}}
			/>,
		);

		const frequencyInput = screen.getByLabelText('scheduledFrequency');
		await selectEvent.select(frequencyInput, 'Weekly');

		expect(await screen.findByText('Weekly')).toBeInTheDocument();

		expect(onChange).toHaveBeenCalledWith('30 14 * * 1,2,3,4,5,6,0');
	});

	it('should disable weekly option when isTemplateTriggerGatesEnabled is true and workflowsWeeklyTrigger is 0', async () => {
		const onChange = vi.fn();

		renderWithProviders(
			<ScheduleFrequencyForm
				onChange={onChange}
				isTemplateTriggerGatesEnabled={true}
				entitlements={{
					isWorkflowsWeeklyTriggerAvailable: false,
					workflowsNumberLimit: 10,
					workflowsTemplateGateLevel: 1,
				}}
			/>,
		);

		const frequencyInput = screen.getByLabelText('scheduledFrequency');

		await selectEvent.openMenu(frequencyInput);

		const weeklyOption = screen.getByText('Weekly');
		expect(weeklyOption).toBeInTheDocument();

		expect(screen.getByText('Upgrade for access')).toBeInTheDocument();
	});

	it('should not disable weekly option when isTemplateTriggerGatesEnabled is false', async () => {
		const onChange = vi.fn();

		renderWithProviders(
			<ScheduleFrequencyForm
				onChange={onChange}
				isTemplateTriggerGatesEnabled={false}
				entitlements={{
					isWorkflowsWeeklyTriggerAvailable: true,
					workflowsNumberLimit: 10,
					workflowsTemplateGateLevel: 1,
				}}
			/>,
		);

		const frequencyInput = screen.getByLabelText('scheduledFrequency');

		await selectEvent.openMenu(frequencyInput);
		await selectEvent.select(frequencyInput, 'Weekly');

		expect(await screen.findByText('Weekly')).toBeInTheDocument();
		expect(onChange).toHaveBeenCalled();
		expect(screen.queryByText('Upgrade for access')).not.toBeInTheDocument();
	});

	it('should not disable weekly option when workflowsWeeklyTrigger is not 0', async () => {
		const onChange = vi.fn();

		renderWithProviders(
			<ScheduleFrequencyForm
				onChange={onChange}
				isTemplateTriggerGatesEnabled={true}
				entitlements={{
					isWorkflowsWeeklyTriggerAvailable: true,
					workflowsNumberLimit: 10,
					workflowsTemplateGateLevel: 1,
				}}
			/>,
		);

		const frequencyInput = screen.getByLabelText('scheduledFrequency');

		await selectEvent.openMenu(frequencyInput);
		await selectEvent.select(frequencyInput, 'Weekly');

		expect(await screen.findByText('Weekly')).toBeInTheDocument();
		expect(onChange).toHaveBeenCalled();
		expect(screen.queryByText('Upgrade for access')).not.toBeInTheDocument();
	});
});
