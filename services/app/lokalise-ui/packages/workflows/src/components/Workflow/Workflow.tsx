import { type ReactFlowProps, ReactFlowProvider, useNodesState, useEdgesState } from '@xyflow/react';

import type { CustomEdgeType, CustomNodeType } from '../../types';

import type { FrontendLayoutStep } from '@lokalise/maestro-expert-tenant-lib';
import '@xyflow/react/dist/style.css';

import { type RefAttributes, useEffect } from 'react';
import { generateLinearLayout } from '../../utils/helpers';
import classes from './Workflow.module.css';
import { WorkflowRenderer } from './WorkflowRenderer';

type WorkflowProps = {
	layout: FrontendLayoutStep[];
	isPreview?: boolean;
	customisable?: boolean;
	selectedNode?: string;
	onLayoutChange?: (nodes: CustomNodeType[], edges: CustomEdgeType[]) => void;
} & ReactFlowProps<CustomNodeType, CustomEdgeType> &
	RefAttributes<HTMLDivElement>;

const Workflow = ({
	layout,
	isPreview = false,
	customisable = false,
	selectedNode,
	onLayoutChange,
	...reactFlowProps
}: WorkflowProps) => {
	const { nodes: initialNodes, edges: initialEdges } = generateLinearLayout(
		layout,
		isPreview,
		customisable,
	);

	const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
	const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

	useEffect(() => {
		const { nodes: updatedNodes, edges: updatedEdges } = generateLinearLayout(
			layout,
			isPreview,
			customisable,
			selectedNode,
		);
		setNodes(updatedNodes);
		setEdges(updatedEdges);
	}, [layout, isPreview, setNodes, setEdges, selectedNode, customisable]);

	useEffect(() => {
		if (onLayoutChange) {
			onLayoutChange(nodes, edges);
		}
	}, [nodes, edges, onLayoutChange]);

	return (
		<div className={classes.providerContainer}>
			<ReactFlowProvider fitView nodeOrigin={[0.5, 0.5]}>
				<div className={classes.workflowContainer}>
					<WorkflowRenderer
						nodes={nodes}
						edges={edges}
						onNodesChange={onNodesChange}
						onEdgesChange={onEdgesChange}
						defaultNodes={initialNodes}
						defaultEdges={initialEdges}
						isPreview={isPreview}
						{...reactFlowProps}
					/>
				</div>
			</ReactFlowProvider>
		</div>
	);
};

export { Workflow, type WorkflowProps };
