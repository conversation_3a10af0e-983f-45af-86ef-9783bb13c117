import {
	AddIcon,
	Button,
	DeleteIcon,
	EditIcon,
	IconButton,
	Modal,
	ModalActions,
} from '@lokalise/louis';
import type { ScheduledTriggerStepForm } from '@lokalise/maestro-expert-tenant-lib';
import clsx from 'clsx';
import { useMemo, useState } from 'react';
import { WorkflowsScopeFilter } from '../../../../../src/app/features/project/pages/Tasks/pages/NewTask/pages/NewTaskScopePage/components/ScopeFilter/WorkflowsScopeFilter';
import type { Filter } from '../../../../../src/app/features/project/pages/Tasks/pages/NewTask/types';
import { useGetProjectFilterDataQuery } from '../../../../../src/app/api/tasks';
import {
	filterPresetToEditorData,
	prepareDocumentProjectFilterOptions,
	prepareFilterOptions,
	prepareFilterPresets,
} from '../../../../../src/app/features/project/pages/Tasks/pages/NewTask/pages/NewTaskScopePage/components/ScopeFilter/utils';
import type { FilterOptions } from '../../../../../src/app/components/CustomFilter';
import type { FilterPreset } from '../../../../../src/app/features/project/pages/Tasks/pages/NewTask/pages/NewTaskScopePage/components/ScopeFilter/types';
import classes from './AddFilters.module.css';

type AddFiltersProps = {
	branchName: string;
	documentOrContentProject: boolean;
	projectId: string;
	onChange: (filterId: ScheduledTriggerStepForm['projectFilterId']) => void;
	selectedFilterId?: string | null;
};

const AddFilters = ({
	branchName,
	documentOrContentProject,
	projectId,
	selectedFilterId,
	onChange,
}: AddFiltersProps) => {
	const { data, isLoading, isError } = useGetProjectFilterDataQuery({
		projectId,
		branchName,
		forWorkflows: true,
	});
	const [opened, setOpened] = useState(false);
	const [selectedFilter, setSelectedFilter] = useState<Filter>();
	const [isCustomEditorOpened, setIsCustomEditorOpened] = useState(false);
	const [isModalStacking, setIsModalStacking] = useState(false);

	const filterPresets: FilterPreset[] = useMemo(() => {
		if (data) {
			return prepareFilterPresets(data.data);
		}
		return [];
	}, [data]);

	const customFilterOptions: FilterOptions[] = useMemo(() => {
		if (data) {
			return documentOrContentProject
				? prepareDocumentProjectFilterOptions(prepareFilterOptions(data.data))
				: prepareFilterOptions(data.data);
		}
		return [];
	}, [data, documentOrContentProject]);

	const computedFilterData = useMemo(() => {
		if (selectedFilterId && data) {
			const selectedFilterData = filterPresets.find(({ id }) => id === selectedFilterId);
			if (selectedFilterData) {
				return {
					id: selectedFilterData.id,
					label: selectedFilterData.label,
					objects: filterPresetToEditorData(selectedFilterData, customFilterOptions),
				};
			}
		}
	}, [data, selectedFilterId, filterPresets, customFilterOptions]);

	const onChangeHandler = () => {
		onChange(selectedFilter?.id);
		setOpened(false);
	};

	const onCloseHandler = () => {
		setSelectedFilter(undefined);
		setOpened(false);
	};

	const onFilterRemove = () => {
		onChange(undefined);
		setSelectedFilter(undefined);
	};

	return (
		<>
			{computedFilterData ? (
				<div className={classes.manageFilterContainer}>
					<button
						className={classes.manageFilterButton}
						type="button"
						onClick={() => setOpened(true)}
					>
						{`Filter: ${computedFilterData.label}`}
					</button>
					<div className={classes.filterActions}>
						<IconButton ariaLabel="Edit filter" onClick={() => setOpened(true)}>
							<EditIcon />
						</IconButton>
						<IconButton ariaLabel="Remove filter" onClick={() => onFilterRemove()}>
							<DeleteIcon />
						</IconButton>
					</div>
				</div>
			) : (
				<Button
					className={classes.addFiltersButton}
					variant="secondary"
					onClick={() => setOpened(true)}
					loading={isLoading}
					leftIcon={<AddIcon size="16px" color="var(--lok-color-text-link-default)" />}
					disabled={isError}
				>
					{isError ? 'Filter currently not available' : 'Add filters'}
				</Button>
			)}
			<Modal
				size="wide"
				className={clsx([classes.filtersModal, isModalStacking && classes.hideFiltersModal])}
				title="Add Filters"
				opened={opened}
				onClose={() => setOpened(false)}
			>
				<p className={classes.description}>
					Tailor the scope processed by your workflow by adding filters. Choose a filter from the
					dropdown menu, or select “create a new filter...” for more filtering options.
				</p>
				<WorkflowsScopeFilter
					value={selectedFilter ? selectedFilter : computedFilterData}
					branchName={branchName}
					documentOrContentProject={documentOrContentProject}
					projectId={projectId}
					onChange={(filter: Filter | undefined, autoClose?: boolean) => {
						setSelectedFilter(filter);
						if (autoClose) {
							onChange(filter?.id);
							setOpened(false);
						}
					}}
					onEditorChange={setIsCustomEditorOpened}
					onModalStack={setIsModalStacking}
				/>
				{!isCustomEditorOpened && (
					<ModalActions>
						<Button variant="tertiary" onClick={() => onCloseHandler()}>
							Cancel
						</Button>
						<Button
							disabled={selectedFilter === undefined}
							variant="primary"
							onClick={() => onChangeHandler()}
						>
							Apply filter
						</Button>
					</ModalActions>
				)}
			</Modal>
		</>
	);
};

export default AddFilters;
