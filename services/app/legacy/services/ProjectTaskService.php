<?php

namespace Lokalise\Services;

use const LOKALISE_USER_ID;

use CommonService;
use DateTime;
use DateTimeImmutable;
use ElasticSearchService;
use Exception;
use KeyService;
use LanguageService;
use Lokalise\Adapter\DatabaseAdapterInterface;
use Lokalise\Adapter\MySql\TransactionIsolationLevel;
use Lokalise\Ai\Enum\AiFeatureType;
use Lokalise\Ai\Event\TeamAiWordsSpentEvent;
use Lokalise\Ai\Provider\AiFeatureStateProvider;
use Lokalise\AiBilling\Services\TeamAiUsageService;
use Lokalise\AutomaticTranslation\Service\AutomaticTranslationService;
use Lokalise\Client\Permission\PermissionClient;
use Lokalise\Client\StyleGuide\Dto\SearchStyleGuideItemData;
use Lokalise\Client\StyleGuide\Dto\SearchStyleGuideProjectData;
use Lokalise\Client\StyleGuide\StyleGuideClient;
use Lokalise\Client\TeamPlanEntitlements\TeamPlanFeatureClient;
use Lokalise\Client\TeamPlanEntitlements\TeamPlanLimitClient;
use Lokalise\Client\User\UserClient;
use Lokalise\Common\Exceptions\PublicException;
use Lokalise\Common\Team\ValueObject\TeamId;
use Lokalise\Common\TeamPlanEntitlements\Dto\Request\TeamPlanFeatureRequest;
use Lokalise\Common\TeamPlanEntitlements\Dto\Request\TeamPlanLimitRequest;
use Lokalise\Common\TeamPlanEntitlements\Enum\TeamPlanEntitlementsEnum;
use Lokalise\Common\User\ValueObject\UserId;
use Lokalise\Constants\DateFormat;
use Lokalise\Constants\ProjectTaskLanguageStatus;
use Lokalise\Constants\ProjectTaskStatus;
use Lokalise\Constants\ProjectTaskTranslationStatus;
use Lokalise\Constants\ProjectTaskType;
use Lokalise\Constants\TaskContributorType;
use Lokalise\Entity\MachineTranslation\Statistics\SourceEnum;
use Lokalise\Entity\Task\SearchUserGroupsParams;
use Lokalise\Entity\Task\SearchUsersParams;
use Lokalise\Enums\Feature;
use Lokalise\EventDispatcher\EventDispatcher;
use Lokalise\Events\Project\ProjectEventActionTypeEnum;
use Lokalise\Events\Project\Task\TaskCancelCloseLangReminderEvent;
use Lokalise\Events\Project\Task\TaskClosedEvent;
use Lokalise\Events\Project\Task\TaskCreatedEvent;
use Lokalise\Events\Project\Task\TaskDueDateChangedEvent;
use Lokalise\Events\Project\Task\TaskInitialTmLeverageCalculatedEvent;
use Lokalise\Events\Project\Task\TaskItemClosedEvent;
use Lokalise\Events\Project\Task\TaskItemReopenedEvent;
use Lokalise\Events\Project\Task\TaskKeysAddedEvent;
use Lokalise\Events\Project\Task\TaskKeysRemovedEvent;
use Lokalise\Events\Project\Task\TaskLanguageClosedEvent;
use Lokalise\Events\Project\Task\TaskLanguageContributorsAddedEvent;
use Lokalise\Events\Project\Task\TaskLanguageOpenedEvent;
use Lokalise\Events\Project\Task\TaskLockingChangedEvent;
use Lokalise\Events\Project\Task\TaskQueuedEvent;
use Lokalise\Events\Project\Task\TaskUnqueuedEvent;
use Lokalise\Exception\TranslationLockedException;
use Lokalise\Exceptions\ProjectTaskItemNotFoundException;
use Lokalise\Factory\ProjectDataFactory;
use Lokalise\FeatureFlag\Provider\FeatureStateProviderInterface;
use Lokalise\Helper\BackgroundProcessHelper;
use Lokalise\Helper\StringHelper;
use Lokalise\Helper\TranslationHelper;
use Lokalise\Lqa\Exception\AiTokenEstimationMissingException;
use Lokalise\Lqa\Exception\MonthlyAIWordsLimitWillExceededException;
use Lokalise\Managers\Tasks\TagManager;
use Lokalise\Models\Collection;
use Lokalise\Models\Parameters\ToggleTaskItemCompleteParams;
use Lokalise\Models\ProjectData\ProjectData;
use Lokalise\Models\Task;
use Lokalise\Models\TaskGroup;
use Lokalise\Models\TaskLanguage;
use Lokalise\Models\TaskUser;
use Lokalise\Models\Translation;
use Lokalise\Models\UserFilteredEmail;
use Lokalise\Proofread\UpdateProofreadStateParams;
use Lokalise\Queue\Rabbit\LegacyTaskName;
use Lokalise\Repository\Task\TaskItemRepository;
use Lokalise\Repository\Task\TaskRepository;
use Lokalise\Repository\TranslationRepository;
use Lokalise\Services\Mapper\TaskMapper;
use Lokalise\Services\Task\AbstractTaskService;
use Lokalise\Services\Task\TaskNotFoundException;
use Lokalise\Services\Task\TaskReportService;
use Lokalise\Structures\Tasks\CreateReviewSubTaskParams;
use Lokalise\Structures\Tasks\CreateTaskParams;
use Lokalise\Task\BackgroundProcess\Process\CreateTaskReindexBgProcess;
use Lokalise\Task\Collection\TaskIdCollection;
use Lokalise\Task\Enum\AiTaskLanguagesInBetaEnum;
use Lokalise\Task\Enum\AiTaskSupportedLanguagesEnum;
use Lokalise\Task\Enum\ProjectTaskSourceTypeEnum;
use Lokalise\Task\Event\ReviewTaskItemCompletedEvent;
use Lokalise\Task\Exception\AllKeysAreLockedOrHaveAssignedTasksException;
use Lokalise\Task\Exception\CannotCloseLanguageException;
use Lokalise\Task\Exception\NoKeysInsertedException;
use Lokalise\Task\Exception\NoKeysSelectedForMassActionException;
use Lokalise\Task\Exception\NotEnoughMachineTranslationCharactersException;
use Lokalise\Task\Logger\TaskLogger;
use Lokalise\Task\Repository\TaskTmLeverage\TaskTmLeverageRepository;
use Lokalise\Task\Service\KeyIdResolver;
use Lokalise\Task\Service\NotificationTaskUsersProvider;
use Lokalise\Task\Service\TaskFeatureStateProvider;
use Lokalise\Task\Service\TaskTranslationMemoryService;
use Lokalise\Task\ValueObject\TaskId;
use Lokalise\Task\ValueObject\TaskLanguageId;
use Lokalise\Task\View\InitialTmLeverageView;
use Lokalise\Task\View\TmLeverageView;
use Lokalise\TranslationMemory\Service\TranslationMemoryService;
use Lokalise\Utils\DateTimeProviderInterface;
use Lokalise\Utils\DateUtils;
use Lokalise\Utils\IntUtils;
use Lokalise\Utils\TaskUtil;
use Lokalise\Workflows\Exception\NoKeysLeftInProjectTaskScope;
use ProjectService;
use Psr\Log\LoggerInterface;
use RedisService;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface as SymfonyEventDispatcher;
use TagService;
use Throwable;

use function array_column;
use function array_merge;
use function array_unique;
use function array_values;
use function count;
use function in_array;
use function is_array;
use function is_null;

class ProjectTaskService extends AbstractTaskService
{
    private const FULL_DAY_IN_SECONDS = 24 * 3600;
    private const TM_LEVERAGE_PROGRESS_KEY_TTL = self::FULL_DAY_IN_SECONDS;
    private const MASTER_BRANCH_NAME = 'master';

    public function __construct(
        private readonly DatabaseAdapterInterface $db,
        TaskRepository $taskRepository,
        TaskItemRepository $taskItemRepository,
        private readonly EventDispatcher $eventDispatcher,
        private readonly TaskMapper $taskMapper,
        private readonly TranslationLockService $translationLockService,
        private readonly VendorRateProfileService $vendorRateProfileService,
        ProjectService $projectService,
        private readonly ElasticSearchService $elasticSearch,
        private readonly CommonService $commonService,
        private readonly KeyService $keyService,
        RedisService $redisService,
        private readonly TranslationRepository $translationRepository,
        TranslationMemoryService $translationMemoryService,
        private readonly TaskReportService $taskReportService,
        TagService $tagService,
        PlanService $planService,
        ProjectDataFactory $pdFactory,
        private readonly KeySelectionService $keySelectionService,
        private readonly RabbitQueueService $queueService,
        private readonly AccessService $accessService,
        private readonly UserClient $userClient,
        private readonly NotificationTaskUsersProvider $notificationTaskUsersProvider,
        KeyIdResolver $keyIdResolver,
        private readonly LoggerInterface $logger,
        private readonly DateTimeProviderInterface $dateTimeProvider,
        private readonly TeamService $teamService,
        private readonly StyleGuideClient $styleGuideClient,
        private readonly LanguageService $languageService,
        private readonly TeamAiUsageService $teamAiUsageService,
        FeatureStateProviderInterface $featureProvider,
        TaskTmLeverageRepository $taskTmLeverageRepository,
        PermissionClient $permissionClient,
        private readonly RedisLockService $redisLockService,
        private readonly TaskTranslationMemoryService $taskTranslationMemoryService,
        TaskFeatureStateProvider $taskFeatureStateProvider,
        private readonly CustomTranslationStatusesService $customTranslationStatusesService,
        private readonly SymfonyEventDispatcher $symfonyEventDispatcher,
        private readonly TeamPlanFeatureClient $teamPlanFeatureClient,
        private readonly TeamPlanLimitClient $teamPlanLimitClient,
        private readonly MachineTranslationStatisticsService $machineTranslationStatisticsService,
        private readonly TaskLogger $taskLogger,
        protected readonly AiFeatureStateProvider $aiFeatureStateProvider,
        private readonly BackgroundProcessService $backgroundProcessService,
    ) {
        parent::__construct(
            $taskRepository,
            $taskItemRepository,
            $tagService,
            $planService,
            $translationMemoryService,
            $projectService,
            $pdFactory,
            $keyIdResolver,
            $featureProvider,
            $permissionClient,
            $taskFeatureStateProvider,
            $redisService,
            $taskTmLeverageRepository
        );
    }

    public function getExistingTemplateTitles(string $projectId): array
    {
        return $this->taskRepository->getExistingTemplateTitlesLowercased($projectId);
    }

    /**
     * Creates a sub-task of type "review" for either "review" or "translation" task
     *
     * @throws Throwable
     */
    public function createReviewSubTask(CreateReviewSubTaskParams $params): int
    {
        $availableParentTaskIds = $this->getAvailableParentTaskIds($params->projectId);

        if (!in_array($params->parentTaskId, $availableParentTaskIds)) {
            throw new Exception('Parent task not available');
        }

        $oldTranslations = $this->taskRepository->getTaskTranslationDataForChildTask($params->parentTaskId);
        $oldTranslationsArray = $this->commonService->groupData($oldTranslations, 'lang_id', null, true);

        $oldTaskLanguages = $this->taskRepository->getTaskLanguageDataForChildTask($params->parentTaskId);
        $oldTaskLanguageArray = $this->commonService->groupData($oldTaskLanguages, 'lang_id', null, true, true);

        $isTaskLocksAvailable = $this->teamPlanFeatureClient->isFeatureAvailable(
            new TeamPlanFeatureRequest(
                teamId: new TeamId($params->teamId),
                featureId: Feature::TaskTranslationLocks,
            )
        );

        if (false === $isTaskLocksAvailable) {
            $params->doLockTranslations = 0;
        } elseif ($this->projectService->isContributorLockOptionEnabled($params->projectId)) {
            $params->doLockTranslations = 1;
        }

        $doLinkVendorCards = false;

        $eligibleToVendorCardLink = $this->isEligibleToVendorCardLink($params->teamId, $params->vendorCardsToLink);
        if ($eligibleToVendorCardLink) {
            $doLinkVendorCards = true;
        }

        $this->db->startTransaction();

        try {
            $parentTask = $this->getTaskByIdBasic($params->parentTaskId);

            $parentTaskCompletedLanguageIds = $this->getCompletedTaskLanguageIds($params->parentTaskId);

            $taskId = $this->taskRepository->insert([
                'project_id' => $params->projectId,
                'title' => $params->title,
                'description' => $params->description,
                'created_by' => $_SESSION['logged']['id'],
                'due_date' => empty($params->dueDate) ? null : $params->dueDate,
                'auto_close_items' => $parentTask['auto_close_items'],
                'autoclose_languages' => $parentTask['autoclose_languages'],
                'autoclose_task' => $parentTask['autoclose_task'],
                'keys' => $parentTask['keys'],
                'words' => $parentTask['words'],
                'repetitions' => $parentTask['repetitions'],
                'status' => empty($parentTaskCompletedLanguageIds) ? ProjectTaskStatus::Queued : ProjectTaskStatus::InProgress,
                'task_type' => ProjectTaskType::Review,
                'parent_task_id' => $params->parentTaskId,
                'do_lock_translations' => $params->doLockTranslations,
                'is_cts_enabled' => $params->hasCustomTranslationStatuses,
                'template_task_id' => $params->templateTaskId,
                'source_lang_id' => $parentTask['source_lang_id'],
            ]);

            if ($params->hasCustomTranslationStatuses) {
                $this->attachCustomTranslationStatusesToTask($taskId, $params->customTranslationStatusIds);
            }

            $akeyIds = [];

            foreach ($params->languages as $lang) {
                $langId = $lang['id'];

                $oldTranslations = $oldTranslationsArray[$langId] ?? [];

                $translationsToInsert = [];
                foreach ($oldTranslations as $oldTranslation) {
                    $translationsToInsert[] = [
                        'project_task_id' => $taskId,
                        'akey_id' => $oldTranslation['akey_id'],
                        'segment_number' => $oldTranslation['segment_number'],
                        'translation_id' => $oldTranslation['translation_id'],
                        'lang_id' => $langId,
                        'words' => $oldTranslation['words'],
                    ];

                    $akeyIds[] = $oldTranslation['akey_id'];
                }

                $this->taskRepository->insertTaskTranslations($translationsToInsert);

                $oldTaskLanguage = $oldTaskLanguageArray[$langId] ?? [];

                $taskLanguageId = $this->taskRepository->insertTaskLanguage([
                    'project_task_id' => $taskId,
                    'lang_id' => $langId,
                    'keys' => $oldTaskLanguage['keys'],
                    'words' => $oldTaskLanguage['words'],
                    'status' => in_array($langId, $parentTaskCompletedLanguageIds) ? ProjectTaskLanguageStatus::Created : ProjectTaskLanguageStatus::Queued,
                ]);

                // Insert users
                $usersToInsert = [];
                foreach ($lang['contributors']['users'] as $userId) {
                    $usersToInsert[] = [
                        'project_task_lang_id' => $taskLanguageId,
                        'lang_id' => $langId,
                        'user_id' => $userId,
                    ];
                }
                $this->taskRepository->insertTaskLanguageUsers($usersToInsert);

                // Insert user groups
                $groupsToInsert = [];
                foreach ($lang['contributors']['groups'] as $groupId) {
                    $groupsToInsert[] = [
                        'project_task_lang_id' => $taskLanguageId,
                        'lang_id' => $lang['id'],
                        'user_group_id' => $groupId,
                    ];
                }
                $this->taskRepository->insertTaskLanguageUserGroups($groupsToInsert);
            }

            (new TagManager($taskId, $_SESSION['logged']['id']))
                ->createTagsForEvent(TaskClosedEvent::EVENT_TYPE_ID, $params->tagsToAddAfterClose);

            if ($doLinkVendorCards) {
                $this->vendorRateProfileService->saveProjectTaskData(
                    $taskId,
                    $params->title,
                    ProjectTaskType::Review,
                    $params->projectId,
                    $parentTask['source_lang_id']
                );
                $this->vendorRateProfileService->linkCardsToTaskFromSelectedRateCards(
                    $taskId,
                    $params->vendorCardsToLink
                );
            }

            $this->db->commit();
        } catch (Throwable $e) {
            $this->db->rollback();

            throw $e;
        }

        $this->elasticSearch->reindexOne(
            projectIds: [$params->projectId],
            keyIds: array_unique($akeyIds),
            origin: __METHOD__,
        );

        $this->cloneMaxTmLeverage($params->parentTaskId, $taskId);

        if (empty($parentTaskCompletedLanguageIds)) {
            $this->eventDispatcher->dispatch(
                new TaskQueuedEvent(
                    $this->taskMapper->mapTaskToEventEntity($this->getTaskById($taskId))
                ),
                TaskQueuedEvent::EVENT_NAME
            );
        } else {
            $this->eventDispatcher->dispatch(
                new TaskCreatedEvent(
                    $this->taskMapper->mapTaskToEventEntity($this->getTaskById($taskId))
                ),
                TaskCreatedEvent::EVENT_NAME
            );
        }

        return $taskId;
    }

    /**
     * @throws AllKeysAreLockedOrHaveAssignedTasksException|NoKeysLeftInProjectTaskScope|NoKeysInsertedException
     * @throws AiTokenEstimationMissingException|NoKeysSelectedForMassActionException
     * @throws MonthlyAIWordsLimitWillExceededException|NotEnoughMachineTranslationCharactersException
     * @throws Throwable
     *
     * @return int ID of created task
     */
    public function createTask(CreateTaskParams $params): int
    {
        $this->taskLogger->logCreatingTask($params);

        $isTaskLocksAvailable = $this->teamPlanFeatureClient->isFeatureAvailable(
            new TeamPlanFeatureRequest(
                teamId: new TeamId($params->teamId),
                featureId: Feature::TaskTranslationLocks,
            )
        );

        if (false === $isTaskLocksAvailable) {
            $params->doLockTranslations = 0;
        } elseif ($this->projectService->isContributorLockOptionEnabled($params->projectId)) {
            $params->doLockTranslations = 1;
        }

        $doLinkVendorCards = false;

        $eligibleToVendorCardLink = $this->isEligibleToVendorCardLink($params->teamId, $params->vendorCardsToLink);
        if ($eligibleToVendorCardLink) {
            $doLinkVendorCards = true;
        }

        $massKeysCount = 0;
        $keysByLanguages = [];

        // store all the unique key ids
        if ($params->keysByLanguages !== null) {
            $keysByLanguages = $params->keysByLanguages;
            $uniqueKeyIds = array_unique(array_merge(...array_map(static fn ($langData) => $langData['keys'], $keysByLanguages)));
        } else {
            if ($params->keys !== null) {
                $uniqueKeyIds = array_unique($params->keys);
            } elseif ($params->isMass) {
                $uniqueKeyIds = $this->keySelectionService->getProjectSelectedKeys($params->projectId);
                $massKeysCount = count($uniqueKeyIds);

                if ($massKeysCount === 0) {
                    throw new NoKeysSelectedForMassActionException('No keys selected for mass action');
                }
            } else {
                $keysByLanguages = $this->getKeysSortedByLanguage($params);
                $uniqueKeyIds = $this->getUniqueKeyIdsFromKeysByLanguages($keysByLanguages);
            }
        }

        // We use this array to calculate AI task credits (try to build it if not built already (in case of isMass or api))
        if (!empty($params->languages) && empty($keysByLanguages)) {
            $keysByLanguages = $this->getKeysSortedByLanguage($params, $uniqueKeyIds);
        }

        // Workflows-specific use case, we should only include keys with non-empty source and empty target translations
        if (
            $params->sourceType === ProjectTaskSourceTypeEnum::Maestro
            && $params->type === ProjectTaskType::AutomaticTranslationByAi
            && $params->translationEngine !== null
        ) {
            $keysByLanguages = $this->filterOutEmptyKeysWithEmptySourceTranslationOrNonEmptyTargetTranslation(
                $keysByLanguages,
                $params->sourceLangId,
            );
            $uniqueKeyIds = $this->getUniqueKeyIdsFromKeysByLanguages($keysByLanguages);

            if (empty($uniqueKeyIds)) {
                throw new NoKeysLeftInProjectTaskScope();
            }
        }

        // add logging to identify issue with empty keys array
        if (
            in_array($params->type, [ProjectTaskType::LqaByAi, ProjectTaskType::AutomaticTranslationByAi], true)
            && array_filter($keysByLanguages, static fn ($keys) => empty($keys['keys']))
        ) {
            $this->logger->warning('Empty keys detected for AI task', [
                'params' => $params,
                'keysByLanguages' => $keysByLanguages,
                'uniqueKeyIds' => $uniqueKeyIds,
                'massKeysCount' => $massKeysCount,
                'uniqueKeyIdsCount' => count($uniqueKeyIds),
            ]);
        }

        $this->db->startTransaction(TransactionIsolationLevel::ReadCommitted);

        // if this is AI task - we need to check if we go over limit
        $estimatedNumberOfTokens = 0;
        if (
            $params->type === ProjectTaskType::LqaByAi ||
            ($params->type === ProjectTaskType::AutomaticTranslationByAi && !$params->isMachineTranslationTask())
        ) {
            foreach ($keysByLanguages as $langId => $keys) {
                $totalWordsInSourceLanguage = $this->translationRepository
                    ->getWordCountByLanguageAndKeys($params->sourceLangId, $keys['keys']);
                $totalWordsInTargetLanguage = $this->translationRepository
                    ->getWordCountByLanguageAndKeys((int) $langId, $keys['keys']);

                $estimatedNumberOfTokens += $this->teamAiUsageService->calculateUsageUnits(
                    $totalWordsInSourceLanguage,
                    $totalWordsInTargetLanguage,
                    $params->type
                );
            }

            if (0 === $estimatedNumberOfTokens) {
                throw new AiTokenEstimationMissingException();
            }

            if ($this->teamAiUsageService->checkIfTeamWillHaveOverlimitMonthlyAiWordsUsage($params->teamId, $estimatedNumberOfTokens)) {
                throw new MonthlyAIWordsLimitWillExceededException();
            }
        }

        if ($params->isMachineTranslationTask()) {
            // For MT tasks, we need to ensure that the new task will not consume more MT characters than the team has
            $estimatedMtCharactersNeeded = 0;
            foreach ($keysByLanguages as $keys) {
                $estimatedMtCharactersNeeded += $this->translationRepository
                    ->getCharactersCountByLanguageAndKeys($params->sourceLangId, $keys['keys']);
            }

            $mtCharactersLimit = $this->teamPlanLimitClient->getTeamPlanLimit(
                new TeamPlanLimitRequest(
                    teamId: new TeamId($params->teamId),
                    limit: TeamPlanEntitlementsEnum::AUTOMATION_LIMIT_MT_CHARS
                )
            );

            $mtCharactersUsage =
                $this->machineTranslationStatisticsService->countTeamSourceUsageThisMonth(
                    $params->teamId,
                    SourceEnum::AUTOMATION,
                );
            $mtCharactersLeft = $mtCharactersLimit - $mtCharactersUsage;

            if ($estimatedMtCharactersNeeded > $mtCharactersLeft) {
                $this->logger->error(
                    'There are not enough MT characters to create an MT task.',
                    [
                        'mt_characters_usage' => $mtCharactersUsage,
                        'mt_characters_limit' => $mtCharactersLimit,
                        'estimated_mt_characters_needed' => $estimatedMtCharactersNeeded,
                    ]
                );

                throw new NotEnoughMachineTranslationCharactersException();
            }
        }

        $templateTitle = $params->templateTitle ?: null;

        $userId = $_SESSION['logged']['id'];

        try {
            $taskId = $this->taskRepository->insert([
                'project_id' => $params->projectId,
                'title' => $params->title,
                'description' => $params->description,
                'created_by' => $userId,
                'due_date' => empty($params->dueDate) ? null : $params->dueDate,
                'auto_close_items' => $params->autoCloseItems ?: 0,
                'autoclose_languages' => $params->autoCloseLanguages ?: 0,
                'autoclose_task' => $params->autoCloseTask ?: 0,
                'task_type' => $params->type,
                'do_lock_translations' => $params->doLockTranslations,
                'is_cts_enabled' => $params->hasCustomTranslationStatuses,
                'template_task_id' => $params->templateTaskId,
                'template_title' => $templateTitle,
                'source_lang_id' => $params->sourceLangId,
                'source_type' => $params->sourceType?->value,
                'correlation_id' => $params->correlationId,
                'task_actor' => $params->translationEngine?->value,
            ]);
            $this->logTaskCreation($taskId, $params->projectId, $params->title);

            if ($params->hasCustomTranslationStatuses) {
                $this->attachCustomTranslationStatusesToTask($taskId, $params->customTranslationStatusIds);
            }

            $this->applyAiTranslationTaskOptions($params, $taskId);

            $totalTranslationsInserted = 0;
            $hasFilteredOutKeys = false;
            foreach ($params->languages as $lang) {
                if ($params->keys !== null) {
                    $languageKeys = [
                        'key_count' => count($params->keys),
                        'keys' => $params->keys,
                    ];
                } elseif ($params->isMass) {
                    $languageKeys = [
                        'key_count' => $massKeysCount,
                        'keys' => $uniqueKeyIds,
                    ];
                } else {
                    $languageKeys = $keysByLanguages[$lang['id']];
                }

                $keysBeforeFilter = $languageKeys['key_count'];
                $languageKeys = $this->getAvailableTranslationsForLanguage($lang['id'], $languageKeys, $params->projectId);
                // getAvailableTranslationsForLanguage just filters out
                $hasFilteredOutKeys = $hasFilteredOutKeys || ($keysBeforeFilter - $languageKeys['key_count']) > 0;

                $contributorUserIds = array_unique($lang['contributors']['users'] ?? []);
                $contributorGroupIds = array_unique($lang['contributors']['groups'] ?? []);

                if (empty($languageKeys['keys']) || (empty($contributorUserIds) && empty($contributorGroupIds))) {
                    continue;
                }

                $translationsToInsert = [];
                foreach ($languageKeys['keys'] as $key) {
                    $translationsToInsert[] = [
                        'project_task_id' => $taskId,
                        'akey_id' => $key['akey_id'],
                        'translation_id' => $key['id'],
                        'segment_number' => $key['segment_number'],
                        'lang_id' => $lang['id'],
                        'words' => 0, // calculated afterwards
                    ];
                    $totalTranslationsInserted++;
                }

                $this->taskRepository->insertTaskTranslations($translationsToInsert);

                $taskLangInsert = [
                    'project_task_id' => $taskId,
                    'lang_id' => $lang['id'],
                    'keys' => 0, // calculated afterwards
                    'words' => 0, // calculated afterwards
                ];

                $taskLangId = $this->taskRepository->insertTaskLanguage($taskLangInsert);

                // Insert users
                $usersToInsert = [];
                foreach ($contributorUserIds as $userId) {
                    $usersToInsert[] = [
                        'project_task_lang_id' => $taskLangId,
                        'lang_id' => $lang['id'],
                        'user_id' => $userId,
                    ];
                }

                $this->taskRepository->insertTaskLanguageUsers($usersToInsert);

                // Insert user groups
                $groupsToInsert = [];
                foreach ($contributorGroupIds as $groupId) {
                    $groupsToInsert[] = [
                        'project_task_lang_id' => $taskLangId,
                        'lang_id' => $lang['id'],
                        'user_group_id' => $groupId,
                    ];
                }
                $this->taskRepository->insertTaskLanguageUserGroups($groupsToInsert);
            }

            (new TagManager($taskId, $_SESSION['logged']['id']))
                ->createTagsForEvent(TaskClosedEvent::EVENT_TYPE_ID, $params->tagsToAddAfterClose);

            if ($totalTranslationsInserted > 0) {
                $this->keySelectionService->clearProjectSelectedKeys($params->projectId);
            } elseif ($hasFilteredOutKeys === true) {
                throw new AllKeysAreLockedOrHaveAssignedTasksException();
            } else {
                throw new NoKeysInsertedException();
            }

            if ($doLinkVendorCards) {
                $this->vendorRateProfileService->saveProjectTaskData(
                    $taskId,
                    $params->title,
                    $params->type,
                    $params->projectId,
                    $params->sourceLangId
                );
                $this->vendorRateProfileService->linkCardsToTaskFromSelectedRateCards(
                    $taskId,
                    $params->vendorCardsToLink
                );
            }

            $this->db->commit();
        } catch (Throwable $e) {
            $this->db->rollback();

            throw $e;
        }

        $this->recalculateKeyAndWordCount($taskId, $params->sourceLangId);

        if ($params->type == ProjectTaskType::LqaByAi) {
            // For the LQAI task we need to display count of words from target language,
            // so we update project_task_translation records with target lang words count
            $dataToUpdate = [];
            foreach (array_keys($keysByLanguages) as $lang) {
                $langTranslations = $this->translationRepository->findByKeyIdsAndLanguageIdForTasks($uniqueKeyIds, $lang);
                foreach ($langTranslations as $langTranslation) {
                    $wordCount = StringHelper::countWordsKeyType((string) $langTranslation['translation'], (bool) $langTranslation['plural']);
                    $dataToUpdate[] = [
                        'akey_id' => $langTranslation['akey_id'],
                        'words' => $wordCount,
                        'segment_number' => $langTranslation['segment_number'],
                        'lang_id' => $langTranslation['lang_id'],
                    ];
                }

                $this->taskRepository->updateLqaAiTaskTranslationBaseWords($taskId, $dataToUpdate);
            }
        }

        $this->calculateMaxTmLeverage(
            $params->teamId,
            $params->projectId,
            $taskId,
            $params->languages,
            $uniqueKeyIds,
            $params->sourceLangId
        );

        $projectData = $this->pdFactory->createFromUnknownId($params->projectId);
        $groupId = BackgroundProcessHelper::getGroupIdFromProjectData($projectData);
        $this->backgroundProcessService->queueProcessWithArguments(
            processName: CreateTaskReindexBgProcess::TYPE,
            groupId: $groupId,
            userId: $userId,
            params: [
                'project_id' => $projectData->getMasterProjectId(),
                'branch_id' => $projectData->getBranchId(),
                'task_id' => $taskId,
                'key_ids' => $uniqueKeyIds,
                'source_lang_id' => $params->sourceLangId,
            ],
        );

        $updatedTask = $this->getTaskById($taskId);
        $this->eventDispatcher->dispatch(
            new TaskCreatedEvent(
                $this->taskMapper->mapTaskToEventEntity($updatedTask)
            ),
            TaskCreatedEvent::EVENT_NAME
        );

        if (
            $params->type === ProjectTaskType::LqaByAi
            || ($params->type === ProjectTaskType::AutomaticTranslationByAi && !$params->isMachineTranslationTask())
        ) {
            $this->teamAiUsageService->lockRedisUsage($params->teamId, $estimatedNumberOfTokens, $taskId);

            $aiFeature = match ($params->type) {
                ProjectTaskType::LqaByAi => AiFeatureType::LQA,
                default => AiFeatureType::TRANSLATION_TASK,
            };
            $sourceLangData = $this->languageService->getById($params->sourceLangId);
            $event = new TeamAiWordsSpentEvent(
                teamId: (int) $params->teamId,
                feature: $aiFeature,
                wordsCount: (int) $estimatedNumberOfTokens,
                teamMonthlyLimit: $this->teamAiUsageService->getTeamAvailableAiUnit((int) $params->teamId),
                teamMonthlyLimitUsed: $this->teamAiUsageService->getTeamSpentUnit((int) $params->teamId),
                language: $sourceLangData['cc_iso'] ?? null,
            );
            $this->eventDispatcher->dispatch($event, TeamAiWordsSpentEvent::EVENT_NAME);
        }

        return $taskId;
    }

    private function isEligibleToVendorCardLink(int $teamId, array $vendorCardsToLink): bool
    {
        $isFeatureAvailable = $this->vendorRateProfileService->isFeatureAvailable($teamId);
        $hasAccessToLinkTasks = $this->vendorRateProfileService->hasAccessToLinkTasks($teamId, $vendorCardsToLink);

        return
            $teamId
             && $isFeatureAvailable
             && $hasAccessToLinkTasks;
    }

    public function updateTask(
        $projectId,
        $taskId,
        $title,
        $description,
        ?string $dueDate,
        $doLockTranslations,
        $autoCloseItems = null,
        $autoCloseLanguages = null,
        $autoCloseTask = null,
        array $closingTags = []
    ) {
        $userId = $this->accessService->getUserId();
        if (!$this->isTaskAdmin($taskId, $userId)) {
            return;
        }

        $task = $this->getTaskByIdBasic($taskId);

        $oldDueDate = $this->prepareDueDate($task['due_date']);
        $newDueDate = $this->prepareDueDate($dueDate);

        $shouldLockTranslationsOriginal = (int) $this->shouldCheckLocks($taskId, false, $task);

        $teamId = $this->projectService->getTeamId($projectId);
        $projectData = $this->pdFactory->createFromUnknownId($projectId);

        $isTaskLocksAvailable = $this->planService->isFeatureAvailable(Feature::TaskTranslationLocks, $teamId);
        if ($isTaskLocksAvailable && $this->projectService->isContributorLockOptionEnabled($projectId)) {
            $doLockTranslations = 1;
        }

        $data = [
            'title' => $title,
            'description' => $description,
            'due_date' => $newDueDate?->format(DateFormat::ISO_DATE_TIME),
            'do_lock_translations' => $isTaskLocksAvailable ? (int) (bool) $doLockTranslations : 0,
        ];

        if (!is_null($autoCloseItems)) {
            $data['auto_close_items'] = $autoCloseItems ? 1 : 0;
        }

        if (!is_null($autoCloseLanguages)) {
            $data['autoclose_languages'] = $autoCloseLanguages ? 1 : 0;
        }

        if (!is_null($autoCloseTask)) {
            $data['autoclose_task'] = $autoCloseTask ? 1 : 0;
        }

        $this->taskRepository->updateById($taskId, $data);
        $this->vendorRateProfileService->updateProjectTaskData($taskId, $title);

        $this->elasticSearch->reindexOne(
            projectIds: [$projectId],
            origin: __METHOD__,
        );

        if ($oldDueDate?->getTimestamp() !== $newDueDate?->getTimestamp()) {
            $this->eventDispatcher->dispatch(
                TaskDueDateChangedEvent::create(
                    $projectData->getProjectId(),
                    $projectData->getBranchId(),
                    false,
                    $taskId,
                    $task['title'],
                    (int) $task['source_lang_id'],
                    $oldDueDate,
                    $newDueDate
                ),
                TaskDueDateChangedEvent::EVENT_NAME
            );
        }

        if ($shouldLockTranslationsOriginal != $data['do_lock_translations']) {
            $taskStatus = $this->getTaskStatus($taskId);

            if (
                !in_array($taskStatus, [
                    ProjectTaskStatus::Created,
                    ProjectTaskStatus::InProgress,
                ])
            ) {
                return;
            }

            $this->eventDispatcher->dispatch(
                new TaskLockingChangedEvent(
                    $this->taskMapper->mapTaskToEventEntity($this->getTaskById($taskId)),
                    (bool) $data['do_lock_translations']
                ),
                TaskLockingChangedEvent::EVENT_NAME
            );
        }

        (new TagManager($taskId, $userId))->syncEventTags($closingTags, TaskClosedEvent::EVENT_TYPE_ID);
    }

    private function prepareDueDate(?string $dueDate): ?DateTimeImmutable
    {
        if (!$dueDate) {
            return null;
        }

        $formatsToTry = ['Y-m-d', DateFormat::ISO_DATE_TIME];

        return $this->tryFormatDueDate($formatsToTry, $dueDate);
    }

    private function tryFormatDueDate(array $formats, string $dueDate): ?DateTimeImmutable
    {
        foreach ($formats as $format) {
            $result = DateTimeImmutable::createFromFormat(
                $format,
                $dueDate
            );

            if ($result !== false) {
                return $result;
            }
        }

        return null;
    }

    /**
     * @throws Throwable
     */
    public function addKeysToTask(string $projectId, int $taskId, string $language, array $keys, int $taskSourceLanguageId): int
    {
        $keyCount = count($keys);

        if ($language == 'all') {
            $taskLanguageIds = $this->getTaskLanguageLanguageIds($taskId);

            $languages = [];
            foreach ($taskLanguageIds as $taskLanguageId) {
                $languages[$taskLanguageId] = [
                    'id' => (int) $taskLanguageId,
                    'keys' => $keys,
                    'key_count' => $keyCount,
                ];
            }
        } else {
            $languages = [
                $language => [
                    'id' => (int) $language,
                    'keys' => $keys,
                    'key_count' => $keyCount,
                ],
            ];
        }

        $affectedTaskIds = [];
        $this->getChildTaskIdsRecursive($taskId, $affectedTaskIds);
        array_unshift($affectedTaskIds, $taskId);

        $availableLangKeys = $this->getAvailableTranslationsForLanguages($languages, $projectId);
        $translationsInserted = 0;

        $newKeyIds = [];
        $this->db->startTransaction();

        try {
            $insertedTranslationIds = [];

            foreach ($availableLangKeys as $lang_id => $key_data) {
                if (empty($key_data['keys'])) {
                    continue;
                }

                $translationsToInsert = [];
                foreach ($key_data['keys'] as $key) {
                    if (!in_array($key['akey_id'], $newKeyIds)) {
                        array_push($newKeyIds, $key['akey_id']);
                    }

                    foreach ($affectedTaskIds as $affectedTaskId) {
                        $translationsToInsert[] = [
                            'project_task_id' => $affectedTaskId,
                            'akey_id' => $key['akey_id'],
                            'segment_number' => $key['segment_number'],
                            'translation_id' => $key['id'],
                            'lang_id' => $lang_id,
                            'words' => 0, // will be calculated afterwards with $this->recalculateKeyAndWordCount
                        ];
                    }

                    $translationsInserted++;
                    $insertedTranslationIds[] = $key['id'];
                }

                if (!empty($translationsToInsert)) {
                    $this->taskRepository->insertTaskTranslations($translationsToInsert, true);
                }
            }
            $this->db->commit();
        } catch (Throwable $e) {
            $this->db->rollback();

            throw $e;
        }

        $this->recalculateKeyAndWordCount($taskId, $taskSourceLanguageId);
        $this->calculateMaxTmLeverage(
            $this->projectService->getTeamId($projectId),
            $projectId,
            $taskId,
            $languages,
            $newKeyIds,
            $taskSourceLanguageId
        );

        $this->elasticSearch->reindexOne(
            keyIds: $newKeyIds,
            origin: __METHOD__,
        );

        $this->eventDispatcher->dispatch(
            new TaskKeysAddedEvent(
                $this->taskMapper->mapTaskToEventEntity($this->getTaskById($taskId)),
                $newKeyIds
            ),
            TaskKeysAddedEvent::EVENT_NAME
        );

        return $translationsInserted;
    }

    /**
     * @throws Throwable
     */
    public function removeKeysFromTask(string $projectId, int $taskId, string $language, array $keyIds, int $taskSourceLanguageId): int
    {
        if (empty($keyIds)) {
            return 0;
        }

        $taskLanguageIds = $this->getTaskLanguageLanguageIds($taskId);

        if ($language != 'all') {
            foreach ($taskLanguageIds as $k => $taskLanguageId) {
                if ($language != $taskLanguageId) {
                    unset($taskLanguageIds[$k]);
                }
            }
        }

        if (empty($taskLanguageIds)) {
            return 0;
        }

        $keys = $this->taskRepository->getKeyIdsByKeyIds($taskId, $keyIds);
        if (empty($keys)) {
            return 0;
        }

        $translationsAffected = null;

        $affectedTaskIds = [];
        $this->getChildTaskIdsRecursive($taskId, $affectedTaskIds);
        array_unshift($affectedTaskIds, $taskId);

        $this->db->startTransaction();

        try {
            foreach ($taskLanguageIds as $languageId) {
                foreach ($affectedTaskIds as $affectedTaskId) {
                    $translationsAffected = $this->taskRepository->deleteTaskKeys($projectId, $affectedTaskId, $keys, $languageId);
                }
            }
            $this->db->commit();
        } catch (Throwable $e) {
            $this->db->rollback();

            throw $e;
        }

        $this->recalculateKeyAndWordCount($taskId, $taskSourceLanguageId);

        $this->eventDispatcher->dispatch(
            new TaskKeysRemovedEvent(
                $this->taskMapper->mapTaskToEventEntity($this->getTaskById($taskId)),
                $keys
            ),
            TaskKeysRemovedEvent::EVENT_NAME
        );

        $this->elasticSearch->reindexOne(
            keyIds: $keys,
            origin: __METHOD__,
        );

        return $translationsAffected;
    }

    public function closeTask($taskId, $automatic = false, bool $forceTaskItemAccess = false, bool $reindexProject = true): bool
    {
        if (!$automatic && !$forceTaskItemAccess && !$this->isTaskAdmin($taskId, $_SESSION['logged']['id'])) {
            return false;
        }

        $task = $this->getTaskById($taskId);

        if (empty($task)) {
            return false;
        }

        if (
            !in_array($task['status'], [
                ProjectTaskStatus::InProgress,
                ProjectTaskStatus::Created,
                ProjectTaskStatus::Queued,
            ], true)
        ) {
            return false;
        }

        if (
            in_array($task['status'], [ProjectTaskStatus::Queued, ProjectTaskStatus::Created], true)
            && in_array($task['task_type'], [ProjectTaskType::LqaByAi, ProjectTaskType::AutomaticTranslationByAi], true)
        ) {
            // clear locked usage for AI task
            $team = $this->teamService->getTeamByProjectId($task['project_id']);
            if (null !== $team) {
                $this->teamAiUsageService->clearTaskUsage((int) $team['id'], (int) $task['id']);
            }
        }

        $this->logger->notice('Closing task', ['task_id' => $taskId, 'automatic' => $automatic, 'force_task_item_access' => $forceTaskItemAccess, 'reindex_project' => $reindexProject]);

        $this->taskRepository->updateById($taskId, [
            'status' => ProjectTaskStatus::Completed,
            'completed_at' => $this->db->sqlEval('NOW()'),
            'completed_by' => $automatic ? 1 : $_SESSION['logged']['id'],
        ]);

        $this->eventDispatcher->dispatch(
            new TaskClosedEvent(
                $this->taskMapper->mapTaskToEventEntity($this->getTaskById($taskId))
            ),
            TaskClosedEvent::EVENT_NAME
        );

        if ($reindexProject) {
            $this->elasticSearch->reindexOne(
                projectIds: [$task['project_id']],
                keyIds: $this->getTaskKeyIds($taskId),
                origin: __METHOD__,
            );
        }

        return true;
    }

    public function unqueueTaskLanguage(int $taskId, int $languageId): bool
    {
        $taskLanguageStatus = $this->getTaskLanguageStatus($taskId, $languageId);

        if ($taskLanguageStatus !== ProjectTaskLanguageStatus::Queued) {
            return false;
        }

        $this->taskRepository->updateTaskLangByProjectAndLangId($taskId, $languageId, [
            'status' => ProjectTaskLanguageStatus::Created,
        ]);

        $this->eventDispatcher->dispatch(
            new TaskLanguageOpenedEvent(
                $this->taskMapper->mapTaskToEventEntity($this->getTaskById($taskId)),
                LanguageService::getInstance()->getById($languageId)
            ),
            TaskLanguageOpenedEvent::EVENT_NAME
        );

        return true;
    }

    public function unqueueTask(int $taskId): bool
    {
        $taskStatus = $this->getTaskStatus($taskId);

        if ($taskStatus !== ProjectTaskStatus::Queued) {
            return false;
        }

        $this->taskRepository->updateById($taskId, [
            'status' => ProjectTaskStatus::Created,
        ]);

        $this->eventDispatcher->dispatch(
            new TaskUnqueuedEvent(
                $this->taskMapper->mapTaskToEventEntity($this->getTaskById($taskId))
            ),
            TaskUnqueuedEvent::EVENT_NAME
        );

        return true;
    }

    public function closeLanguage($taskId, $languageId, int $userId, $automatic = false, bool $forceTaskItemAccess = false)
    {
        $return = [
            'success' => false,
            'finished' => $this->isLanguageDoneByItems($taskId, $languageId),
            'closed_task' => null,
            'lang' => null,
            'child_langs' => [],
            'child_task_unqueued' => false,
            'child_task_language_unqueued' => false,
            'warnings' => [],
        ];

        try {
            $task = $this->getTaskById($taskId);
            if (empty($task['id'])) {
                throw new CannotCloseLanguageException('Task not found');
            }

            $isAiTask = in_array($task['task_type'], [ProjectTaskType::LqaByAi, ProjectTaskType::AutomaticTranslationByAi], true);

            if (false === $isAiTask) {
                $hasTaskLanguageAccess = $this->hasTaskLanguageAccess($taskId, $languageId, $userId);

                $canFinishLang = $hasTaskLanguageAccess || $forceTaskItemAccess;

                if (!$canFinishLang) {
                    throw new CannotCloseLanguageException('Language cannot be finished');
                }
            }

            if ($this->getTaskLanguageStatus($taskId, $languageId) === ProjectTaskLanguageStatus::Completed) {
                $return['warnings'][] = 'lang_done_already';

                throw new CannotCloseLanguageException('Language already marked as done');
            }
        } catch (CannotCloseLanguageException $e) {
            $this->logger->notice('Cannot close the language', ['task_id' => $taskId, 'exception' => $e]);

            return $return;
        } catch (Throwable $e) {
            $this->logger->error('Cannot close the language', ['task_id' => $taskId, 'exception' => $e]);

            return $return;
        }

        $shouldCheckLocks = $this->shouldCheckLocks(
            taskId: $taskId,
            checkOnlyPending: true,
            task: $task,
        );

        $taskLanguageId = null;
        if ($shouldCheckLocks) {
            $taskLanguageId = $this->getTaskLanguageIdByTaskAndLanguageId($taskId, $languageId);
        }

        $childTaskIds = $this->getChildTaskIds($taskId);

        $lockNames = ['task:update:' . $taskId];
        $this->redisLockService->acquireMultipleLocks($lockNames, 300);

        try {
            $this->db->startTransaction();

            $this->taskRepository->updateTaskLangByProjectAndLangId($taskId, $languageId, [
                'status' => ProjectTaskLanguageStatus::Completed,
                'completed_at' => $this->db->sqlEval('NOW()'),
                'completed_by' => $automatic ? LOKALISE_USER_ID : $userId,
                'error_count' => $this->taskItemRepository->getCompletedItemsWithErrorsCountByTaskAndLanguageId(
                    taskId: (int) $taskId,
                    languageId: (int) $languageId
                ),
            ]);

            if ($shouldCheckLocks && $taskLanguageId !== null) {
                $this->translationLockService->unlockByTaskLanguage($taskLanguageId);
            }

            $childTaskUnqueued = false;
            $childTaskLanguageUnqueued = false;
            foreach ($childTaskIds as $childTaskId) {
                $childTaskUnqueuedCurrent = $this->unqueueTask($childTaskId);
                $childTaskUnqueued = $childTaskUnqueued || $childTaskUnqueuedCurrent;

                $childTaskLanguageUnqueuedCurrent = $this->unqueueTaskLanguage($childTaskId, $languageId);
                $childTaskLanguageUnqueued = $childTaskLanguageUnqueued || $childTaskLanguageUnqueuedCurrent;
            }

            $return['child_task_unqueued'] = $childTaskUnqueued;
            $return['child_task_language_unqueued'] = $childTaskLanguageUnqueued;

            $hasTaskUncompletedLanguages = $this->hasTaskUncompletedLanguages($taskId);

            if ((int) $task['autoclose_task'] === 1 && !$hasTaskUncompletedLanguages) {
                $this->logger->notice('Task is done, trying to auto-close it', ['task_id' => $taskId]);

                if ($this->closeTask($taskId, true, $forceTaskItemAccess, false)) {
                    $return['closed_task'] = [
                        'task_id' => $taskId,
                    ];
                }
            } else {
                $this->logger->notice('Not attempting to auto-close task', ['task_id' => $taskId, 'autoclose_option' => $task['autoclose_task'], 'has_task_uncompleted_languages' => $hasTaskUncompletedLanguages]);
            }

            $this->db->commit();

            $this->logger->notice('Committed DB transaction for task', ['task_id' => $taskId]);

            $return['success'] = true;
        } catch (Throwable $e) {
            $this->logger->error('Rolling back DB transaction for task', ['task_id' => $taskId, 'exception' => $e]);
            $this->db->rollback();

            return $return;
        } finally {
            $this->redisLockService->releaseMultipleLocks($lockNames);
        }

        $this->eventDispatcher->dispatch(
            new TaskCancelCloseLangReminderEvent($taskId, $languageId),
            TaskCancelCloseLangReminderEvent::EVENT_NAME,
        );

        $this->eventDispatcher->dispatch(
            new TaskLanguageClosedEvent(
                $this->taskMapper->mapTaskToEventEntity($this->getTaskById($taskId)),
                LanguageService::getInstance()->getById($languageId),
                $userId
            ),
            TaskLanguageClosedEvent::EVENT_NAME
        );

        $this->elasticSearch->reindexOne(
            projectIds: [$task['project_id']],
            keyIds: $return['closed_task'] ? $this->getTaskKeyIds($taskId) : [],
            origin: __METHOD__,
        );

        $return['langs'] = $this->getLanguagesForProjectTask($taskId, $languageId);

        $childTaskIds = $this->getChildTaskIds($taskId);
        foreach ($childTaskIds as $childTaskId) {
            $return['child_langs'][] = $this->getLanguagesForProjectTask($childTaskId, $languageId);
        }

        return $return;
    }

    /**
     * Forcibly closes given task language (as Lokalise system user). Language close event is not dispatched to avoid
     * notification and webhook triggering.
     */
    public function forceCloseLanguage(int $taskId, int $languageId): void
    {
        $currentStatus = $this->getTaskLanguageStatus($taskId, $languageId);

        if ($currentStatus === null || $currentStatus === ProjectTaskLanguageStatus::Completed) {
            return;
        }

        $this->taskRepository->updateTaskLangByProjectAndLangId(
            $taskId,
            $languageId,
            [
                'status' => ProjectTaskLanguageStatus::Completed,
                'completed_at' => $this->db->sqlEval('NOW()'),
                'completed_by' => LOKALISE_USER_ID,
            ]
        );

        $this->eventDispatcher->dispatch(
            new TaskCancelCloseLangReminderEvent($taskId, $languageId),
            TaskCancelCloseLangReminderEvent::EVENT_NAME,
        );

        $this->translationLockService->unlockByTaskLanguage(
            $this->getTaskLanguageIdByTaskAndLanguageId(
                $taskId,
                $languageId
            )
        );
    }

    public function getActiveTaskItem(int $taskItemId, int $taskId): ?array
    {
        return $this->db->queryFirstRow('
            SELECT 
                pt.project_id, 
                ptt.project_task_id, 
                ptt.akey_id,
                ptt.translation_id,
                ptt.lang_id,
                ptt.status,
                pt.autoclose_languages,
                pt.task_type,
                pt.is_cts_enabled,
                p.is_cts_multiple_enabled
            FROM 
                project_task_translations ptt
            JOIN 
                project_tasks pt ON pt.id = ptt.project_task_id
            JOIN 
                project p ON p.id = pt.project_id
            WHERE 
                ptt.id = %i_ptt_id
            AND 
                pt.id = %i_pt_id
            GROUP BY 
                ptt.id
        ', [
            'ptt_id' => $taskItemId,
            'pt_id' => $taskId,
        ]);
    }

    private function getTaskCtsInfoById(int $taskId): ?array
    {
        return $this->db->queryFirstRow('
            SELECT 
                pt.is_cts_enabled,
                p.is_cts_multiple_enabled
            FROM 
                project_tasks pt
            LEFT JOIN 
                project p ON p.id = pt.project_id
            WHERE 
                pt.id = %i
        ', $taskId);
    }

    /**
     * @throws PublicException
     * @throws TranslationLockedException
     * @throws ProjectTaskItemNotFoundException
     */
    public function toggleItemComplete(ToggleTaskItemCompleteParams $params, ?ProjectData $prm = null)
    {
        $return = [
            'task_id' => $params->getProjectTaskId(),
            'task_item_id' => $params->getTaskItemId(),
            'success' => false,
            'changed' => false,
            'closed_language' => null,
            'closed_task' => null,
        ];

        $currentUserId = $params->isForceSuperUser() ? LOKALISE_USER_ID : (int) $_SESSION['logged']['id'];

        if (!$params->getDoForceTaskItemAccess() && !$this->hasTaskItemAccess($params->getTaskItemId(), $currentUserId)) {
            return $return;
        }

        $newStatus = $params->getIsCompleted() ? ProjectTaskTranslationStatus::Completed : ProjectTaskTranslationStatus::Created;

        $canToggleTaskItemStatus = $this->taskItemRepository->canToggleStatus($params, $newStatus);
        if (!$canToggleTaskItemStatus) {
            // Legacy logic. Leaving as is
            $taskItem = $this->taskItemRepository->findById($params->getTaskItemId());

            if (null === $taskItem) {
                throw new ProjectTaskItemNotFoundException();
            }

            $return['status'] = $taskItem['status'];
            $return['success'] = true;

            return $return;
        }

        $updateFields = [
            'status' => $newStatus,
            'completed_by' => $newStatus === ProjectTaskTranslationStatus::Completed ? $currentUserId : null,
            'error' => $params->getError()?->name,
        ];
        $affectedRows = $this->taskItemRepository->update($params->getTaskItemId(), $updateFields);
        $activeTaskItem = $this->getActiveTaskItem($params->getTaskItemId(), $params->getProjectTaskId());

        $return['status'] = $activeTaskItem['status'];
        $return['success'] = true;

        if ($affectedRows === 0) {
            return $return;
        }

        if ($newStatus === ProjectTaskTranslationStatus::Completed) {
            try {
                $prm ??= $this->pdFactory->createFromUnknownId($activeTaskItem['project_id']);
            } catch (Throwable $e) {
                return $return;
            }

            if ($activeTaskItem['is_cts_enabled']) {
                $taskCustomStatusIds = $this->getTaskCustomTranslationStatusIds($params->getProjectTaskId());

                if ($activeTaskItem['is_cts_multiple_enabled']) {
                    $this->customTranslationStatusesService->addStatusesForTranslation(
                        $prm,
                        $activeTaskItem['translation_id'],
                        $taskCustomStatusIds,
                        $currentUserId
                    );
                } else {
                    $this->customTranslationStatusesService->setStatusesForTranslation(
                        $prm,
                        $activeTaskItem['translation_id'],
                        $taskCustomStatusIds,
                        $currentUserId
                    );
                }
            }

            if ($activeTaskItem['task_type'] === ProjectTaskType::Review) {
                $updateProofreadStateParams = new UpdateProofreadStateParams(
                    userId:               (int) $currentUserId,
                    providerId:           (int) $_SESSION['logged']['provider_id'],
                    translations:         [
                        [
                            'id' => $activeTaskItem['translation_id'],
                            'akey_id' => $activeTaskItem['akey_id'],
                        ],
                    ],
                    projectData:          $prm,
                    actionType:           ProjectEventActionTypeEnum::TASK_ITEM_COMPLETED,
                    apiCall:              false,
                    updateMode:           UpdateProofreadStateParams::MODE_SET,
                    newState:             true
                );

                $this->symfonyEventDispatcher->dispatch(
                    new ReviewTaskItemCompletedEvent($updateProofreadStateParams),
                );
            }
        }

        $return['changed'] = true;

        if ($return['status'] == ProjectTaskTranslationStatus::Completed) {
            $this->eventDispatcher->dispatch(
                new TaskItemClosedEvent(
                    $this->taskMapper->mapTaskToEventEntity(
                        $this->getTaskByItemId($params->getTaskItemId())
                    ),
                    $this->taskMapper->mapTaskItemToEventEntity(
                        $this->taskRepository->getTaskItemById($params->getTaskItemId())
                    ),
                    $params->getReferenceLanguageId()
                ),
                TaskItemClosedEvent::EVENT_NAME
            );
        } else {
            $this->eventDispatcher->dispatch(
                new TaskItemReopenedEvent(
                    $this->taskMapper->mapTaskToEventEntity(
                        $this->getTaskByItemId($params->getTaskItemId())
                    ),
                    $this->taskMapper->mapTaskItemToEventEntity(
                        $this->taskRepository->getTaskItemById($params->getTaskItemId())
                    ),
                    $params->getReferenceLanguageId()
                ),
                TaskItemReopenedEvent::EVENT_NAME
            );
        }

        return $return;
    }

    public function completeAiTaskItems(
        int $aiTaskId,
        array $aiTaskItemIds,
        ProjectData $projectData,
        int $taskSourceLanguageId,
    ): void {
        $currentUserId = LOKALISE_USER_ID;

        $completableAiTaskItemIds = $this->taskItemRepository->getCompletableAiTaskItemIds($aiTaskId, $aiTaskItemIds);

        if (empty($completableAiTaskItemIds)) {
            return;
        }

        $updateFields = [
            'status' => ProjectTaskTranslationStatus::Completed,
            'completed_by' => $currentUserId,
        ];
        $affectedRows = $this->taskItemRepository->updateManyWithSameData($completableAiTaskItemIds, $updateFields);

        if ($affectedRows === 0) {
            return;
        }

        $taskItems = $this->taskItemRepository->findManyByIds($completableAiTaskItemIds);
        $translationIds = array_column($taskItems, 'translation_id');
        $taskCtsInfo = $this->getTaskCtsInfoById($aiTaskId);

        if ($taskCtsInfo !== null && (int) $taskCtsInfo['is_cts_enabled'] === 1) {
            $taskCustomStatusIds = $this->getTaskCustomTranslationStatusIds($aiTaskId);

            if ((int) $taskCtsInfo['is_cts_multiple_enabled'] === 1) {
                $this->customTranslationStatusesService->addStatusesForTranslations(
                    $projectData,
                    $translationIds,
                    $taskCustomStatusIds,
                    $currentUserId
                );
            } else {
                $this->customTranslationStatusesService->setStatusesForTranslations(
                    $projectData,
                    $translationIds,
                    $taskCustomStatusIds,
                    $currentUserId
                );
            }
        }

        $task = $this->getTaskByIdBasic($aiTaskId);
        foreach ($taskItems as $taskItem) {
            $this->eventDispatcher->dispatch(
                new TaskItemClosedEvent(
                    task: $this->taskMapper->mapTaskToEventEntity($task),
                    taskItem: $this->taskMapper->mapTaskItemToEventEntity($taskItem),
                    referenceLanguageId: $taskSourceLanguageId,
                ),
                TaskItemClosedEvent::EVENT_NAME
            );
        }
    }

    public function getTaskLanguageIdByTaskAndLanguageId(int $taskId, int $languageId): ?int
    {
        return $this->taskRepository->getTaskLanguageIdByTaskAndLanguageId($taskId, $languageId);
    }

    /**
     * @param string $type One of TaskContributorType
     *
     * @throws Exception
     */
    private function saveTaskLangContributors(int $taskId, int $languageId, array $contributorIds, string $type)
    {
        if (
            !in_array($type, [
                TaskContributorType::User,
                TaskContributorType::UserGroup,
            ])
        ) {
            throw new Exception();
        }

        $projectTaskLanguageId = $this->getTaskLanguageIdByTaskAndLanguageId($taskId, $languageId);

        if (empty($projectTaskLanguageId)) {
            return;
        }

        $dbTable = $type === TaskContributorType::User ? 'project_task_lang_users' : 'project_task_lang_groups';
        $dbField = $type === TaskContributorType::User ? 'user_id' : 'user_group_id';

        if ($type === TaskContributorType::User) {
            $existingIds = $this->getTaskLangContributorUserIds($taskId, $languageId);
        } else {
            $existingIds = $this->getTaskLangContributorGroupIds($taskId, $languageId);
        }

        $doNotTouchIds = array_intersect($existingIds, $contributorIds);

        $newIds = [];
        foreach ($contributorIds as $contributorId) {
            if (!in_array($contributorId, $doNotTouchIds)) {
                $newIds[] = $contributorId;
            }
        }

        $dataToInsert = [];
        foreach ($newIds as $newId) {
            $dataToInsert[] = [
                'project_task_lang_id' => $projectTaskLanguageId,
                'lang_id' => $languageId,
                $dbField => $newId,
            ];
        }

        $deleteIds = [];
        foreach ($existingIds as $existingContributorId) {
            if (!in_array($existingContributorId, $contributorIds)) {
                $deleteIds[] = $existingContributorId;
            }
        }

        $this->db->startTransaction();

        try {
            if (!empty($newIds)) {
                $this->db->insert($dbTable, $dataToInsert);
            }

            if (!empty($deleteIds)) {
                $this->db->delete(
                    $dbTable,
                    "project_task_lang_id = %i AND {$dbField} IN %li",
                    $projectTaskLanguageId,
                    $deleteIds
                );
            }

            $this->db->commit();
        } catch (Throwable $t) {
            $this->db->rollback();
        } finally {
            if (!empty($newIds)) {
                $this->eventDispatcher->dispatch(
                    new TaskLanguageContributorsAddedEvent(
                        $this->getTaskById($taskId),
                        LanguageService::getInstance()->getById($languageId),
                        $newIds,
                        $type
                    ),
                    TaskLanguageContributorsAddedEvent::EVENT_NAME
                );
            }
        }
    }

    public function getTaskLangContributorUserIds(int $taskId, int $languageId): array
    {
        return $this->taskRepository->getTaskLangContributorUserIds($taskId, $languageId);
    }

    /**
     * @throws Exception
     */
    public function saveTaskLangContributorUsers(int $taskId, int $languageId, array $contributorUserIds)
    {
        $this->saveTaskLangContributors($taskId, $languageId, $contributorUserIds, TaskContributorType::User);
    }

    public function getTaskLangContributorGroupIds(int $taskId, int $languageId): array
    {
        return $this->taskRepository->getTaskLangContributorGroupIds($taskId, $languageId);
    }

    /**
     * @throws Exception
     */
    public function saveTaskLangContributorGroups(int $taskId, int $languageId, array $contributorGroupIds): void
    {
        $this->saveTaskLangContributors($taskId, $languageId, $contributorGroupIds, TaskContributorType::UserGroup);
    }

    public function getLanguageKeysFromTask(int $taskId): array
    {
        $languageIds = array_column($this->taskRepository->getTaskLanguageIds($taskId), 'lang_id');

        $return = [];
        foreach ($languageIds as $languageId) {
            ['keys' => $keyCount, 'words' => $wordCount] =
                $this->taskRepository->getKeyAndWordCountByTaskIdAndLangId($taskId, $languageId);

            $return[$languageId] = [
                'key_count' => $keyCount,
                'word_count' => $wordCount,
                'key_count_other' => 0,
            ];
        }

        return $return;
    }

    /**
     * Save initial TM leverage for all translations included into the task.
     *
     * @note: For now, method works together with VendorRateProfiles, and used only if VRP are enabled
     *
     * @param int|null $baseLanguageId
     */
    public function calculateMaxTmLeverage(
        int $teamId,
        string $projectId,
        int $taskId,
        array $languages,
        array $keyIds,
        int $baseLanguageId
    ): void {
        if (!$baseLanguageId) {
            // Without base language there is nothing to search in elastic. Just skip
            return;
        }

        $taskLanguagesIds = array_unique(array_map('intval', array_column($languages, 'id')), SORT_NUMERIC);

        $chunkSize = 2000;
        $languagesCount = count($taskLanguagesIds);
        $chunkKeyCount = intval(floor($chunkSize / $languagesCount));
        $chunks = array_chunk($keyIds, $chunkKeyCount);

        $this->saveInitialProgressOnTmLeverage($taskId, count($chunks));

        foreach ($chunks as $chunkIndex => $chunkedKeyIds) {
            $this->queueService->publishTask(
                LegacyTaskName::CALCULATE_MAX_TM_LEVERAGE,
                [
                    'teamId' => $teamId,
                    'projectId' => $projectId,
                    'taskId' => $taskId,
                    'taskLanguagesIds' => $taskLanguagesIds,
                    'keyIds' => $chunkedKeyIds,
                    'baseLanguageId' => $baseLanguageId,
                ],
                TASK_GROUP_QUEUE_TM_LEVERAGE,
                $projectId,
            );
        }
    }

    public function recalculateKeyAndWordCount(int $taskId, ?int $taskSourceLanguageId = null): void
    {
        $task = $this->taskRepository->getByIdBasic($taskId);
        if (!$task) {
            $this->logger->warning('Task not found - aborting recalculation of key and word count', [
                'taskId' => $taskId,
            ]);

            return;
        }

        if ($task['status'] === ProjectTaskStatus::Completed) {
            $this->logger->warning('Task is closed but recalculation of key and word count is triggered', [
                'taskId' => $taskId,
            ]);
        }

        if ($taskSourceLanguageId === null) {
            $taskSourceLanguageId = $this->getSourceLanguageId($taskId);
        }
        if ($taskSourceLanguageId === null) {
            return;
        }

        $taskLanguageIds = $this->getTaskLanguageLanguageIds($taskId);

        if (empty($taskLanguageIds)) {
            return;
        }

        $uniqueKeyIds = $this->taskRepository->getTaskKeyIds($taskId);

        $keySourceWords = [];
        $sourceWordCountByKeyIds = [];
        $keySourceLangTranslations = $this->translationRepository->findByKeyIdsAndLanguageIdForTasks($uniqueKeyIds, $taskSourceLanguageId);
        foreach ($keySourceLangTranslations as $keySourceLangRow) {
            if (!array_key_exists($keySourceLangRow['akey_id'], $sourceWordCountByKeyIds)) {
                $sourceWordCountByKeyIds[$keySourceLangRow['akey_id']] = 0;
            }
            // TODO: Why it's needed to recount? This data is already inside `translation` table.
            // TODO: This function (recalculateKeyAndWordCount) could be done by queries.
            // TODO: Count and group in DB, update directly from `translation` table instead of tmpTable
            $wordCount = StringHelper::countWordsKeyType((string) $keySourceLangRow['translation'], (bool) $keySourceLangRow['plural']);
            $sourceWordCountByKeyIds[$keySourceLangRow['akey_id']] += $wordCount;
            $keySourceWords[] = [
                'akey_id' => $keySourceLangRow['akey_id'],
                'segment_number' => $keySourceLangRow['segment_number'],
                'words' => $wordCount,
            ];
        }

        // Only need the keyId and langId so that above stats could be grouped by lang
        // And each lang could count how many keys and words it has
        // Distinct is beacuse there could be segments. And in this step segments are not important.
        // TODO: JOIN `project_task_langs` is probably reduntant
        $sql = '
          SELECT DISTINCT
            a.id as akey_id, t.lang_id
          FROM 
            akey a 
          JOIN
            translation t ON t.akey_id = a.id
          JOIN
            project_task_translations ptt ON ptt.translation_id = t.id AND ptt.project_task_id = %i_task_id
          JOIN
            project_task_langs ptl ON ptl.lang_id = ptt.lang_id AND ptl.project_task_id = %i_task_id
          WHERE
            a.id IN %li_key_ids
        ';

        $sourceWordCountByLanguagesTemp = [];
        if (!empty($uniqueKeyIds)) {
            $sourceWordCountByLanguagesTemp = $this->db->query($sql, [
                'task_id' => $taskId,
                'key_ids' => $uniqueKeyIds,
            ]);
        }

        $sourceKeyCountByLanguages = [];
        $sourceWordCountByLanguages = [];
        foreach ($taskLanguageIds as $taskLangId) {
            $sourceKeyCountByLanguages[$taskLangId] = 0;
            $sourceWordCountByLanguages[$taskLangId] = 0;
        }

        foreach ($sourceWordCountByLanguagesTemp as $v) {
            $sourceKeyCountByLanguages[$v['lang_id']]++;
            $sourceWordCountByLanguages[$v['lang_id']] += $sourceWordCountByKeyIds[$v['akey_id']];
        }

        $totalSourceWordCount = array_sum($sourceWordCountByLanguages);
        $affectedTaskIds = [];
        $this->getChildTaskIdsRecursive($taskId, $affectedTaskIds);
        array_unshift($affectedTaskIds, $taskId);

        $this->taskRepository->updateKeyAndWordCount(
            $affectedTaskIds,
            $keySourceWords,
            $taskLanguageIds,
            $sourceWordCountByLanguages,
            $sourceKeyCountByLanguages,
            $totalSourceWordCount,
            $uniqueKeyIds,
            $this->keyService->getNumberOfRepetitions(
                $uniqueKeyIds,
                $taskSourceLanguageId
            )
        );
    }

    public function shouldCheckLocks(int $taskId, bool $checkOnlyPending = false, ?array $task = null): bool
    {
        if ($task !== null) {
            $res = [
                'status' => $task['status'],
                'do_lock_translations' => $task['do_lock_translations'],
            ];
        } else {
            $sql = 'SELECT status, do_lock_translations FROM project_tasks pt WHERE pt.id = %i_task_id LIMIT 1';
            $res = $this->db->queryFirstRow($sql, [
                'task_id' => $taskId,
            ]);
        }

        if (empty($res)) {
            return false;
        }

        if ($checkOnlyPending) {
            return in_array($res['status'], [
                ProjectTaskStatus::Created,
                ProjectTaskStatus::InProgress,
            ]) && $res['do_lock_translations'];
        }

        return (bool) $res['do_lock_translations'];
    }

    public function getUnclosedProjectTasks($projectId, bool $withoutChildren = false)
    {
        $taskArray = [];

        $tasks = $this->taskRepository->getUnclosedProjectTasks($projectId, $withoutChildren);

        if (!empty($tasks)) {
            $taskIds = array_value_recursive('id', $tasks);
            $taskLanguages = $this->getLanguagesForProjectTasks($taskIds);

            foreach ($tasks as $index => $task) {
                $task['langs'] = [];
                if ($taskLanguages[$task['id']]) {
                    $task['langs'] = $taskLanguages[$task['id']];
                }

                $taskArray[] = $task;
            }
        }

        return $taskArray;
    }

    public function getInitialProjectTasksForListView(string $projectId): array
    {
        $sqlTemplate = '(
            SELECT 
                pt.*,
                IF(pt.status = %s_status_in_progress, %s_status_created, pt.status) as task_status,
                u.fullname AS created_by_fullname,
                u.email AS created_by_email,
                u2.fullname AS completed_by_fullname,
                u2.email AS completed_by_email,
                parent_task.title AS parent_task_title,
                GROUP_CONCAT(CONCAT_WS("::", te.event_id, te.tag)) AS event_tags,
                p_master.id as master_project_id,
                IF(p_master.id IS NOT NULL, p_branch.name, NULL) AS branch_name,
                template_task.template_title AS template_title,
                COALESCE(pt_source_lang.name, p_source_lang.name_en_US) AS source_lang_name,
                COALESCE(pt_source_lang_lang.cc_iso, p_source_lang.cc_iso) AS source_lang_cc_iso,
                GROUP_CONCAT(pl_target.lang_id) AS target_lang_ids
            FROM 
              project_tasks pt
            JOIN 
              `user` u ON u.id = pt.created_by
            JOIN
              project as p_branch ON p_branch.id = pt.project_id
            LEFT JOIN
              project as p_master ON p_master.insert_id = p_branch.master_reference_id
            LEFT JOIN
              project_lang pt_source_lang ON pt.source_lang_id = pt_source_lang.lang_id AND pt_source_lang.project_id = pt.project_id
            LEFT JOIN
              lang pt_source_lang_lang ON pt_source_lang_lang.id = pt_source_lang.lang_id  
            LEFT JOIN
              lang p_source_lang ON p_source_lang.id = pt.source_lang_id
            LEFT JOIN 
              `user` u2 ON u2.id = pt.completed_by
            LEFT JOIN 
              project_tasks parent_task ON pt.parent_task_id = parent_task.id
            LEFT JOIN
              project_task_tag_events te ON te.project_task_id = pt.id
            LEFT JOIN
              project_tasks template_task ON template_task.id = pt.template_task_id
            LEFT JOIN
              project_task_langs pl_target ON pl_target.project_task_id = pt.id
            WHERE 
              pt.project_id = %s_project_id AND pt.status IN %ls_[STATUSES_KEY]
            GROUP BY 
              pt.id
            ORDER BY 
                pt.created_at DESC
            LIMIT 5 OFFSET 0
        )';

        $params = [
            'project_statuses_0' => [ProjectTaskStatus::Created, ProjectTaskStatus::InProgress],
            'project_statuses_1' => [ProjectTaskStatus::Queued],
            'project_statuses_2' => [ProjectTaskStatus::Completed],
        ];

        $sqls = [];
        foreach ($params as $statusKey => $statusArray) {
            $sqls[] = str_replace('[STATUSES_KEY]', $statusKey, $sqlTemplate);
        }

        $params = array_merge($params, [
            'project_id' => $projectId,
            'status_in_progress' => ProjectTaskStatus::InProgress,
            'status_created' => ProjectTaskStatus::Created,
        ]);

        $sql = implode("\nUNION\n", $sqls);

        $tasks = $this->db->query($sql, $params);

        $formattedTasks = $this->formatTaskItemsForListView($tasks);

        foreach ($formattedTasks as $i => $task) {
            if ($formattedTasks[$i]['master_project_id']) {
                $formattedTasks[$i]['project_id'] = $formattedTasks[$i]['master_project_id'];
            }
        }

        return $this->commonService->groupData($formattedTasks, 'task_status');
    }

    public function getProjectTasksForListViewByStatus(ProjectData $prm, string $status, int $firstId): array
    {
        return $this->formatTaskItemsForListView(
            $this->taskRepository->getDataForListView($prm, $status, $firstId)
        );
    }

    private function formatTaskItemsForListView(array $tasks): array
    {
        $taskArray = [];

        if (empty($tasks)) {
            return [];
        }

        $taskIds = array_value_recursive('id', $tasks);
        $taskLanguages = $this->getLanguagesForProjectTasks($taskIds);

        foreach ($tasks as $index => $task) {
            $taskTmLeverageInfo = $this->getTmLeverageInfo(new TaskId($task['id']));

            $task['is_tm_ready'] = $taskTmLeverageInfo->isReady();
            $task['is_tm_supported'] = $taskTmLeverageInfo->isSupported();

            $task['langs'] = [];
            if ($taskLanguages[$task['id']]) {
                $task['langs'] = $taskLanguages[$task['id']];
            }

            $task['event_tags'] = $this->tagService->formatEventTags($task['event_tags']);

            $taskArray[] = $task;
        }

        return $taskArray;
    }

    /**
     * @param array $ids
     * @param int|null $limit
     * @param int|null $offset
     *
     * @deprecated Should be removed after Task Dashboard 2.0 release, use getProjectTasks method instead
     */
    public function getProjectTasksCollection(
        ProjectData $prm,
        $ids = [],
        $limit = null,
        $offset = null,
        array $filters = []
    ): Collection {
        $projectBaseLanguageIso = $this->projectService->getDefaultLanguageIso($prm->getProjectId());

        $raw = $this->taskRepository->getDataForTasksCollection($prm->getProjectId(), $ids, $limit, $offset, $filters);
        $taskIds = array_column($raw['items'], 'id');

        $potentialParentTaskIds = $this->getAvailableParentTaskIds($prm->getProjectId());

        $chainedTasksFeatureEnabled = $this->teamPlanFeatureClient->isFeatureAvailable(
            new TeamPlanFeatureRequest(
                teamId: new TeamId($prm->project->team->id),
                featureId: Feature::ChainedTasks,
            )
        );

        $customTranslationStatuses = $this->getTasksCustomTranslationStatuses($taskIds);

        // get all data in bulk instead of foreach
        $statsRaw = $this->taskRepository->getStatsPerTaskPerLangByTaskIds($taskIds);
        $statsGrouped = [];
        foreach ($statsRaw as $statRaw) {
            $taskId = $statRaw['project_task_id'];
            $langId = $statRaw['lang_id'];
            if (!array_key_exists($taskId, $statsGrouped)) {
                $keysGrouped[$taskId] = [
                    'langs' => [],
                    'total' => 0,
                    'done' => 0,
                ];
            }
            // there is a function that does it swapKeyIdsToMaster
            // but it's too slow for large number of keys
            // and too heavy for mysql
            $statsGrouped[$taskId]['langs'][$langId] = $statRaw;
            $statsGrouped[$taskId]['total'] += (int) $statRaw['total'];
            $statsGrouped[$taskId]['done'] += (int) $statRaw['done'];
        }

        $languagesRaw = $this->taskRepository->getLanguagesForTasksCollectionByTaskIds($prm->getProjectId(), $taskIds);
        $langaugeIds = array_column($languagesRaw, 'id');
        $languagesGrouped = $this->commonService->groupData($languagesRaw, 'project_task_id');
        $keysRaw = $this->taskRepository->getKeysForTasksCollectionByTaskIds($taskIds);
        $keysGrouped = [];

        $shouldUseKeyIdFromBranch = $this->taskFeatureStateProvider->shouldUseKeyIdFromBranch();

        foreach ($keysRaw as $keyRaw) {
            $taskId = $keyRaw['project_task_id'];
            $langId = $keyRaw['project_task_lang_id'];
            if (!array_key_exists($taskId, $keysGrouped)) {
                $keysGrouped[$taskId] = [];
            }
            if (!array_key_exists($langId, $keysGrouped[$taskId])) {
                $keysGrouped[$taskId][$langId] = [];
            }

            $keysGrouped[$taskId][$langId][] = $this->keyIdResolver->resolveKeyIdForTaskObject($keyRaw, $shouldUseKeyIdFromBranch);
        }

        // languageIds are not regular languageIds but PK of project_task_lang
        $usersRaw = $this->getUsersForTaskLanguages($langaugeIds);
        $groupsRaw = $this->getUserGroupsForTaskLanguages($langaugeIds);

        $taskLanguageIdToCompoundTmMatchesMap = $this->taskTranslationMemoryService->getMappedByTaskIds(
            TaskIdCollection::fromRawArray($taskIds)
        );

        $collection = new Collection();
        foreach ($raw['items'] as $row) {
            $task = new Task();
            $task->id = (int) $row['id'];
            $task->title = $row['title'];
            $task->description = $row['description'];
            $task->dueDate = $row['due_date'] ? DateTime::createFromFormat('Y-m-d H:i:s', $row['due_date']) : null;
            if ($task->dueDate) {
                $task->dueDateTimestamp = $task->dueDate->getTimestamp();
            }
            $task->sourceLangId = (int) $row['source_lang_id'];
            $task->sourceLangIso = $row['source_lang_iso'] ?? $projectBaseLanguageIso;
            $task->keysCount = (int) $row['keys'];
            $task->wordsCount = (int) $row['words'];
            $task->createdAt = DateTime::createFromFormat('Y-m-d H:i:s', $row['created_at']);
            $task->createdAtTimestamp = $task->createdAt->getTimestamp();
            $task->createdBy = (int) $row['created_by'];
            $task->createdByEmail = $row['created_by_email'];
            $task->createdByEmailFiltered = new UserFilteredEmail($row['created_by_email'], $row['created_by_provider_id']);
            $task->autoCloseItems = $row['auto_close_items'] === '1';
            $task->autoCloseLanguages = $row['autoclose_languages'] === '1';
            $task->autoCloseTask = $row['autoclose_task'] === '1';
            $task->completedAt = $row['completed_at'] ? DateTime::createFromFormat('Y-m-d H:i:s', $row['completed_at']) : null;
            if ($task->completedAt) {
                $task->completedAtTimestamp = $task->completedAt->getTimestamp();
            }
            $task->completedBy = $row['completed_by'] ? (int) $row['completed_by'] : null;
            $task->completedByEmail = $row['completed_by_email'] ?: null;
            $task->completedByEmailFiltered = $row['completed_by_email'] ? new UserFilteredEmail($row['completed_by_email'], $row['completed_by_provider_id']) : null;
            $task->canBeParent = $chainedTasksFeatureEnabled ? in_array($task->id, $potentialParentTaskIds) : false;
            $task->taskType = $row['task_type'];
            $task->parentTaskId = $row['parent_task_id'] ? (int) $row['parent_task_id'] : null;
            $task->closingTags = $this->tagService->formatEventTags($row['event_tags'])[TaskClosedEvent::EVENT_TYPE_ID] ?? [];
            $task->doLockTranslations = $row['do_lock_translations'] === '1';
            $task->customTranslationStatusIds = array_map(function (array $advStatus) {
                return (int) $advStatus['custom_translation_status_id'];
            }, $customTranslationStatuses[$row['id']] ?? []);

            $taskStats = $statsGrouped[$task->id];

            $task->progress = TaskUtil::calculateProgress((int) $taskStats['done'], (int) $taskStats['total']);
            $task->status = $row['status'];

            $taskLanguagesRaw = $languagesGrouped[$task->id] ?? [];

            $languagesCollection = new Collection();
            foreach ($taskLanguagesRaw as $taskLanguageRow) {
                $language = new TaskLanguage();
                $language->id = (int) $taskLanguageRow['id'];
                $language->languageId = (int) $taskLanguageRow['lang_id'];
                $language->languageIso = $taskLanguageRow['iso'];
                $language->languageName = $taskLanguageRow['name'];
                // keys are swapped already
                $language->keys = $keysGrouped[$task->id][$language->id] ?? [];
                $language->keysCount = (int) $taskLanguageRow['keys'];
                $language->wordsCount = (int) $taskLanguageRow['words'];
                $language->completedAt = $taskLanguageRow['completed_at'] ? DateTime::createFromFormat('Y-m-d H:i:s', $taskLanguageRow['completed_at']) : null;
                if ($language->completedAt) {
                    $language->completedAtTimestamp = $language->completedAt->getTimestamp();
                }
                $language->completedBy = $taskLanguageRow['completed_by'] ? (int) $taskLanguageRow['completed_by'] : null;
                $language->completedByEmail = $taskLanguageRow['completed_by_email'] ?: null;
                $language->completedByEmailFiltered = $taskLanguageRow['completed_by_email'] ? (new UserFilteredEmail($taskLanguageRow['completed_by_email'], $taskLanguageRow['completed_by_provider_id'])) : null;

                if (!empty($taskStats['langs'][$language->languageId])) {
                    $progressTotal = $taskStats['langs'][$language->languageId]['total'];
                    $progressDone = $taskStats['langs'][$language->languageId]['done'];
                    $language->progress = TaskUtil::calculateProgress((int) $progressDone, (int) $progressTotal);
                }
                $language->status = $language->progress > 0 && $taskLanguageRow['status'] == ProjectTaskLanguageStatus::Created ? 'in progress' : $taskLanguageRow['status'];

                $taskUsersRaw = $usersRaw[$language->id]['users'] ?? [];

                $usersCollection = new Collection();
                foreach ($taskUsersRaw as $taskUserRow) {
                    $user = new TaskUser();
                    $user->id = (int) $taskUserRow['user_id'];
                    $user->email = $taskUserRow['email'];
                    $user->emailFiltered = new UserFilteredEmail($taskUserRow['email'], $taskUserRow['provider_id']);
                    $user->fullName = $taskUserRow['fullname'];
                    $usersCollection->add($user);
                }
                $language->users = $usersCollection;

                $taskGroupsRaw = $groupsRaw[$language->id]['groups'] ?? [];

                $groupsCollection = new Collection();
                foreach ($taskGroupsRaw as $taskGroupRaw) {
                    $group = new TaskGroup();
                    $group->id = (int) $taskGroupRaw['id'];
                    $group->name = $taskGroupRaw['name'];
                    $groupsCollection->add($group);
                }
                $language->groups = $groupsCollection;

                $compoundTmMatches = $taskLanguageIdToCompoundTmMatchesMap->find(
                    TaskLanguageId::create(
                        $task->id,
                        $language->languageId
                    )
                );

                $initialTmLeverageView = $compoundTmMatches
                    ? InitialTmLeverageView::createCompleted($compoundTmMatches->getLegacyTmMatches())
                    : InitialTmLeverageView::createPending();

                $tmLeverageView = $compoundTmMatches
                    ? TmLeverageView::createCompleted($compoundTmMatches->getTmMatches())
                    : TmLeverageView::createPending();

                $language->initialTmLeverage = $initialTmLeverageView->toArray();
                $language->tmLeverage = $tmLeverageView->toArray();

                $languagesCollection->add($language);
            }
            $task->languages = $languagesCollection;

            $collection->add($task);
        }

        return $collection;
    }

    public function getLanguagesForProjectTask($taskId, $languageIds = null)
    {
        $result = [];
        $taskLangs = $this->getLanguagesForProjectTasks([$taskId], $languageIds);

        if ($taskLangs) {
            $result = $taskLangs[$taskId];
        }

        return $result;
    }

    public function getLanguagesForProjectTasks($taskIds, $languageIds = null)
    {
        $taskIds = (array) $taskIds;
        if (empty($taskIds)) {
            return [];
        }

        $result = [];

        $langsQuery = '
            SELECT 
              ptl.*, 
              pt.status AS task_status,
              IF(pl.name IS NOT NULL, pl.name, l.name_en_US) AS name, 
              l.cc_iso,
              l.lang_iso
            FROM 
              project_task_langs ptl
            JOIN 
              lang l ON l.id = ptl.lang_id
            LEFT JOIN 
              project_tasks pt ON pt.id = ptl.project_task_id
            LEFT JOIN 
              project_lang pl ON pl.project_id = pt.project_id 
            AND 
              pl.lang_id = ptl.lang_id
            WHERE 
              ptl.project_task_id IN %li_task_ids
        ';

        if (!empty($languageIds)) {
            $languageIds = (array) $languageIds;
            $langsQuery .= ' AND ptl.lang_id IN %li_lang_ids';
        }

        $langsQuery .= ' GROUP BY ptl.id';

        $langsQuery .= ' ORDER BY name';

        $taskLanguages = $this->db->query($langsQuery, [
            'task_ids' => $taskIds,
            'lang_ids' => $languageIds,
        ]);

        if (empty($taskLanguages)) {
            return $result;
        }

        $languageIds = array_map(
            static fn (array $taskLanguage): int => (int) $taskLanguage['lang_id'],
            $taskLanguages
        );

        $taskLanguageIds = array_value_recursive('id', $taskLanguages);

        $projectId = $this->taskRepository->getById($taskLanguages[0]['project_task_id'])['project_id'];
        $taskUserIdsAsContributors = $this->taskRepository->getTaskUserIdsThatAreProjectContributors($taskIds, $languageIds, $projectId);

        $taskLanguageUsers = $this->getUsersForTaskLanguages($taskLanguageIds);
        $taskLanguageUserGroups = $this->getUserGroupsForTaskLanguages(
            taskLanguageIds: $taskLanguageIds,
            kickUserIds: $taskUserIdsAsContributors
        );

        $perTaskPerLanguage = $this->languageStatsByTaskIds($taskIds);

        foreach ($taskLanguages as $index => $taskLanguage) {
            if (empty($perTaskPerLanguage[$taskLanguage['project_task_id']][$taskLanguage['lang_id']])) {
                $perTaskPerLanguage[$taskLanguage['project_task_id']][$taskLanguage['lang_id']] = [
                    'project_task_id' => $taskLanguage['project_task_id'],
                    'lang_id' => $taskLanguage['lang_id'],
                    'keys_total' => 0,
                    'keys_done' => 0,
                    'base_words_total' => 0,
                    'base_words_done' => 0,
                ];
            }

            $taskLanguage = array_merge($taskLanguage, $perTaskPerLanguage[$taskLanguage['project_task_id']][$taskLanguage['lang_id']]);

            $contributorUserIds = [];

            $contributors = [];
            foreach (($taskLanguageUsers[$taskLanguage['id']]['users'] ?? []) as $item) {
                $contributors[] = array_merge($item, [
                    'type' => 'user',
                ]);

                $contributorUserIds[] = $item['user_id'];
            }

            foreach (($taskLanguageUserGroups[$taskLanguage['id']]['groups'] ?? []) as $item) {
                $contributors[] = array_merge($item, [
                    'type' => 'group',
                ]);

                $contributorUserIds = array_merge($contributorUserIds, explode(',', $item['group_user_ids']));
            }

            $taskLanguage['contributors'] = $contributors;
            $taskLanguage['contributor_count'] = count(array_unique($contributorUserIds));

            $result[$taskLanguage['project_task_id']][] = $taskLanguage;
        }

        return $result;
    }

    public function getProjectBadgesForUserTeam(int $userId, int $teamId): array
    {
        $query = '
            SELECT
               IF(master.insert_id IS NULL, p.id, master.id) AS id,
               p.insert_id,
               IF(master.insert_id IS NULL, %s_master_name, p.name) AS name,
               COUNT(DISTINCT pt.id) AS task_count
           FROM project p
           JOIN project_user pu ON pu.project_id = p.id
           LEFT JOIN project_tasks pt ON pt.project_id = p.id AND pt.status != %s_task_status
           LEFT JOIN project master ON master.insert_id = p.master_reference_id
           WHERE pu.user_id = %i_user_id
             AND p.team_id = %i_team_id
           GROUP BY pu.project_id
        ';

        $badges = $this->db->query(
            $query,
            [
                'master_name' => static::MASTER_BRANCH_NAME,
                'team_id' => $teamId,
                'user_id' => $userId,
                'task_status' => ProjectTaskStatus::Completed,
            ]
        );

        // Properly type DB output
        $badges = array_map(function ($row) {
            $row['insert_id'] = (int) $row['insert_id'];
            $row['task_count'] = (int) $row['task_count'];

            return $row;
        }, $badges);

        // Group by project ids
        $badges = $this->commonService->groupData($badges, 'id', null, true);

        return $badges ?: [];
    }

    /**
     * @deprecated use NotificationTaskUsersProvider directly
     */
    public function getTaskUsersForNotification($taskId, $languageId = null, ?array $filterUserIds = null): array
    {
        return $this->notificationTaskUsersProvider->getTaskUsersForNotification(
            IntUtils::intOrNull($taskId),
            IntUtils::intOrNull($languageId),
            $filterUserIds ?? []
        );
    }

    private const TASK_TYPES_TO_AUTOCOMPLETE = [
        ProjectTaskType::Review,
        ProjectTaskType::Translation,
    ];

    public function autoSetItemCompleteAfterTranslation(
        int $translationId,
        ?int $referenceLanguageId = null,
        bool $forceTaskItemAccess = false
    ): ?array {
        $taskItem = $this->getUncompletedTaskTranslationItemByTranslation($translationId);
        if (empty($taskItem) || !in_array($taskItem['task_type'], self::TASK_TYPES_TO_AUTOCOMPLETE, true)) {
            return null;
        }

        return $this->toggleItemComplete(
            new ToggleTaskItemCompleteParams(
                $taskItem['id'],
                $taskItem['project_task_id'],
                true,
                $referenceLanguageId,
                $forceTaskItemAccess
            )
        );
    }

    public function autoSetItemCompleteAfterReview(
        int $translationId,
        ?int $refLangId = null,
        ?array $taskItem = null
    ) {
        $taskItem = $taskItem ?: $this->getUncompletedTaskTranslationItemByTranslation($translationId);

        $isReviewTypeTask = $taskItem && $taskItem['task_type'] === ProjectTaskType::Review;

        if (empty($taskItem) || !$isReviewTypeTask) {
            return;
        }

        $toggleTaskItemCompleteParams = new ToggleTaskItemCompleteParams(
            $taskItem['id'],
            $taskItem['project_task_id'],
            true,
            $refLangId
        );

        return $this->toggleItemComplete($toggleTaskItemCompleteParams);
    }

    /**
     * @return int[]
     */
    public function getActiveTasksByTranslations(string $projectId, array $translationIds): array
    {
        $results = $this->taskRepository->getUnclosedLangTasksByProjectIdAndTranslationIds($projectId, $translationIds);
        if (!empty($results)) {
            array_walk($results, fn (&$item) => $item['translation_id'] = intval($item['translation_id']));
            $results = array_column($results, 'project_task_id', 'translation_id');
        }

        return $results;
    }

    public function getUnclosedLangTasksByTranslationsForUser(string $projectId, $translationIds, ?int $userId = null)
    {
        if (!$userId) {
            $userId = $_SESSION['logged']['id'];
        }
        $translationIds = (array) $translationIds;

        $results = $this->taskRepository->getUnclosedLangTasksByProjectIdAndTranslationIdsAndUserId(
            $projectId,
            $translationIds,
            $userId
        );

        return array_column($results, null, 'translation_id');
    }

    public function searchUsers(SearchUsersParams $params): array
    {
        $usersQuery = '
            SELECT 
               u.id AS user_id, 
               u.fullname, 
               u.email, 
               "user" as type
            FROM 
              project_lang_user plu
            JOIN 
              `user` u ON u.id = plu.user_id
            WHERE 
              project_id = %s_project_id
            AND 
              lang_id = %i_lang_id
            AND 
              is_editable = 1
        ';

        if ($params->isProviderAlpha()) {
            $usersQuery .= ' AND (u.provider_id IS NULL OR u.provider_id = %i_provider_id)';
        } else {
            $usersQuery .= ' AND u.provider_id IS NULL';
        }

        if ($params->isOnlyReviewers()) {
            $usersQuery .= ' AND u.id IN (
                SELECT 
                    user_id 
                FROM 
                    project_user 
                WHERE 
                    project_id = %s_project_id 
                AND 
                    is_proofreader = 1
            )';
        }

        $usersQuery .= ' AND u.id IN (
            SELECT plu.user_id FROM project_lang_user plu WHERE plu.project_id = %s_project_id AND plu.lang_id = %i_ref_lang_id
        )';

        $res = $this->db->query($usersQuery, [
            'project_id' => $params->getProjectId(),
            'lang_id' => $params->getTargetLanguageId(),
            'provider_id' => $params->getProviderId(),
            'ref_lang_id' => $params->getReferenceLanguageId(),
        ]);

        return array_map(function ($row) {
            $row['user_id'] = (int) $row['user_id'];

            return $row;
        }, $res);
    }

    public function searchGroups(SearchUserGroupsParams $params): array
    {
        $proofreadQuery = '';
        if ($params->isOnlyReviewers()) {
            $proofreadQuery = ' AND ug.is_proofreader = 1';
        }

        $groupsQuery = "
            SELECT 
               ug.id, 
               ug.name, 
               COALESCE(GROUP_CONCAT(ugu.user_id), '') as group_user_ids,
               ug.logo_url,
               'group' as type
            FROM 
                user_group ug 
            JOIN
                user_group_project ugp ON ugp.user_group_id = ug.id 
            LEFT JOIN 
                user_group_lang ugl ON ug.id = ugl.user_group_id
            LEFT JOIN
                user_group_user ugu ON ugu.user_group_id = ug.id
            WHERE 
                ugp.project_id = %s_project_id
            AND
                (
                  (
                      ugl.lang_id = %i_lang_id AND ugl.is_editable = 1 
                      AND (SELECT 1 FROM user_group_lang ugl2 WHERE ugl2.user_group_id = ug.id AND ugl2.lang_id = %i_ref_lang_id LIMIT 1) = 1
                  )
                  OR
                  ug.is_admin = 1
                )
            {$proofreadQuery}
            GROUP BY
              ug.id
        ";

        $res = $this->db->query($groupsQuery, [
            'project_id' => $params->getProjectId(),
            'lang_id' => $params->getTargetLanguageId(),
            'ref_lang_id' => $params->getReferenceLanguageId(),
        ]);

        return array_map(function ($row) {
            $row['id'] = (int) $row['id'];

            return $row;
        }, $res);
    }

    public function getTranslationTaskIdsForKeys($keyIds, $grouped = false)
    {
        $keyIds = (array) $keyIds;
        if (count($keyIds) == 0) {
            return [];
        }

        $data = $this->taskRepository->getTranslationTaskIdsByKeyIds(array_map(intval(...), $keyIds));

        return $grouped ? $this->commonService->groupData($data, 'translation_id', null, true) : $data;
    }

    public function getTasksForProjectsForElastic($projectIds, $grouped = false)
    {
        $projectIds = (array) $projectIds;

        $data = $this->taskRepository->getDataForElasticByProjectIds($projectIds);

        return $grouped ? $this->commonService->groupData($data, 'project_id', null, true) : $data;
    }

    public function getFilteredKeysForLanguage($projectId, $languageId, $filterParams): array
    {
        $langKeys = [];
        $langKeys['keys'] = $this->elasticSearch->getProjectKeyIds(
            $projectId,
            null,
            'asc',
            [
                'f_show_per_page' => 20,
                'f_show_hidden' => false,
                'f_languages' => [$languageId],
                'f_languages_type' => [$languageId],
                'search' => null,
                'key_id' => null,
                'per_platform' => 0,
                'filter_params' => $filterParams,
            ]
        );
        $langKeys['key_count'] = count($langKeys['keys']);

        return $langKeys;
    }

    public function getFilteredKeysForLanguages($projectId, $languageIds, $filterParams)
    {
        $languageIds = is_array($languageIds) ? $languageIds : [$languageIds];
        $langKeys = [];
        foreach ($languageIds as $lang_id) {
            $langKeys[$lang_id] = $this->getFilteredKeysForLanguage($projectId, $lang_id, $filterParams);
        }

        return $langKeys;
    }

    public function getAvailableTranslationsForLanguage($languageId, $keyData, $projectId)
    {
        $availableLanguageKeys = [
            'keys' => [],
            'key_count' => 0,
            'key_count_other' => 0,
        ];

        if (empty($keyData) || empty($keyData['keys'])) {
            return $availableLanguageKeys;
        }

        $translations = [];

        $keyData['keys'] = array_map('intval', $keyData['keys']);
        $keyIdsChunks = array_chunk($keyData['keys'], 1000);

        foreach ($keyIdsChunks as $keyIdsChunk) {
            $translations = [...$translations, ...$this->db->query("
                SELECT t.id, t.akey_id, t.translation, t.segment_number
                FROM translation t
                JOIN akey a ON a.id = t.akey_id
                WHERE t.akey_id IN %li_key_ids
                  AND t.lang_id = %i_lang_id
                  AND IF(custom_attributes ->> '$.translatable' IS NULL, 'null',
                         custom_attributes ->> '$.translatable') <> 'false'
                  AND a.is_archived = 0
                  AND NOT EXISTS (
                    SELECT 1
                    FROM project_task_translations ptt
                    JOIN project_tasks pt ON pt.id = ptt.project_task_id
                    JOIN project_task_langs ptl ON ptl.project_task_id = pt.id AND ptl.lang_id = ptt.lang_id
                    WHERE ptt.akey_id = t.akey_id
                      AND ptt.lang_id = t.lang_id
                      AND ptt.segment_number = t.segment_number
                      AND ptl.lang_id = t.lang_id
                      AND ptl.status <> %s_ptl_status
                      AND pt.project_id = %s_project_id
                      AND pt.status <> %s_pt_status
                  )
            ", [
                'key_ids' => $keyIdsChunk,
                'lang_id' => $languageId,
                'pt_status' => ProjectTaskStatus::Completed,
                'ptl_status' => ProjectTaskLanguageStatus::Completed,
                'project_id' => $projectId,
            ])];
        }

        $this->logger->debug(
            'Retrieved available translations',
            ['languageId' => $languageId, 'translationsCount' => count($translations)],
        );

        $this->translationLockService->filterOutLockedTranslations(
            $translations,
            $_SESSION['logged']['id'],
            $projectId,
            isUserAdmin: $_SESSION['logged']['id'] === LOKALISE_USER_ID ? true : null,
        );

        $this->logger->debug(
            'Filtered out locked translations',
            ['languageId' => $languageId, 'translationsCount' => count($translations)],
        );

        $availableLanguageKeys['keys'] = $translations;
        // segmentation introduces multiple translations per key_id so there is a need to filter out dupe ids
        $availableLanguageKeys['key_count'] = count(array_unique(array_column($translations, 'akey_id')));
        $availableLanguageKeys['key_count_other'] = $keyData['key_count'] - $availableLanguageKeys['key_count'];

        return $availableLanguageKeys;
    }

    public function getAvailableTranslationsForLanguages($languageKeys, $projectId)
    {
        $availableLanguageKeys = [];
        foreach ($languageKeys as $languageId => $keyData) {
            $availableLanguageKeys[$languageId] = $this->getAvailableTranslationsForLanguage($languageId, $keyData, $projectId);
        }

        return $availableLanguageKeys;
    }

    /**
     * Gets a summary (available & unavailable key counts, words to-do count) for currently selected keys for all given
     * language pairs (source language -> [target languages]).
     *
     * @param int[] $targetLanguageIds
     */
    public function getSelectedKeysLanguagePairSummary(
        ProjectData $projectData,
        int $sourceLanguageId,
        array $targetLanguageIds,
        bool $countSourceWords,
        bool $countTargetWords
    ): array {
        $projectId = $projectData->getProjectId();

        $selectedKeyIds = $this->keySelectionService->getProjectSelectedKeys($projectId);

        $summary = [];

        foreach ($targetLanguageIds as $targetLanguageId) {
            $summary[$targetLanguageId] = $this->getLanguagePairKeysSummary(
                $projectId,
                $selectedKeyIds,
                $sourceLanguageId,
                $targetLanguageId,
                $countSourceWords,
                $countTargetWords
            );
        }

        return $summary;
    }

    /**
     * Gets a summary (available & unavailable key counts, words to-do count) for keys that matches given filter for all
     * given language pairs (single source language - [multiple target languages]).
     *
     * @param int[] $targetLanguageIds
     * @param mixed[] $filterParams
     */
    public function getFilteredKeysLanguagePairSummary(
        ProjectData $projectData,
        int $sourceLanguageId,
        array $targetLanguageIds,
        array $filterParams,
        bool $countSourceWords,
        bool $countTargetWords
    ): array {
        $projectId = $projectData->getProjectId();
        $summary = [];

        $filteredKeysPerLanguage = $filteredKeysForAllLanguages = [];

        $hasTaskRelatedFilter = count(
            array_filter(
                $filterParams,
                fn (array $filterParam) => strtolower($filterParam['object']) === 'task'
                || in_array(
                    strtolower($filterParam['option']),
                    ['completed_in_task', 'uncompleted_in_task']
                )
            )
        ) > 0;

        $languages = $targetLanguageIds;
        if ($hasTaskRelatedFilter) {
            $languages = array_keys($this->projectService->getLanguages($projectId));
        }

        foreach ($languages as $languageId) {
            $filteredKeysPerLanguage[$languageId] = $this->getFilteredKeysForLanguage(
                $projectId,
                $languageId,
                $filterParams
            );

            $filteredKeysForAllLanguages = [
                ...$filteredKeysForAllLanguages,
                ...$filteredKeysPerLanguage[$languageId]['keys'],
            ];
        }

        $filteredKeyIdsForAllLanguages = array_unique($filteredKeysForAllLanguages);

        foreach ($targetLanguageIds as $targetLanguageId) {
            $filteredKeyData = $filteredKeysPerLanguage[$targetLanguageId] ?? [];
            $filteredKeyIds = $filteredKeyData['keys'];

            if (!count($filteredKeyIds) && $hasTaskRelatedFilter) {
                $filteredKeyIds = $filteredKeyIdsForAllLanguages;
            }

            $summary[$targetLanguageId] = $this->getLanguagePairKeysSummary(
                $projectId,
                $filteredKeyIds,
                $sourceLanguageId,
                $targetLanguageId,
                $countSourceWords,
                $countTargetWords
            );
        }

        return $summary;
    }

    /**
     * @param int[] $keyIds
     */
    private function getLanguagePairKeysSummary(
        string $projectId,
        array $keyIds,
        int $sourceLanguageId,
        int $targetLanguageId,
        bool $countSourceWords,
        bool $countTargetWords
    ): array {
        $availableTranslations = $this->getAvailableTranslationsForLanguage(
            $targetLanguageId,
            ['keys' => $keyIds, 'key_count' => count($keyIds)],
            $projectId
        );

        $isAiLanguageInBeta = AiTaskLanguagesInBetaEnum::tryFrom($targetLanguageId);

        if ($availableTranslations['key_count'] === 0) {
            return [
                'key_count' => 0,
                'key_count_other' => $availableTranslations['key_count_other'],
                'word_count' => 0,
                'target_word_count' => 0,
                'lqai_preferred_lang' => $isAiLanguageInBeta || AiTaskSupportedLanguagesEnum::tryFrom($targetLanguageId),
                'ai_language_in_beta' => $isAiLanguageInBeta,
            ];
        }
        $availableTranslationKeyIds = array_unique(array_column($availableTranslations['keys'], 'akey_id'));

        $data = [
            'key_count' => $availableTranslations['key_count'],
            'key_count_other' => $availableTranslations['key_count_other'],
            'word_count' => 0,
            'target_word_count' => 0,
            'lqai_preferred_lang' => $isAiLanguageInBeta || AiTaskSupportedLanguagesEnum::tryFrom($targetLanguageId),
            'ai_language_in_beta' => $isAiLanguageInBeta,
        ];

        if ($countSourceWords) {
            $data['word_count'] = $this->translationRepository->getWordCountByLanguageAndKeys($sourceLanguageId, $availableTranslationKeyIds);
        }

        if ($countTargetWords) {
            $data['target_word_count'] = $this->translationRepository->getWordCountByLanguageAndKeys($targetLanguageId, $availableTranslationKeyIds);
        }

        return $data;
    }

    private const ACTIVE_TASKS_FOR_USER_QUERY = '
        SELECT pt.id
        FROM
        (
            SELECT ptlu.project_task_lang_id
            FROM project_task_lang_users ptlu
            WHERE ptlu.user_id = %i_user_id
            UNION
            SELECT ptlg.project_task_lang_id
            FROM user_group_user ugu
            JOIN project_task_lang_groups ptlg ON ugu.user_group_id = ptlg.user_group_id
            WHERE ugu.user_id = %i_user_id
        ) tmp_table
        JOIN project_task_langs ptl ON tmp_table.project_task_lang_id = ptl.id
        JOIN project_tasks pt ON ptl.project_task_id = pt.id
        AND pt.status IN %ls_task_statuses
        AND ptl.status IN %ls_lang_statuses
    ';

    public function getActiveTasksForUser(int $userId, ?TaskId $taskId = null): array
    {
        $sql = self::ACTIVE_TASKS_FOR_USER_QUERY;
        $params = [
            'user_id' => $userId,
            'task_statuses' => [
                ProjectTaskStatus::Created,
                ProjectTaskStatus::InProgress,
            ],
            'lang_statuses' => [
                ProjectTaskLanguageStatus::Created,
                ProjectTaskLanguageStatus::InProgress,
            ],
        ];

        if ($taskId && $taskId->getValue() !== null) {
            $sql .= ' AND pt.id = %i_task_id';
            $params['task_id'] = $taskId->toInt();
        }

        $userTaskIds = $this->db->queryFirstColumn($sql, $params);

        return array_map('intval', $userTaskIds);
    }

    /**
     * @param string|null $currentProjectId Used to prioritize order
     *
     * @return array[]
     */
    public function getActiveTasksDataForUser(int $userId, ?string $currentProjectId, ?TaskId $currentTaskId = null): array
    {
        $taskIds = $this->getActiveTasksForUser($userId, $currentTaskId);

        if (empty($taskIds)) {
            return [];
        }

        $return = [];

        if ($currentProjectId !== null) {
            $orderBy = $this->db->sqlEval(
                'CASE
                    WHEN p.id = %s_project_id THEN 1
                    ELSE 2
                END ASC,
                pt.created_at DESC',
                ['project_id' => $currentProjectId]
            );
        } else {
            $orderBy = $this->db->sqlEval('pt.created_at DESC');
        }

        $tasksQuery = '
                SELECT pt.id,
                       pt.task_type,
                       master_p.team_id,
                       pt.due_date,
                       pt.title,
                       pt.description,
                       pt.status,
                       u.fullname AS created_by_fullname,
                       master_p.name AS project_name,
                       master_p.id AS master_project_id,
                       master_p.is_branching_enabled AS master_branching_enabled,
                       IF(master_p.is_branching_enabled = 1 AND master_p.id <> p.id, p.name, NULL) AS branch_name,
                       COALESCE(pt_source_lang.name, p_source_lang.name_en_US) AS source_lang_name,
                       COALESCE(pt_source_lang_lang.cc_iso, p_source_lang.cc_iso) AS source_lang_cc_iso
                FROM project_tasks pt
                JOIN project p ON p.id = pt.project_id
                LEFT JOIN project master_p ON IF(p.master_reference_id IS NULL, p.insert_id, p.master_reference_id) = master_p.insert_id
                LEFT JOIN  project_lang pt_source_lang ON pt.source_lang_id = pt_source_lang.lang_id AND pt_source_lang.project_id = pt.project_id
                LEFT JOIN lang pt_source_lang_lang ON pt_source_lang_lang.id = pt_source_lang.lang_id  
                LEFT JOIN lang p_source_lang ON p_source_lang.id = pt.source_lang_id   
                JOIN `user` u ON u.id = pt.created_by
                WHERE pt.id IN %li_task_ids
                GROUP BY pt.id
                ORDER BY %?_order_by
        ';

        $tasks = $this->db->query($tasksQuery, [
            'task_ids' => $taskIds,
            'order_by' => $orderBy,
        ]);

        $readyLanguagesArray = [];
        $taskLanguageArray = [];

        $taskLanguages = $this->db->query('
            (
            SELECT 
              ptl.*, 
              IFNULL(pl.name, l.name_en_US) AS name,
              l.cc_iso
            FROM project_task_langs ptl
              JOIN lang l ON l.id = ptl.lang_id
              JOIN project_task_lang_users ptlu ON ptlu.project_task_lang_id = ptl.id
              JOIN project_tasks pt ON pt.id = ptl.project_task_id
              JOIN project_lang pl ON pl.project_id = pt.project_id AND pl.lang_id = ptl.lang_id
            WHERE ptlu.user_id = %i_user_id 
              AND ptl.project_task_id IN %li_task_ids
              AND ptl.status IN %ls_ptl_statuses
            GROUP BY ptl.id
            ORDER BY l.name_en_US
            )
            UNION
            (
            SELECT 
              ptl.*, 
              IFNULL(pl.name, l.name_en_US) AS name,
              l.cc_iso
            FROM project_task_langs ptl
              JOIN lang l ON l.id = ptl.lang_id
              JOIN project_task_lang_groups ptlg ON ptlg.project_task_lang_id = ptl.id
              JOIN user_group_user ugu ON ugu.user_group_id = ptlg.user_group_id AND ugu.user_id = %i_user_id
              JOIN project_tasks pt ON pt.id = ptl.project_task_id
              JOIN project_lang pl ON pl.project_id = pt.project_id AND pl.lang_id = ptl.lang_id
            WHERE
              ptl.project_task_id IN %li_task_ids
              AND ptl.status IN %ls_ptl_statuses
            GROUP BY ptl.id
            ORDER BY l.name_en_US
            )
        ', [
            'user_id' => $userId,
            'task_ids' => $taskIds,
            'ptl_statuses' => [
                ProjectTaskLanguageStatus::Queued,
                ProjectTaskLanguageStatus::Created,
                ProjectTaskLanguageStatus::InProgress,
                ProjectTaskLanguageStatus::Completed,
            ],
        ]);

        $perTaskPerLanguage = $this->languageStatsByTaskIds($taskIds);
        if ($taskLanguages) {
            foreach ($taskLanguages as $taskLanguage) {
                if (empty($perTaskPerLanguage[$taskLanguage['project_task_id']][$taskLanguage['lang_id']])) {
                    $perTaskPerLanguage[$taskLanguage['project_task_id']][$taskLanguage['lang_id']] = [
                        'project_task_id' => $taskLanguage['project_task_id'],
                        'lang_id' => $taskLanguage['lang_id'],
                        'keys_total' => 0,
                        'keys_done' => 0,
                        'base_words_total' => 0,
                        'base_words_done' => 0,
                        'progress' => 0,
                    ];
                }

                $taskLanguage = array_merge($taskLanguage, $perTaskPerLanguage[$taskLanguage['project_task_id']][$taskLanguage['lang_id']]);
                $taskLanguage['progress'] = TaskUtil::calculateProgress(
                    (int) $taskLanguage['base_words_done'],
                    (int) $taskLanguage['base_words_total']
                );

                if ($taskLanguage['base_words_done'] >= $taskLanguage['base_words_total']) {
                    $readyLanguagesArray[$taskLanguage['project_task_id']][] = $taskLanguage;
                } else {
                    $taskLanguageArray[$taskLanguage['project_task_id']][] = $taskLanguage;
                }
            }
        }

        $allTasksLanguages = array_unique(
            array_map(
                static fn ($lang) => (int) $lang['lang_id'],
                array_merge(...$taskLanguageArray)
            )
        );
        $allTasksLanguages[] = 0; // we're added 0 to get all style guides for all languages
        $allTasksProjects = array_column($tasks, 'master_project_id');

        $teamId = $this->accessService->getTeamId();

        $styleGuides = $this->styleGuideClient->search(
            array_unique(array_map(static fn ($task) => (int) $task['team_id'], $tasks)),
            $allTasksProjects,
            $allTasksLanguages,
        );

        foreach ($tasks as $task) {
            $task['team_id'] = $teamId;

            $task['langs'] = [];
            if (!empty($readyLanguagesArray[$task['id']])) {
                $task['langs'] = $readyLanguagesArray[$task['id']];
            }
            if ($taskLanguageArray[$task['id']]) {
                $task['langs'] = array_merge($task['langs'], $taskLanguageArray[$task['id']]);
            }

            if (!$task['langs']) {
                continue;
            }

            $styleGuidesForTask = array_filter($styleGuides, function (SearchStyleGuideItemData $styleGuide) use ($task) {
                $hasProject = !empty($styleGuide->getProjects())
                    && !empty(
                        array_filter(
                            $styleGuide->getProjects(),
                            static fn (SearchStyleGuideProjectData $project) => $project->getProjectId() === $task['master_project_id']
                        )
                    );

                return $hasProject
                    && !empty(array_filter(
                        $task['langs'],
                        static fn ($lang) => !empty($styleGuide->getLanguage()) && in_array($styleGuide->getLanguage()->getLangId(), [0, (int) $lang['lang_id']])
                    ));
            });

            $task['style_guides'] = array_map(static fn (SearchStyleGuideItemData $styleGuide) => [
                'id' => $styleGuide->getId(),
                'name' => $styleGuide->getName(),
            ], $styleGuidesForTask);

            $return[] = $task;
        }

        return $return;
    }

    public function getDueDateTaskWithUsers(): array
    {
        $now = $this->dateTimeProvider->get();

        $dateString = DateUtils::addSeconds($now, self::FULL_DAY_IN_SECONDS)->format('Y-m-d');
        $todayDate = DateUtils::setToMidnight($now);
        $parameters = [
            'from' => "{$dateString} 00:00:00",
            'to' => "{$dateString} 23:59:59",
            'today' => $todayDate->format('Y-m-d H:i:s'),
            'pt_statuses' => [
                ProjectTaskStatus::Created,
                ProjectTaskStatus::InProgress,
            ],
            'ptl_statuses' => [
                ProjectTaskLanguageStatus::Created,
                ProjectTaskLanguageStatus::InProgress,
            ],
        ];

        $tasks = $this->db->query('
          SELECT DISTINCT
            pt.id AS task_id,
            ptlu.user_id
          FROM project_task_lang_users AS ptlu
          JOIN project_task_langs AS ptl ON ptl.id = ptlu.project_task_lang_id
          JOIN project_tasks AS pt ON pt.id = ptl.project_task_id
          WHERE pt.status IN %ls_pt_statuses
          AND (pt.due_date BETWEEN %s_from AND %s_to)
          AND pt.created_at < %t_today
          AND ptl.status IN %ls_ptl_statuses
        ', $parameters);

        $users = $this->userClient->getBasicLegacyDataByUserIds(
            array_map(fn ($ccUser) => UserId::fromRaw($ccUser), array_unique(array_column($tasks, 'user_id'))),
            [
                'id',
                'email',
                'notificationSettings',
            ]
        );
        $users = array_column($users, null, 'id');

        foreach ($tasks as $key => &$task) {
            if (!isset($users[$task['user_id']])) {
                unset($tasks[$key]);
                continue;
            }

            $task['email'] = $users[$task['user_id']]['email'];
            $task['notification_settings'] = $users[$task['user_id']]['notificationSettings'];
        }

        return array_values($tasks);
    }

    public function getTasksCustomTranslationStatuses(array $taskIds): array
    {
        return $this->commonService->groupData(
            $this->taskRepository->getTasksCustomTranslationStatuses($taskIds),
            'task_id'
        );
    }

    public function getTasksCustomTranslationStatusIds(array $taskIds): array
    {
        return $this->commonService->groupData(
            $this->taskRepository->getTasksCustomTranslationStatusIds($taskIds),
            'task_id',
            'custom_translation_status_id',
            true
        );
    }

    /**
     * Method will always return an array with initial TM leverage, in case apps integrated with api2 not crash
     * In case task has new maxTmLeverage saved, method will convert it to previous initialTmLeverage
     * In case task has previously saved initialTmLeverage, method will prettify it
     * In other cases, method will return an array with zero values
     *
     * @param mixed $leverage
     */
    public function getPrettyInitialTmLeverage(
        int $taskId,
        int $languageId,
        array $translationsWordCountData,
        $leverage,
        array $maxLeverage
    ): array {
        if ($this->getTmLeverageInfo(TaskId::fromInt($taskId))->isReady()) {
            return $this->convertMaxTmLeverageToInitial($taskId, $languageId, $translationsWordCountData, $maxLeverage);
        }

        return $this->prettyInitialTmLeverage($leverage);
    }

    /**
     * Method converts current maxTmLeverage into initialTmLeverage, which is not generated anymore
     */
    private function convertMaxTmLeverageToInitial(int $taskId, int $languageId, array $translationsWordCountData, array $maxLeverage): array
    {
        $default = [
            '0%+' => 0,
            '60%+' => 0,
            '75%+' => 0,
            '95%+' => 0,
            '100%' => 0,
        ];

        foreach ($maxLeverage as $leverageKey => $value) {
            if (!(strpos($leverageKey, ":{$languageId}") > 0)) {
                continue;
            }
            $wordCount = $translationsWordCountData["{$taskId}:{$leverageKey}"] ?? 0;

            if ($value >= 100) {
                $default['100%'] += $wordCount;
            } elseif ($value >= 95) {
                $default['95%+'] += $wordCount;
            } elseif ($value >= 75) {
                $default['75%+'] += $wordCount;
            } elseif ($value >= 60) {
                $default['60%+'] += $wordCount;
            } else {
                $default['0%+'] += $wordCount;
            }
        }

        return $default;
    }

    public function saveMaxTmLeverage(int $taskId, array $leverageData): void
    {
        if (empty($leverageData)) {
            return;
        }

        $this->taskTmLeverageRepository->save($taskId, $leverageData);
    }

    public function getMaxTmLeverage(int $taskId): array
    {
        return $this->taskTmLeverageRepository->getAll($taskId);
    }

    public function cloneMaxTmLeverage(int $parentTaskId, int $subTaskId): void
    {
        $redisClient = $this->redisService->getClient();

        if (!$this->getTmLeverageInfo(TaskId::fromInt($parentTaskId))->isReady()) {
            // Add a flag for TM leverage calculation of parent task, that it should clone its result to subTask
            $redisClient->sAdd(
                $this->redisService->getProjectTaskTmLeverageDelayedClone($parentTaskId),
                $subTaskId
            );
        }

        $stored = $this->taskTmLeverageRepository->getAll($parentTaskId);

        if (!empty($stored)) {
            $this->taskTmLeverageRepository->save($subTaskId, $stored);
        }
    }

    public function saveAiTranslationToTmTaskOption(int $taskId, bool $isTranslationToTm): void
    {
        $client = $this->redisService->getClient();
        $client->setex(
            $this->redisService->getProjectTaskSaveAiTranslationToTmKey($taskId),
            AutomaticTranslationService::REDIS_TTL_7DAYS,
            $isTranslationToTm ? 1 : 0
        );
    }

    public function isAiTranslationToTmTaskOptionEnabled(int $taskId): bool
    {
        $client = $this->redisService->getClient();
        $key = $this->redisService->getProjectTaskSaveAiTranslationToTmKey($taskId);

        return 1 === (int) $client->get($key);
    }

    public function useTm100PercentMatchTaskOption(int $taskId): void
    {
        $client = $this->redisService->getClient();
        $client->setex(
            $this->redisService->getUseTm100PercentMatchKey($taskId),
            AutomaticTranslationService::REDIS_TTL_7DAYS,
            1
        );
    }

    public function isUseTm100PercentMatchTaskOptionEnabled(int $taskId): bool
    {
        $client = $this->redisService->getClient();
        $key = $this->redisService->getUseTm100PercentMatchKey($taskId);

        return 1 === (int) $client->get($key);
    }

    public function useTmFuzzyMatchTaskOption(int $taskId): void
    {
        $client = $this->redisService->getClient();
        $client->setex(
            $this->redisService->getUseTmFuzzyMatchKey($taskId),
            AutomaticTranslationService::REDIS_TTL_7DAYS,
            1
        );
    }

    public function isUseTmFuzzyMatchTaskOptionEnabled(int $taskId): bool
    {
        $client = $this->redisService->getClient();
        $key = $this->redisService->getUseTmFuzzyMatchKey($taskId);

        return 1 === (int) $client->get($key);
    }

    public function useIncludeScoreTaskOption(int $taskId): void
    {
        $client = $this->redisService->getClient();
        $client->setex(
            $this->redisService->getUseIncludeScoreTaskOption($taskId),
            AutomaticTranslationService::REDIS_TTL_7DAYS,
            1
        );
    }

    public function isUseIncludeScoreTaskOptionEnabled(int $taskId): bool
    {
        $client = $this->redisService->getClient();
        $key = $this->redisService->getUseIncludeScoreTaskOption($taskId);

        return 1 === (int) $client->get($key);
    }

    public function saveInitialProgressOnTmLeverage(int $taskId, int $queueTaskCount): void
    {
        $redisClient = $this->redisService->getClient();
        $key = $this->redisService->getProjectTaskTmLeverageProgressKey($taskId);

        if ((bool) $redisClient->exists($key)) {
            $redisClient->del($key);
        }

        $redisClient->incrBy($key, $queueTaskCount);
        $redisClient->expire($key, 3600 * 24);
    }

    public function progressOnTmLeverage(int $taskId): void
    {
        $redisClient = $this->redisService->getClient();
        $key = $this->redisService->getProjectTaskTmLeverageProgressKey($taskId);

        if (!(bool) $redisClient->exists($key)) {
            return;
        }

        // It is the amount of translation chunks that is kept under the given key. Each time chunk is processed, the
        // progressOnTmLeverage() is called and we decrement the value. Once we reach zero it means that all the chunks
        // were processed and we can proceed with the initial analysis report generation.
        $chunksLeft = (int) $redisClient->decr($key);

        if ($chunksLeft > 0) {
            // There still are chunks to process. Prolong key TTL and skip report generation this time.
            $redisClient->expire($key, self::TM_LEVERAGE_PROGRESS_KEY_TTL);

            return;
        }

        // Save report data to storage
        try {
            $reportData = $this->taskReportService->generateInitialReportData($taskId);
        } catch (TaskNotFoundException $e) {
            // For long running tasks it is possible that at a moment when initial report will be generated the task
            // will not exist anymore. In this case we should just silently ignore the error and do nothing.
            return;
        }

        $this->taskReportService->storeReportData($reportData);

        $leverageData = [];

        foreach ($reportData->getLanguagesData() as $languageId => $languageData) {
            $leverageData[$languageId] = $this->getPrettyInitialTmLeverage(
                $taskId,
                $languageId,
                [],
                $languageData['leverages'],
                $this->getMaxTmLeverage($taskId)
            );
        }

        $this->eventDispatcher->dispatch(
            new TaskInitialTmLeverageCalculatedEvent(
                $this->taskMapper->mapTaskToEventEntity($this->getTaskById($taskId)),
                $leverageData
            ),
            TaskInitialTmLeverageCalculatedEvent::EVENT_NAME
        );

        // Delete key, marking all background processes for the task are completed
        $redisClient->del($key);
        // Check whether there were create subTasks and we need to clone calculated TM leverage
        $this->processDelayedTmLeverageClones($taskId);
    }

    /**
     * Check whether there was created a subTask, while there was no TM for the task
     */
    private function processDelayedTmLeverageClones(int $taskId): void
    {
        $redisClient = $this->redisService->getClient();

        $delayedKey = $this->redisService->getProjectTaskTmLeverageDelayedClone($taskId);
        $delayed = $redisClient->sMembers($delayedKey);
        if (!is_array($delayed) || empty($delayed)) {
            return;
        }
        foreach ($delayed as $subTaskId) {
            $this->cloneMaxTmLeverage($taskId, $subTaskId);
            // Check for loop also. (New review task on previous review task? Why not?)
            $this->processDelayedTmLeverageClones($subTaskId);
        }
        $redisClient->del($delayedKey);
    }

    public function getDataFarTaskTmReCalculation(int $taskId): ?array
    {
        $data = $this->taskRepository->getDataForTmReCalculation($taskId);
        if (empty($data)) {
            return null;
        }

        $langIds = $this->taskRepository->getTaskLanguageIds($taskId);
        if (empty($langIds)) {
            // Stop if any data is missing
            return null;
        }

        $data['lang_ids'] = array_column($langIds, 'lang_id');
        $data['key_ids'] = $this->taskRepository->getTaskKeyIds($taskId);

        if (empty($data['key_ids'])) {
            // Stop if any data is missing
            return null;
        }

        return $data;
    }

    public function getSourceLanguageId(int $taskId): ?int
    {
        return $this->taskRepository->getSourceLanguageId($taskId);
    }

    /**
     * Tells whether there is any non-completed task for given key set.
     *
     * @param int[] $keyIds set of key IDs
     */
    public function hasUnclosedTaskForKeys(array $keyIds): bool
    {
        if (empty($keyIds)) {
            // There never be a matching task for no keys.
            return false;
        }

        return $this->taskRepository->existsByStatusesAndKeyIds(
            [
                ProjectTaskStatus::Created,
                ProjectTaskStatus::Queued,
                ProjectTaskStatus::InProgress,
            ],
            $keyIds
        );
    }

    public function handleTaskItemTranslationUpdate(array $taskItem, Translation $translation): void
    {
        if ($taskItem['task_type'] !== ProjectTaskType::Review) {
            // For now we care only about the Review tasks, the rest functionality is handled by listeners.
            return;
        }

        if (!$taskItem['is_cts_enabled']) {
            if ($translation->is_proofread) {
                $this->autoSetItemCompleteAfterReview($translation->id, null, $taskItem);
            }

            return;
        }

        $taskStatusIds = array_map('intval', explode(',', $taskItem['custom_translation_status_ids']));
        $translationStatusIds = $this->customTranslationStatusesService->getStatusIdsForTranslation($translation->id);

        // If all task statuses are submitted, the task is ready to be closed
        $doCloseTaskItem = true;
        foreach ($taskStatusIds as $taskStatusId) {
            if (!in_array($taskStatusId, $translationStatusIds)) {
                $doCloseTaskItem = false;
                break;
            }
        }

        if ($doCloseTaskItem) {
            $this->toggleItemComplete(
                new ToggleTaskItemCompleteParams($taskItem['id'], $taskItem['project_task_id'], true, null, true)
            );
        }
    }

    public function getTeamTotalTasksForNonBranches(int $teamId, bool $excludeLast24Hours = false): int
    {
        return $this->taskRepository->getTeamTotalTasksForNonBranches($teamId, $excludeLast24Hours);
    }

    private function logTaskCreation(int $taskId, string $projectId, string $taskTitle): void
    {
        $this->logger->notice(
            'Project Task created',
            [
                'task_id' => $taskId,
                'project_id' => $projectId,
                'title' => $taskTitle,
            ],
        );
    }

    private function getKeysSortedByLanguage(
        CreateTaskParams $params,
        array $includeOnlyListedKeyIds = [],
    ): array {
        $keysByLanguages = [];

        foreach ($params->languages as $lang) {
            $languageId = $lang['id'];
            $keysByLanguages[$languageId] = $this->getFilteredKeysForLanguage(
                $params->projectId,
                $lang['id'],
                $params->filterParams
            );

            // During isMass call we don't get context of selected keys here, so we need to filter them out
            if (!empty($includeOnlyListedKeyIds) && !empty($keysByLanguages[$languageId]['keys'])) {
                $keysByLanguages[$languageId]['keys'] = array_intersect(
                    $keysByLanguages[$languageId]['keys'],
                    $includeOnlyListedKeyIds
                );
                $keysByLanguages[$languageId]['key_count'] = count($keysByLanguages[$languageId]['keys']);
            }

            // Fallback just in case something is wrong with ES
            if (!empty($includeOnlyListedKeyIds) && empty($keysByLanguages[$languageId]['keys'])) {
                $keysByLanguages[$languageId]['keys'] = $includeOnlyListedKeyIds;
                $keysByLanguages[$languageId]['key_count'] = count($includeOnlyListedKeyIds);
            }
        }

        return $keysByLanguages;
    }

    /**
     * @param array<string, array<string, mixed>> $keysByLanguages
     *
     * @return int[]
     */
    public function getUniqueKeyIdsFromKeysByLanguages(array $keysByLanguages): array
    {
        $allKeyIds = [];

        foreach ($keysByLanguages as $languageData) {
            foreach ($languageData['keys'] as $keyId) {
                $allKeyIds[] = $keyId;
            }
        }

        return array_values(array_unique($allKeyIds));
    }

    /**
     * Filters $keysByLanguages to keep only keys that do not have an empty source translation,
     * for which it would not make sense to create automatic translation tasks, and that have empty
     * target translations, to prevent automatic translation tasks from overwriting existing translations.
     * Returns the filtered version of $keysByLanguages.
     *
     * @param array<string, array<string, mixed>> $keysByLanguages
     *
     * @return array<string, array<string, mixed>>
     */
    public function filterOutEmptyKeysWithEmptySourceTranslationOrNonEmptyTargetTranslation(
        array $keysByLanguages,
        string $sourceLanguageId,
    ): array {
        $uniqueKeyIds = $this->getUniqueKeyIdsFromKeysByLanguages($keysByLanguages);
        $translations = $this->translationRepository->getTranslationsWithPluralFlagByKeyIds($uniqueKeyIds);

        $keysWithNonEmptySourceTranslation = [];
        $keysWithEmptyTargetTranslation = [];

        foreach ($translations as $translation) {
            $isEmptyTranslation = TranslationHelper::isTranslationEmpty(
                $translation['translation'],
                (int) $translation['is_plural'] === 1,
            );
            $languageId = $translation['lang_id'];
            $keyId = $translation['akey_id'];

            if ($isEmptyTranslation) {
                $keysWithEmptyTargetTranslation[$languageId][] = $keyId;
            } elseif (
                $languageId === $sourceLanguageId && !in_array($keyId, $keysWithNonEmptySourceTranslation, true)
            ) {
                $keysWithNonEmptySourceTranslation[] = $keyId;
            }
        }

        $this->logger->info(
            sprintf(
                '%s out of %s keys have a non empty source translation and are viable for task creation.',
                count($keysWithNonEmptySourceTranslation),
                count($uniqueKeyIds),
            ),
        );

        if (empty($keysWithNonEmptySourceTranslation)) {
            /*
             * If all keys have an empty source translation, we can return early because no key is viable
             */
            return [];
        }

        $filteredKeysByLanguages = [];
        foreach ($keysByLanguages as $languageId => $languageData) {
            if (!empty($keysWithEmptyTargetTranslation[$languageId])) {
                $keys = $languageData['keys'];

                // Filter out keys that have an empty source translation
                $keysFilteredByNonEmptySourceTranslation = (!empty($keysWithNonEmptySourceTranslation))
                    ? array_intersect($keys, $keysWithNonEmptySourceTranslation)
                    : $keys;

                // Filter out keys that have a non-empty target translation
                $keysFilteredByEmptyTargetTranslation = array_intersect(
                    $keysFilteredByNonEmptySourceTranslation,
                    $keysWithEmptyTargetTranslation[$languageId],
                );

                if (count($keysFilteredByEmptyTargetTranslation) > 0) {
                    $filteredKeysByLanguages[$languageId] = [
                        'keys' => array_values($keysFilteredByEmptyTargetTranslation),
                        'key_count' => count($keysFilteredByEmptyTargetTranslation),
                    ];
                }
            }
        }

        return $filteredKeysByLanguages;
    }

    private function applyAiTranslationTaskOptions(CreateTaskParams $params, int $taskId): void
    {
        if ($params->type !== ProjectTaskType::AutomaticTranslationByAi) {
            return;
        }

        if ($params->saveAiTranslationToTm) {
            $this->saveAiTranslationToTmTaskOption($taskId, true);
        }

        if ($this->aiFeatureStateProvider->canUse100TmMatch() && $params->useTm100PercentMatch) {
            $this->useTm100PercentMatchTaskOption($taskId);
        }

        if ($this->aiFeatureStateProvider->canUseFuzzyMatch() && $params->useTmFuzzyMatch) {
            $this->useTmFuzzyMatchTaskOption($taskId);
        }

        $defaultLanguageId = $this->languageService->getDefaultLanguageIdByProjectId((string) $params->projectId);
        if ($this->aiFeatureStateProvider->canUseAiScoring() && $params->sourceLangId == $defaultLanguageId) {
            $this->useIncludeScoreTaskOption($taskId);
        }
    }
}
