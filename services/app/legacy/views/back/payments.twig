<div role="tabpanel" class="tab-pane" id="payments">
    <table class="table table-striped">
        <thead>
            <th>Type</th>
            <th>Created</th>
            <th>Order ID</th>
            <th>Customer</th>
            <th>Amount</th>
        </thead>
        {% for payment in data.payments %}
            <tr>
                <td>
                    <span class="label label-{% if payment.type == 'sub' %}success{% else %}info{% endif %}">{{ payment.type | upper }}</span>
                </td>
                <td>
                    {{ payment.created_at }}
                </td>
                <td>
                    {{ payment.order_id }}
                </td>
                <td>
                    <a href="/projects/?nologintrack=1&token={{ payment.token }}">{{ payment.fullname }}</a>
                </td>
                <td>
                    <strong>${{ (payment.amount / 100) | number_format(2) }}</strong>
                </td>
            </tr>
        {% endfor %}
    </table>
</div>
