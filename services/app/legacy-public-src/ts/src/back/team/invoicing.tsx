import * as React from 'react';
import Spinner from '../../_react-components/spinner';
import { axios } from '../../_common/axios-instance';
import InvoicingBillingDetails from './invoicing/billing_details';
import InvoicingBillingType from './invoicing/billing_type';
import ITeam from '../interfaces/team';
import {
    IInvoicingSubscriptionFormData,
    IInvoicingSubscriptionSummary,
    ISubscription,
    ISubscriptionAdvancedOptions,
} from '../interfaces/subscription';
import InvoicingBillingSummary from './invoicing/summary';

interface IProps {
    teamId: number;
}

interface IState {
    loading: boolean;
    spinner: boolean;
    team: ITeam;
    initialSubscription: ISubscription;
    formData: IInvoicingSubscriptionFormData;
    previewIsValid: boolean;
    isBillingInformationEdit: boolean;
}

const DEFAULT_CURRENCY_ISO = 'USD';

class Invoicing extends React.Component<IProps, IState> {
    public constructor(props: IProps) {
        super(props);

        this.state = {
            loading: true,
            spinner: false,
            team: {} as ITeam,
            initialSubscription: {} as ISubscription,
            formData: {} as IInvoicingSubscriptionFormData,
            previewIsValid: false,
            isBillingInformationEdit: false,
        };
    }

    public componentDidMount(): void {
        this.loadData();
    }

    public static formatAmount(amount: number, currency: string = DEFAULT_CURRENCY_ISO) {
        const formattedAmount = amount && !Number.isNaN(amount) ? amount / 100 : 0;

        return new Intl.NumberFormat('en-US', { style: 'currency', currency: currency }).format(formattedAmount);
    }

    public static hasError(formViolations: object, field: string): boolean {
        return formViolations && Object.keys(formViolations).indexOf(field) > -1;
    }

    public static getErrors(formViolations: object, field: string): string[] {
        return formViolations[field];
    }

    public static renderErrors(formViolations: object, fieldName: string) {
        return (
            Invoicing.hasError(formViolations, fieldName) &&
            Invoicing.getErrors(formViolations, fieldName).map((error) => {
                return <div className="form-error">{error}</div>;
            })
        );
    }

    public static renderErrorsSummary(formViolations: object) {
        if (Object.keys(formViolations).length) {
            const errors = Object.keys(formViolations).map((field) => Invoicing.renderErrors(formViolations, field));

            return <div className="form-error-summary alert alert-danger">{errors}</div>;
        }

        return;
    }

    private loadData(): void {
        this.setState(
            {
                spinner: true,
            },
            async () => {
                try {
                    const subscription = await this.getSubscriptionInfo();
                    const subscriptionData = subscription.initialSubscription;

                    this.setState({
                        team: subscription.team,
                        initialSubscription: subscriptionData.subscription ?? { items: [] },
                        spinner: false,
                        loading: false,
                        formData: {
                            resetFormData: false,
                            teamId: this.props.teamId,
                            subscriptionId: subscriptionData.subscription ? subscriptionData.subscription.id : null,
                            selectedBillingType: null,
                            items: subscriptionData.subscription ? subscriptionData.subscription.items : [],
                            term: null,
                            customer: subscriptionData.subscription
                                ? subscriptionData.customer
                                : { has_card: false, id: subscription.team.stripe_customer ?? null },
                            paymentMethod: subscriptionData.subscription
                                ? subscriptionData.subscription.collection_method
                                : null,
                            invoiceDetails: {
                                dueDays: subscriptionData.team ? subscriptionData.team.due_days : 0,
                                generateInvoice: false,
                            },
                            coupon: null,
                            memo: '',
                            advancedOptions: {
                                customInvoiceFields: [],
                            } as ISubscriptionAdvancedOptions,
                            previewData: {} as IInvoicingSubscriptionSummary,
                            formViolations: {},
                        },
                    });
                } catch (e) {
                    this.setState({
                        spinner: false,
                        loading: false,
                    });

                    bootbox.alert('Something went wrong..');
                }
            },
        );
    }

    private async getSubscriptionInfo() {
        const teamInfo = await axios.get(`back/teams/${this.props.teamId}`);
        let subscriptionData = null;
        if (teamInfo.data.stripe_subscription) {
            subscriptionData = await axios.get(
                `back/team/billing-dashboard/subscription/${teamInfo.data.stripe_subscription}`,
            );
        }

        return {
            team: teamInfo.data,
            initialSubscription: subscriptionData ? subscriptionData.data : { items: [] },
        };
    }

    private handleDataChange(parts: Partial<IInvoicingSubscriptionFormData>) {
        const formData = this.state.formData;

        this.setState({
            formData: { ...formData, ...parts },
            previewIsValid: false,
        });
    }

    private changeBillingInformationEdit(isEdit: boolean, refreshTeamInfo?: boolean) {
        this.setState({
            isBillingInformationEdit: isEdit,
        });

        if (refreshTeamInfo === true) {
            this.setState(
                {
                    spinner: true,
                },
                async () => {
                    try {
                        const subscription = await this.getSubscriptionInfo();
                        const subscriptionData = subscription.initialSubscription;
                        this.setState({
                            team: subscription.team,
                            formData: {
                                ...this.state.formData,
                                customer: subscriptionData.subscription
                                    ? subscriptionData.customer
                                    : { has_card: false, id: subscription.team.stripe_customer ?? null },
                            },
                        });
                    } catch (e) {
                        bootbox.alert('Something went wrong..');
                    } finally {
                        this.setState({
                            spinner: false,
                        });
                    }
                },
            );
        }
    }

    private async handlePreviewDataChange(parts: Partial<IInvoicingSubscriptionFormData>, formViolations: object) {
        const formData = this.state.formData;
        const needToResetForm = parts.resetFormData;

        this.setState({
            formData: { ...formData, ...parts, ...{ formViolations: formViolations } },
            previewIsValid: !Object.keys(formViolations).length,
        });

        if (needToResetForm === true) {
            $('#team-info').modal('toggle');
        }
    }

    public render() {
        const { spinner, team, formData, loading, previewIsValid, isBillingInformationEdit } = this.state;

        return (
            <>
                <div className={'company-row'}>
                    {!loading && (
                        <div className={'row team-invoicing-tab'}>
                            <InvoicingBillingDetails
                                team={team}
                                isBillingInformationEdit={isBillingInformationEdit}
                                changeBillingInformationEdit={(isEdit: boolean, refreshTeamInfo?: boolean) =>
                                    this.changeBillingInformationEdit(isEdit, refreshTeamInfo)
                                }
                            />
                            <div
                                className={
                                    isBillingInformationEdit || !Object.keys(team.billing_details).length
                                        ? 'hidden'
                                        : ''
                                }
                            >
                                <InvoicingBillingType
                                    formData={formData}
                                    team={team}
                                    handleDataChange={(formData: IInvoicingSubscriptionFormData) =>
                                        this.handleDataChange(formData)
                                    }
                                />
                                {formData.selectedBillingType && (
                                    <InvoicingBillingSummary
                                        formData={formData}
                                        previewIsValid={previewIsValid}
                                        handlePreviewDataChange={(
                                            formData: IInvoicingSubscriptionFormData,
                                            formViolations: any,
                                        ) => this.handlePreviewDataChange(formData, formViolations)}
                                    />
                                )}
                            </div>
                        </div>
                    )}
                </div>
                {spinner && <Spinner />}
            </>
        );
    }
}

export default Invoicing;
