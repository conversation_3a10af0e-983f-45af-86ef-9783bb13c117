window.uniqueScrollerId = Math.random().toString(36).substr(2, 9);

var ProjectSimpleScroller = function (searchString) {
    const EVENT_KEYS_LOADED_INITIAL_PAGE = '@project-scroller/keys_loaded_initial_page';

    var pub = {};
    var _parent = this;
    this.debug = false;

    this.filesSelectionHash = window.filesSelectionHash;

    this.searchMode = false;
    this.cleanList = false;
    this.searchString = searchString ?? '';
    this.searchCaseSensitive = 0;
    this.exactMatch = 0;

    this.firstRun = true;

    this.$document = $(document);
    this.$body = $(document.body);

    this.totalKeyCount = 0;

    this.view = 'multi';
    this.reference_lang_id = null;
    this.single_lang_id = null;
    this.data = {};
    this.session = {};

    this.$endlessContainer = $('#endless');

    this.le_offset_start = 0;
    this.le_offset = 1;
    this.loadingKeysTop = false;
    this.loadingKeysBottom = false;
    this.isAfterSearch = false;
    this.isAfterFilter = false;
    this.isAfterSort = false;
    this.afterMiddlePosition = null;

    // id is used to track user tab and cache filter params on backend
    this.uniqueScrollerId = window.uniqueScrollerId;

    this.$sliderWrap = null;
    this.$sliderElement = null;
    this.$sliderHandle = null;
    this.$sliderHeader = null;
    this.$sliderFooter = null;
    this.$sliderPreview = null;
    this.pageCount = 0;
    this.perPageCount = 0;
    this.disableScrollLoad = false;
    this.disableSliderUpdate = false;
    this.checkSpare = false;
    this.isSpareRunning = false;

    this.screenshotsFilter = null;

    this.keys = {}; // key_id => array (key data)
    this.pages = {}; // page => array (key ids in page)
    this.positions = {};

    this.detached = {};

    this.lastScrollDirection = 1;
    this.lastScrollTop = 0;
    this.lastScrollTopDelta = 5;

    this.cookieLastRemember = 'pplr_'; // project_pagination_last_remember_
    this.lastRemember = false;
    this.cookieLastPosition = 'pplp_'; // project_pagination_last_position_

    this.rememberScrollVisible = null;

    this.$stickyWrapper = null;
    this.stickyState = false;

    /*
     * The URL has to come form the same origin. Most browsers don't allow you to load a Worker from an external
     * source, even if the CORS headers are all in order. Don't prefix it with a CDN path.
     * */
    var _twigWorker = new Worker(`/js/project/twig_worker.js?v=${CACHE_VERSION}`);
    _twigWorker.addEventListener('message', function (e) {
        pub.processWorkerMessage(e.data);
    });
    this.sortMode = 1;
    this.memoizedSortMode = null;
    this.filterMode = 1;
    this.filter = null;
    this.documentId = null;
    this.branch = null;

    // sort switch
    this.sortActiveLabel = $('#project-sort-active-label');
    this.sortDropdown = $('#project-sort-dropdown');

    pub.getDetachedPages = function () {
        return _parent.detached;
    };

    var changeKeyCount = function (keyCount) {
        $('editor-stats').attr('keys-count', keyCount);
        $(document).trigger('scroller:refresh', {
            keyCount: keyCount,
        });
    };

    var changeSourceWordCount = function (sourceWordCount) {
        $('editor-stats').attr('source-word-count', sourceWordCount);
        $(document).trigger('scroller:refresh', {
            sourceWordCount: sourceWordCount,
        });
    };

    const enableEditorEditing = function () {
        //editing was disabled on rendering until lock status was received from the BE call. On scroll, new translations are loaded and now, enable editing after lock status for translations is received.
        [...document.querySelectorAll('.lokalise-editor-wrapper')].forEach(($wrapper) =>
            $wrapper.classList.remove('pointer-disabled'),
        );
    };

    pub.searchDetached = function (queryString, filterOnly) {
        var returnValue = false;
        filterOnly = filterOnly || false;

        if (_parent.detached) {
            $.each(_parent.detached, function () {
                if (!filterOnly) {
                    returnValue = this.find(queryString);
                }
                if (!returnValue || returnValue.length <= 0) {
                    returnValue = this.filter(queryString);
                }

                if (returnValue && returnValue.length > 0) {
                    return false;
                }
            });
        }

        return returnValue;
    };

    pub.p = function () {
        console.log(_parent);
    };

    pub.reset = function () {
        _parent.cleanList = true;
        _parent.le_offset_start = 0;
        _parent.le_offset = 0;
        _parent.detached = {};
    };
    pub.initFinished = function () {
        var spinner = $('#spinner-endless');
        $('#endless-table').show();
        hideSpinner(spinner);
        spinner.hide();
    };

    pub.init = function () {
        $(window).scroll(pub.onScroll);
        _parent.sortMode = sort_mode;
        _parent.filterMode = filter_mode;
        _parent.searchCaseSensitive = search_case_sensitive == 1 ? 1 : 0;
        _parent.exactMatch = exact_match == 1 ? 1 : 0;
        _parent.totalKeyCount = 0; // Will be initialized when data is available
        _parent.sourceWordCount = 0; // Will be initialized when data is available
        _parent.perPageCount =
            keys_per_page == undefined ? _parent.$endlessContainer.find('.row-key').length : keys_per_page;
        _parent.pageCount = Math.ceil(_parent.totalKeyCount / _parent.perPageCount);
        _parent.branch = getCurrentBranch();
        _parent.key = new URLSearchParams(window.location.search).get('k');
        _parent.commentId = new URLSearchParams(window.location.search).get('commentId');
        _parent.comments = new URLSearchParams(window.location.search).get('comments');
        if (current_view === 'single') {
            _parent.view = 'single';
            _parent.reference_lang_id = reference_lang_id;
            _parent.single_lang_id = single_lang_id;
        } else if (current_view === 'document') {
            _parent.view = 'document';
            _parent.reference_lang_id = reference_lang_id;
            _parent.single_lang_id = single_lang_id;
            _parent.documentId = document_id;
            if (_parent.documentId != null && filenames[_parent.documentId]) {
                if (filenames[_parent.documentId].indexOf('.docx') !== -1) {
                    Prism.languages.lokalise = Prism.languages.lokaliseDocx;
                } else {
                    Prism.languages.lokalise = Prism.languages.lokaliseMain;
                }
            }
        }
        pub.initSlider();

        pub.initSticky();

        pub.toggleFooter();
        _parent.checkSpare = true;
        pub.removeSpare();

        const isRememberingPositionActive = getCookie(_parent.cookieLastRemember) === '1';
        const lastRememberedPosition = parseInt(getCookie(_parent.cookieLastPosition));

        if (isRememberingPositionActive && lastRememberedPosition) {
            pub.loadPage(lastRememberedPosition);
        }

        pub.fixCookies();
        if (project_active_filter != null) {
            const keyParamInUrl = new URLSearchParams(window.location.search).get('k');
            if (!keyParamInUrl) {
                _parent.filter = project_active_filter;
            }
        }
        // select all checkbox
        $('#thekey-all').prop('disabled', !!$('#noresults').attr('class'));

        // sort switch
        _parent.sortDropdown.on('click', '.project-sort-item', function () {
            projectScroller.updateSort(this);
        });
        // screenshots
        if ($('#screenshot-filter')) {
            _parent.screenshotsFilter = new Lokalise['screenshots-filter'](
                p_id,
                16,
                function () {
                    pub.applyScreenshotsFilter();
                },
                screenshots_filter_active != undefined ? screenshots_filter_active : null,
                screenshots_tags != undefined ? screenshots_tags : null,
            );
            _parent.screenshotsFilterParams = screenshots_filter_active != undefined ? screenshots_filter_active : null;

            $('editor-options').on('click', '#toggle-screenshots-filter', function () {
                _parent.screenshotsFilter.toggleFilter();
            });
            pub.setCurrentUrl();
        }

        // too large screens, it will load second page
        if (window.innerHeight > pub.getScrollHeight() * 0.8 && _parent.pageCount > 1) {
            pub.loadKeysBottom();
        }
        pub.initEvents();
        document.dispatchEvent(new CustomEvent('@project-scroller/ready'));
    };
    pub.initSlider = function () {
        _parent.cookieLastRemember += pub.getProjectIdForCookie();
        _parent.cookieLastPosition += pub.getProjectIdForCookie();
        _parent.$sliderWrap = $('#pagination-slider-wrap');
        _parent.$sliderElement = $('#pagination-slider');
        _parent.$sliderHeader = $('#pagination-slider-header');
        _parent.$sliderFooter = $('#pagination-slider-footer');
        _parent.$sliderHandle = $('#pagination-slider-handle');
        _parent.$sliderPreview = $('#pagination-slider-preview');
        $('editor-options').on('click', '#toggle-page-slider-remember', function () {
            pub.toggleSliderLastRemember();
        });
        _parent.lastRemember = getCookie(_parent.cookieLastRemember) == '1';

        document
            .querySelector('editor-options')
            .setAttribute('slider-remember', _parent.lastRemember ? 'true' : 'false');

        _parent.$sliderElement.slider({
            orientation: 'vertical',
            min: 1,
            max: _parent.totalKeyCount,
            value: _parent.totalKeyCount,
            create: function () {
                _parent.$sliderHandle.text(1);
            },
            slide: function (event, ui) {
                pub.dispatchScrollEvent();
                _parent.$sliderHandle.text(_parent.totalKeyCount - ui.value + 1);
            },
            stop: function (event, ui) {
                pub.loadPage(_parent.totalKeyCount - ui.value);
            },
        });
        _parent.$sliderHeader.text(1);
        _parent.$sliderFooter.text(_parent.totalKeyCount);
        _parent.$sliderWrap.toggleClass('force-hidden', _parent.totalKeyCount < 10);
        _parent.$sliderWrap.show();
        _parent.$sliderElement.on('mousemove', function (e) {
            pub.sliderPreviewPosition(e);
        });

        _parent.$sliderHeader.on('click', function () {
            pub.dispatchScrollEvent();
            pub.loadPage(0);
        });
        _parent.$sliderFooter.on('click', function () {
            pub.dispatchScrollEvent();
            pub.loadPage(_parent.totalKeyCount - 1);
        });
    };
    pub.updateKeyCount = function ({ keysCount, sourceWordCount }) {
        _parent.totalKeyCount = keysCount;
        _parent.sourceWordCount = sourceWordCount;
        _parent.pageCount = Math.ceil(_parent.totalKeyCount / _parent.perPageCount);

        pub.enableSlider();
        pub.resetSlider();
        pub.setSliderPage();

        $(document).trigger('scroller:refresh', {
            keyCount: keysCount,
            sourceWordCount: sourceWordCount,
        });
    };
    pub.dispatchScrollEvent = function () {
        document.dispatchEvent(new CustomEvent('@project-scroller/before-scroll'));
    };
    pub.initEvents = function () {
        var debounce = null;
        var oneSecond = 1000;
        $(document).on(
            'base_lang_plural_translation_updated base_lang_translation_updated key_removed document_changed',
            function () {
                // Counts are prepared asynchronously, there is no guarantee race condition
                // will be eliminated in given timeout
                clearTimeout(debounce);
                debounce = setTimeout(pub.refreshKeyStats, oneSecond);
            },
        );

        document.addEventListener('file-widget-select-loading', (e) => {
            if (e.detail.loading) {
                showSpinner($('#spinner-main'));
            } else {
                hideSpinner($('#spinner-main'));
            }
        });

        document.addEventListener('file-widget-select', (e) => {
            _parent.filesSelectionHash = e.detail.filesSelectionHash;
            pub.setCurrentUrl();
            pub.searchMode = true;
            pub.setFilter(_parent.filter, true);
        });
    };
    pub.resetSlider = function () {
        if (_parent.$sliderElement == null) {
            return;
        }
        if (_parent.$sliderElement.slider('instance') == undefined) {
            return;
        }
        _parent.$sliderWrap.toggleClass('force-hidden', _parent.pageCount <= 1);
        _parent.$sliderElement.slider('option', {
            max: _parent.totalKeyCount,
            value: _parent.totalKeyCount,
        });
        _parent.$sliderHandle.show().text(1);
        _parent.$sliderFooter.text(_parent.totalKeyCount);
        _parent.$sliderElement.slider('option', 'disabled', false);
        _parent.$sliderHeader.show();
        _parent.$sliderFooter.show();
    };
    pub.disableSlider = function () {
        _parent.$sliderElement.slider('option', 'disabled', true);
        _parent.$sliderHandle.hide();
        _parent.$sliderHeader.hide();
        _parent.$sliderFooter.hide();
    };
    pub.enableSlider = function () {
        _parent.$sliderElement.slider('option', 'disabled', false);
        _parent.$sliderHandle.show();
        _parent.$sliderHeader.show();
        _parent.$sliderFooter.show();
    };
    pub.setData = function (data) {
        _parent.data = data;
    };
    pub.setSession = function (session) {
        _parent.session = session;
    };
    pub.search = function (searchString, options) {
        var trimmedSearchString = $.trim(searchString);
        var searchCaseSensitive = search_case_sensitive;
        var exactMatch = exact_match;
        if (
            searchString != _parent.searchString ||
            (searchCaseSensitive != _parent.searchCaseSensitive && trimmedSearchString != '') ||
            (exactMatch != _parent.exactMatch && trimmedSearchString != '')
        ) {
            if (options?.inProjectSearch) {
                Lokalise['analytics']['ApdexTracker'].begin(Lokalise['analytics']['ApdexEventName'].IN_PROJECT_SEARCH);
            }

            pub.reset();
            _parent.searchString = searchString;
            _parent.key = null;
            _parent.searchCaseSensitive = searchCaseSensitive == 1 ? 1 : 0;
            _parent.exactMatch = exactMatch == 1 ? 1 : 0;
            window.search = searchString;

            if (searchString == '' && _parent.searchMode && _parent.filter == null) {
                _parent.searchMode = false;
            } else {
                _parent.searchMode = true;
            }
            _parent.isAfterSearch = true;

            if (searchString) {
                if (!_parent.memoizedSortMode) {
                    _parent.memoizedSortMode = _parent.sortMode;
                }
            } else if (!searchString && _parent.memoizedSortMode) {
                pub.resetPreviousSorting();
                _parent.memoizedSortMode = null;
            }

            pub.toggleSearchSpinner(true);
            pub.disableSlider();
            pub.loadKeysBottom(options);

            Lokalise['analytics']['default'](Lokalise['analytics']['AnalyticsEventName'].SEARCHED_EDITOR, {
                query: searchString,
            });
            document.dispatchEvent(new CustomEvent('@key-stats/update'));
        } else if (searchCaseSensitive != _parent.searchCaseSensitive && trimmedSearchString == '') {
            _parent.searchCaseSensitive = searchCaseSensitive == 1 ? 1 : 0;
            document.dispatchEvent(new CustomEvent('@key-stats/update'));
        } else if (exactMatch != _parent.exactMatch && trimmedSearchString == '') {
            _parent.exactMatch = exactMatch == 1 ? 1 : 0;
            document.dispatchEvent(new CustomEvent('@key-stats/update'));
        }
    };
    pub.toggleSearchSpinner = function (isSearching) {
        document.querySelector('editor-search').setAttribute('loading', !!isSearching);
    };
    pub.setFilter = function (filter, enableSearchMode) {
        _parent.filter = filter;
        _parent.isAfterFilter = true;
        pub.reset();

        if (enableSearchMode) {
            _parent.searchMode = true;
        } else if ((_parent.searchString == '' || !_parent.searchMode) && _parent.filter == null) {
            _parent.searchMode = false;
        } else {
            _parent.searchMode = true;
        }
        projectScroller.setCurrentUrl();
        pub.disableSlider();
        pub.loadKeysBottom();
        document.dispatchEvent(new CustomEvent('@key-stats/update'));
    };
    pub.getFilter = function () {
        return _parent.filter;
    };
    pub.getSearchString = function () {
        return _parent.searchString;
    };
    pub.resetPreviousSorting = function () {
        pub.updateSort($(`[data-sort-mode=${_parent.memoizedSortMode}]`), false);
    };
    pub.loadKeysBottom = function (options) {
        if (
            _parent.loadingKeysBottom ||
            (_parent.pageCount <= _parent.le_offset && !_parent.isAfterSearch && !_parent.isAfterFilter) ||
            _parent.data.no_results === 'no-languages'
        ) {
            return;
        }

        _parent.loadingKeysBottom = true;
        if (options?.inProjectSearch) {
            Lokalise['analytics']['ApdexTracker'].sendRequest(
                Lokalise['analytics']['ApdexEventName'].IN_PROJECT_SEARCH,
            );
        }

        $.ajax({
            url: '/project/' + p_id + '/editor-search/',
            dataType: 'json',
            data: {
                view: _parent.view,
                reference_lang_id: _parent.reference_lang_id,
                single_lang_id: _parent.single_lang_id,
                mode: 'endless',
                offset: _parent.le_offset,
                search: _parent.searchString,
                search_case_sensitive: _parent.searchCaseSensitive,
                exact_match: _parent.exactMatch,
                filter: _parent.filter,
                reset_filter: _parent.filter == null ? 1 : 0,
                screenshots_filter: pub.formatScreenshotFilterParams(),
                reset_screenshots_filter: pub.formatScreenshotFilterParams() == null ? 1 : 0,
                sort_mode: _parent.sortMode,
                document_id: _parent.documentId,
                branch: _parent.branch,
                files_selection: _parent.filesSelectionHash ?? '',
            },
            success: function (data) {
                if (options?.inProjectSearch) {
                    Lokalise['analytics']['ApdexTracker'].responseReceived(
                        Lokalise['analytics']['ApdexEventName'].IN_PROJECT_SEARCH,
                    );
                }

                let s = data.data.search == null ? '' : data.data.search;
                if ((_parent.isAfterSearch || _parent.isAfterFilter) && s !== _parent.searchString) {
                    if (_parent.isAfterSearch) {
                        pub.toggleSearchSpinner(true);
                    }
                    setTimeout(function () {
                        pub.reset();
                        _parent.isAfterSearch = true;
                        pub.disableSlider();
                        pub.loadKeysBottom();
                    }, 50);
                    return;
                }
                if (data.data.view === 'single') {
                    _parent.view = 'single';
                } else if (data.data.view === 'document') {
                    _parent.view = 'document';
                }

                if (data.key_positions.length > 0) {
                    _parent.le_offset++;
                }

                if (pub.filterModeCheck(data.filter_mode)) {
                    return;
                }

                _parent.perPageCount = data.keys_count_per_page;
                _parent.pageCount = Math.ceil(_parent.totalKeyCount / _parent.perPageCount);

                if (_parent.isAfterSearch || _parent.isAfterFilter) {
                    if (_parent.isAfterSearch) {
                        pub.toggleSearchSpinner(false);
                    }
                    _parent.disableScrollLoad = true;
                    _parent.$document.scrollTop(0);
                    _parent.disableScrollLoad = false;
                    _parent.isAfterSearch = false;
                    _parent.isAfterFilter = false;
                }

                $.each(data.referenced_keys, function (akey, referenceData) {
                    if (!ReferencedKeys[akey]) {
                        ReferencedKeys[akey] = referenceData;
                    } else {
                        ReferencedKeys[akey].langs = referenceData.langs;
                        ReferencedKeys[akey].data = referenceData.data;
                        ReferencedKeys[akey].referenced_in = {
                            ...ReferencedKeys[akey].referenced_in,
                            ...referenceData.referenced_in,
                        };
                    }
                });
                _parent.data = data.data;
                _parent.session = data.session;
                pub.addKeysToCache(data);
                pub.drawKeysBottom({ ...data, options }, false, 2);
                // screenshots
                _parent.screenshots = data.data.screenshots_data;

                projectScroller.setCurrentUrl();
                $(document).trigger('scroller:search:completed', {
                    search: _parent.searchString,
                });

                document.dispatchEvent(new Event('editor-load-keys-finished'));
            },
            beforeSend: function () {
                showSpinner($('#spinner-endless'));
                $('#footer').hide();
            },
            complete: function () {
                _parent.loadingKeysBottom = false;
                if (_parent.firstRun) {
                    pub.initFinished();
                }
                hideSpinner($('#spinner-main'));
                hideSpinner($('#spinner-endless'));
                document.dispatchEvent(new CustomEvent('@project-scroller/loading-after-scroll-complete'));
            },
        });
    };
    pub.loadKeysTop = function () {
        if (_parent.loadingKeysTop || _parent.le_offset_start == 0) {
            return;
        }
        _parent.loadingKeysTop = true;
        $.ajax({
            url: '/project/' + p_id + '/editor-search/',
            dataType: 'json',
            data: {
                view: _parent.view,
                reference_lang_id: _parent.reference_lang_id,
                single_lang_id: _parent.single_lang_id,
                mode: 'endless',
                offset: _parent.le_offset_start - 1,
                search: _parent.searchString,
                search_case_sensitive: _parent.searchCaseSensitive,
                exact_match: _parent.exactMatch,
                filter: _parent.filter,
                reset_filter: _parent.filter == null ? 1 : 0,
                screenshots_filter: pub.formatScreenshotFilterParams(),
                reset_screenshots_filter: pub.formatScreenshotFilterParams() == null ? 1 : 0,
                sort_mode: _parent.sortMode,
                document_id: _parent.documentId,
                branch: _parent.branch,
                files_selection: _parent.filesSelectionHash ?? '',
            },
            success: function (data) {
                if (data.data.view === 'single') {
                    _parent.view = 'single';
                } else if (data.data.view === 'document') {
                    _parent.view = 'document';
                }

                if (data.key_positions.length > 0) {
                    _parent.le_offset_start--;
                }

                if (pub.filterModeCheck(data.filter_mode)) {
                    return;
                }

                _parent.perPageCount = data.keys_count_per_page;
                _parent.pageCount = Math.ceil(_parent.totalKeyCount / _parent.perPageCount);

                $.each(data.referenced_keys, function (akey, referenceData) {
                    if (!ReferencedKeys[akey]) {
                        ReferencedKeys[akey] = referenceData;
                    } else {
                        ReferencedKeys[akey].langs = referenceData.langs;
                        ReferencedKeys[akey].data = referenceData.data;
                        ReferencedKeys[akey].referenced_in = {
                            ...ReferencedKeys[akey].referenced_in,
                            ...referenceData.referenced_in,
                        };
                    }
                });

                _parent.data = data.data;
                _parent.session = data.session;
                pub.addKeysToCache(data);
                pub.drawKeysTop(data);
                // screenshots
                _parent.screenshots = data.data.screenshots_data;
                document.dispatchEvent(new Event('editor-load-keys-finished'));
            },
            beforeSend: function () {
                showSpinner($('#spinner-endless-top'));
            },
            complete: function () {
                _parent.loadingKeysTop = false;
                hideSpinner($('#spinner-endless-top'));
                document.dispatchEvent(new CustomEvent('@project-scroller/loading-after-scroll-complete'));
            },
        });
    };
    pub.getFilterParams = function () {
        return {
            view: _parent.view,
            reference_lang_id: _parent.view !== 'multi' ? _parent.reference_lang_id : undefined,
            single_lang_id: _parent.view !== 'multi' ? _parent.single_lang_id : undefined,
            search: _parent.searchString,
            search_case_sensitive: _parent.searchCaseSensitive,
            exact_match: _parent.exactMatch,
            filter: _parent.filter,
            reset_filter: _parent.filter == null ? 1 : 0,
            screenshots_filter: pub.formatScreenshotFilterParams(),
            reset_screenshots_filter: pub.formatScreenshotFilterParams() == null ? 1 : 0,
            sort_mode: _parent.sortMode,
            document_id: _parent.documentId,
            branch: _parent.branch ?? undefined,
            files_selection: _parent.filesSelectionHash ?? '',
        };
    };
    pub.loadKeysMiddle = function () {
        _parent.disableScrollLoad = true;
        $.ajax({
            url: '/project/' + p_id + '/editor-search/',
            dataType: 'json',
            data: {
                view: _parent.view,
                reference_lang_id: _parent.reference_lang_id,
                single_lang_id: _parent.single_lang_id,
                mode: 'endless',
                offset: _parent.le_offset,
                jump: 1,
                search: _parent.searchString,
                search_case_sensitive: _parent.searchCaseSensitive,
                exact_match: _parent.exactMatch,
                filter: _parent.filter,
                reset_filter: _parent.filter == null ? 1 : 0,
                screenshots_filter: pub.formatScreenshotFilterParams(),
                reset_screenshots_filter: pub.formatScreenshotFilterParams() == null ? 1 : 0,
                sort_mode: _parent.sortMode,
                document_id: _parent.documentId,
                branch: _parent.branch,
                files_selection: _parent.filesSelectionHash ?? '',
            },
            success: function (data) {
                if (data.data.view === 'single') {
                    _parent.view = 'single';
                } else if (data.data.view === 'document') {
                    _parent.view = 'document';
                }
                if (data.key_positions.length > 0) {
                    _parent.le_offset++;
                }
                if (data.key_positions.length > 0 && _parent.isAfterSort) {
                    // jump param in request means we've requested two pages
                    // increase offset again to avoid retrieving the same keys
                    // necessary only for sorting as it doesn't trigger loadKeysBottom or loadKeysTop
                    _parent.isAfterSort = false;
                    _parent.le_offset++;
                }

                if (pub.filterModeCheck(data.filter_mode)) {
                    return;
                }

                _parent.perPageCount = data.keys_count_per_page;
                _parent.pageCount = Math.ceil(_parent.totalKeyCount / _parent.perPageCount);

                $.each(data.referenced_keys, function (akey, referenceData) {
                    if (!ReferencedKeys[akey]) {
                        ReferencedKeys[akey] = referenceData;
                    } else {
                        ReferencedKeys[akey].langs = referenceData.langs;
                        ReferencedKeys[akey].data = referenceData.data;
                        ReferencedKeys[akey].referenced_in = {
                            ...ReferencedKeys[akey].referenced_in,
                            ...referenceData.referenced_in,
                        };
                    }
                });

                _parent.data = data.data;
                _parent.session = data.session;
                pub.addKeysToCache(data, data.offset > 0 ? true : false);
                _parent.cleanList = true;
                pub.drawKeysBottom(data, data.offset > 0 ? true : false, 1);
                // screenshots
                _parent.screenshots = data.data.screenshots_data;

                document.dispatchEvent(new Event('editor-load-keys-finished'));
            },
            beforeSend: function () {
                showSpinner($('#spinner-endless'));
            },
            fail: function () {
                pub.completeKeysLoading();
            },
            complete: function () {
                hideSpinner($('#spinner-endless'));
                document.dispatchEvent(new CustomEvent('@project-scroller/loading-after-scroll-complete'));
            },
        });
    };
    pub.completeKeysLoading = function () {
        _parent.disableScrollLoad = false;
        _parent.isAfterSort = false;
        hideSpinner();
    };

    pub.addKeysToCache = function (data, two_pages) {
        if (two_pages == true) {
            _parent.pages[data.offset - 1] = [];
            _parent.pages[data.offset] = [];
            $.each(data.key_positions, function (index, keyObject) {
                _parent.positions[keyObject.position] = keyObject.id;
                _parent.keys[keyObject.id] = data.keys[keyObject.id];
                if (index < _parent.perPageCount) {
                    _parent.pages[data.offset - 1].push(keyObject.id);
                } else {
                    _parent.pages[data.offset].push(keyObject.id);
                }
            });
        } else {
            _parent.pages[data.offset] = [];
            $.each(data.key_positions, function (index, keyObject) {
                _parent.positions[keyObject.position] = keyObject.id;
                _parent.keys[keyObject.id] = data.keys[keyObject.id];
                _parent.pages[data.offset].push(keyObject.id);
            });
        }
    };

    pub.processWorkerMessage = function (data) {
        if (data.cleanList) {
            _parent.$body.find('.tooltip.fade.top.in')?.remove();
            _parent.$endlessContainer.html('');
        }
        $('#noresults').removeClass('no-filtered no-empty no-document');
        const empty = $('project-editor').attr('empty');
        if (empty === '1') {
            $('project-editor').attr('empty', '0');
        }

        // Always false on first render as searchMode is not initialised on page load
        if (_parent.searchMode) {
            $('#page-counter').hide();
            $('#paginator').hide();
            if (data.responseData.key_positions.length == 0 && data.cleanList) {
                $('#endless-keys-container').hide();
                pub.toggleFooter();
                changeKeyCount(_parent.totalKeyCount);
                changeSourceWordCount(_parent.sourceWordCount);
                $('#noresults').addClass('no-filtered');
                $('#thekey-all').prop('disabled', true);
                $('#thekey-all').prop('checked', false);
                $('#thekey-all').prop('checked', false);
                $('#floater').hide();
                return;
            } else {
                $('#endless-keys-container').show();
                if (!$('#noresults').attr('class')) {
                    $('#thekey-all').prop('disabled', false);
                    // we need to wait for the checkboxes to be rendered
                    setTimeout(() => {
                        if ($('input.thekey:not(#thekey-all):checked').length) {
                            $('#thekey-all').prop('checked', true);
                            $('#floater').show();
                        }
                    }, 0);
                }
                pub.showEditorSidePanel();
            }
        } else {
            const noKeysToDisplay = data.responseData.key_positions.length == 0;
            const noKeysInProject = data.responseData.data.all_keys_count == 0;

            if (noKeysToDisplay || noKeysInProject) {
                const noResultsReason = noKeysInProject ? 'no-empty' : 'no-filtered';

                $('#endless-keys-container').hide();
                pub.toggleFooter();
                changeKeyCount(_parent.totalKeyCount);
                // Controls whether user sees the "No search results" or "Set-up your project" screen
                $('#noresults').addClass(noResultsReason);
                $('project-editor').attr('empty', noKeysInProject ? '1' : '0');

                $('#thekey-all').prop('disabled', true);
                $('#thekey-all').prop('checked', false);
                $('#floater').hide();
                return;
            } else {
                $('#endless-keys-container').show();
                $('#endless-head').show();
                $('#page-counter').show();
                $('#paginator').show();
                if (!$('#noresults').attr('class')) {
                    $('#thekey-all').prop('disabled', false);
                    // we need to wait for the checkboxes to be rendered
                    setTimeout(() => {
                        if ($('input.thekey:not(#thekey-all):checked').length) {
                            $('#thekey-all').prop('checked', true);
                            $('#floater').show();
                        }
                    }, 0);
                }
                pub.showEditorSidePanel();
            }
        }
        if (data.cmd === 'reDrawKey') {
            // Only used for multi view
            $(`#load-more-segments-${data.keyId}`).remove();
            // Replace keyrowhead - used for single and multi view
            $(`#keyrowhead-${data.keyId}`).replaceWith($.parseHTML(data.newKey));
            // Highlight placeholders if enabled
            _editor.highlightAll();
        }

        const newKeysHtml = $.parseHTML(data.newKeysString);

        if (data.cmd === 'drawKeysBottom') {
            changeKeyCount(_parent.totalKeyCount);
            changeSourceWordCount(_parent.sourceWordCount);

            if (data.two_pages) {
                var $tempDomPageBefore = $('<div class="page"></div>').append(
                    $.parseHTML(data.newKeysStringPageBefore),
                );
                $tempDomPageBefore.attr('data-offset', data.responseData.offset - 1);
                init_stuff($tempDomPageBefore);
                _editor.highlightAll($tempDomPageBefore);
                _parent.$endlessContainer.append($tempDomPageBefore);
            }

            let $tempDom;

            if (window.is_document_or_content_project) {
                if ($('.page').length) {
                    $tempDom = $('.page').append(newKeysHtml);
                } else {
                    $tempDom = $('<div class="page"></div>').append(newKeysHtml);
                }

                // remove duplicated headers
                data.filesWithKeys.forEach(({ filename_id }) => {
                    const allHeaders = document.querySelectorAll(`.js-file-name-header[data-file-id="${filename_id}"]`);

                    allHeaders.forEach((header, index) => {
                        if (index !== 0) {
                            header.remove();
                        }
                    });
                });
            } else {
                $tempDom = $('<div class="page"></div>').append(newKeysHtml);
            }

            $tempDom.attr('data-offset', data.responseData.offset);

            init_stuff($tempDom);
            _editor.highlightAll($tempDom);

            _parent.$endlessContainer.append($tempDom);

            changeKeyCount(_parent.totalKeyCount);
            _parent.checkSpare = true;
            pub.removeSpare();
            pub.toggleFooter();

            if (data.type == 1) {
                if (_parent.afterMiddlePosition != null) {
                    pub.scrollToPosition(_parent.afterMiddlePosition);
                    _parent.afterMiddlePosition = null;
                } else {
                    pub.scrollToPosition(pub.getScrollValue(), false);
                }
                _parent.lastScrollTop = _parent.$document.scrollTop();
                _parent.lastScrollDirection = 1;
                pub.onScroll();
                pub.completeKeysLoading();
            } else if (data.type == 2) {
                _parent.firstRun = false;
            }
        } else if (data.cmd === 'drawKeysTop') {
            const $tempDom = $('<div class="page"></div>').append(newKeysHtml);
            $tempDom.attr('data-offset', data.responseData.offset);
            init_stuff($tempDom);
            _editor.highlightAll($tempDom);
            pub.rememberScroll();
            _parent.$endlessContainer.prepend($tempDom);
            pub.restoreScroll();
            changeKeyCount(_parent.totalKeyCount);
            _parent.checkSpare = true;
            pub.removeSpare();
        }
        enableEditorEditing();
        const newTranslationIds = pub.getReloadedKeysTranslationIds(data.responseData.keys);

        //On every key-view update, new keys are loaded and locks for editing needs to be updated
        if (newTranslationIds.length && $('.js-legacy-editor-wrapper').length) {
            getTranslationLockStatus(newTranslationIds);
        }
        if (data.responseData.options?.inProjectSearch) {
            Lokalise['analytics']['ApdexTracker'].rendered(Lokalise['analytics']['ApdexEventName'].IN_PROJECT_SEARCH, {
                projectId: data.responseData.data.projectId,
            });
        }
    };

    pub.drawKeysBottom = function (responseData, two_pages, type) {
        _twigWorker.postMessage({
            type: type,
            cmd: 'drawKeysBottom',
            responseData: responseData,
            two_pages: two_pages,
            perPageCount: _parent.perPageCount,
            data: _parent.data,
            thekeys: UserSession.thekeys,
            session: _parent.session,
            view: _parent.view,
            is_document_project: window.is_document_project,
            is_document_or_content_project: window.is_document_or_content_project,
            showContentPreview: window.showContentPreview,
            CACHE_VERSION: CACHE_VERSION,
            DEFINES: DEFINES,
            ENUMS: window.ENUMS,
            cleanList: _parent.cleanList,
            darkmode: darkmode,
            env: window.env,
        });

        _parent.cleanList = false;
    };
    pub.drawKeysTop = function (responseData) {
        _twigWorker.postMessage({
            cmd: 'drawKeysTop',
            responseData: responseData,
            perPageCount: _parent.perPageCount,
            data: _parent.data,
            thekeys: UserSession.thekeys,
            session: _parent.session,
            view: _parent.view,
            CACHE_VERSION: CACHE_VERSION,
            DEFINES: DEFINES,
            ENUMS: window.ENUMS,
            cleanList: _parent.cleanList,
            darkmode: darkmode,
            env: window.env,
        });

        _parent.cleanList = false;
    };
    pub.createKeyElementFromData = function (data, asString) {
        asString = asString || false;

        if (!_parent.data.view) {
            _parent.data.view = _parent.view;
        }

        var output = TwigTemplates['views/project/' + _parent.view + '/key.twig'].render({
            data: {
                ..._parent.data,
                is_project_admin: _parent.data.p.is_project_admin,
                user: {
                    ..._parent.data.user,
                    permissions: _parent.data.user.permissions ?? _parent.data.user.admin_rights,
                },
            },
            session: _parent.session,
            position: data.position,
            key: data,
            thekeys: _parent.session.thekeys[p_id],
        });

        output += '';

        if (!asString) {
            var $newElement = $(output);
            $newElement.position = data.position;
            init_stuff($newElement);

            return $newElement;
        } else {
            return output;
        }
    };

    pub.onScroll = function () {
        pub.scrollDirection();
        if (_parent.disableScrollLoad) {
            return;
        }

        if (!_parent.loadingKeysBottom && !_parent.loadingKeysTop && _parent.pageCount > 0) {
            var first_child = _parent.$endlessContainer.find('.page:first-child');
            var last_child = _parent.$endlessContainer.find('.page:last-child');
            var first_size = first_child.length ? first_child.offset().top + first_child.height() : 2000;
            var last_size = last_child.length ? last_child.height() : 2000;
            var last_offset = last_child.length ? last_child.offset().top : 0;
            var bottomCutoffPoint = last_offset + last_size * 0.8;
            if (
                _parent.lastScrollDirection == 1 &&
                bottomCutoffPoint < _parent.$document.scrollTop() + window.innerHeight
            ) {
                pub.loadKeysBottom();
            } else if (
                _parent.le_offset_start > 0 &&
                _parent.lastScrollDirection == -1 &&
                _parent.$document.scrollTop() < first_size * 0.8
            ) {
                pub.loadKeysTop();
            }
        }
        _parent.checkSpare = true;
        pub.removeSpare();
        enableEditorEditing();
        if (!_parent.disableSliderUpdate) {
            pub.setSliderPage();
        }
    };
    pub.removeSpare = function () {
        if (_parent.isSpareRunning) {
            return;
        }
        _parent.isSpareRunning = true;
        _parent.checkSpare = false;

        const top = _parent.$document.scrollTop();
        const bottom = top + window.innerHeight;

        var $pages = _parent.$endlessContainer.find('.page');
        var $page = pub.getVisiblePage();
        if ($page == null) {
            pub.toggleFooter();
            return;
        }
        $pages.removeClass('adjecent current');
        $page.addClass('current');

        let $prev = $page.prev();
        do {
            $prev.addClass('adjecent');
            if ($prev.hasClass('empty')) {
                pub.loadPageCache($prev);
            }
            $prev = $prev.prev();
        } while ($prev.length && $prev.offset().top + $prev.height() > top);

        let $next = $page.next();
        do {
            $next.addClass('adjecent');
            if ($next.hasClass('empty')) {
                pub.loadPageCache($next);
            }
            $next = $next.next();
        } while ($next.length && $next.offset().top < bottom);

        if ($page.hasClass('empty')) {
            pub.loadPageCache($page);
        }

        $pages.not('.adjecent, .current, .empty, .editable-trans-opened').each(function () {
            pub.removePage(this);
        });

        pub.toggleFooter();
        _parent.isSpareRunning = false;
        if (_parent.checkSpare) {
            pub.removeSpare();
        }
    };
    pub.removePage = function ($page) {
        $page = $($page);
        const offset = $page.data('offset');
        const $elems = $page.find('.row-key');
        if ($elems.length == 0) {
            return;
        }
        $page.height($page.height());
        _parent.detached[offset] = $elems;

        const $tooltippedItems = $('.tippy-popper');
        for (var i = 0, count = $tooltippedItems.length; i < count; i++) {
            var $tooltipItem = $tooltippedItems.eq(i);
            var tooltip = $tooltipItem.data('tippy');
            if (tooltip) {
                tooltip.destroy();
            }
        }

        $elems.detach();
        $page.addClass('empty');
    };
    pub.loadPageCache = function ($page) {
        var offset = $page.data('offset');
        if (_parent.detached[offset] == undefined) {
            return;
        }
        $page.append(_parent.detached[offset]);
        _parent.$document.trigger('editor_page_reattached', [_parent.detached[offset], offset]);

        $page.find('.row-trans').each(function () {
            checkTranslationLock($(this).data('id'));
        });
        // TODO: check this out for Safari
        $page.height('');
        $page.removeClass('empty');
    };

    // slider
    pub.setSliderPage = function () {
        var curr = pub.getVisibleElement();
        if (!curr) {
            return;
        }
        var i = parseInt(curr.data('position')) + 1;
        _parent.$sliderElement.slider('value', _parent.totalKeyCount - i + 1);
        _parent.$sliderHandle.text(i);
        pub.storeScrollPosition();
    };
    pub.getVisibleElement = function () {
        var closest = null;
        var keys = _parent.$endlessContainer.find('.row-key');
        if (keys.length > 0) {
            closest = keys.eq(0);
        }
        keys.each(function () {
            closest = $(this);
            if ($(this).offset().top > _parent.$document.scrollTop()) {
                return false;
            }
        });
        return closest;
    };
    pub.getVisiblePage = function () {
        var scrollTarget = _parent.$document.scrollTop() + window.innerHeight / 2;
        var closest = null;
        var pages = _parent.$endlessContainer.find('.page');
        if (pages.length > 0) {
            closest = pages.eq(0);
        }
        pages.each(function () {
            if ($(this).offset().top < scrollTarget) {
                closest = $(this);
            } else {
                return false;
            }
        });
        return closest;
    };
    pub.loadPage = function (position, force) {
        var page = Math.floor(position / _parent.perPageCount);
        // if page is already loaded then we just jump to that page
        if (_parent.le_offset_start <= page && _parent.le_offset - 1 >= page && force !== true) {
            var target = _parent.$endlessContainer.find('.page[data-offset="' + page + '"]');
            if (target.length && target.hasClass('empty')) {
                pub.loadPageCache(target);
            }
            pub.scrollToPosition(position, true);
        } else {
            _parent.le_offset_start = page == 0 ? 0 : page - 1;
            _parent.le_offset = page;
            _parent.afterMiddlePosition = position;
            pub.loadKeysMiddle();
            const pages = _parent.$endlessContainer.find('.page');
            // Optimistically remove the loaded pages to improve the page responsiveness
            pages.each(function () {
                pub.removePage(this);
            });
            document.dispatchEvent(new CustomEvent('@project-scroller/loading-after-scroll'));
            hideSpinner($('#spinner-endless'));
        }
    };
    pub.scrollToPosition = function (pos, animation) {
        let target;
        if (pos !== 0) {
            target = _parent.$endlessContainer.find('.row-key[data-position="' + pos + '"]');
        } else {
            // Special case when we want to show page header too
            target = _parent.$document.find('.sticky-wrapper-parent');
        }
        var stickyHeight = $('#sticky-wrapper').outerHeight();
        if (!target.length) {
            return;
        }
        if (animation) {
            _parent.disableSliderUpdate = true;
            // console.log('target', target.offset().top - stickyHeight);
            $('html, body').animate(
                {
                    scrollTop: target.offset().top - stickyHeight,
                },
                1000,
                'swing',
                function () {
                    _parent.disableSliderUpdate = false;
                    pub.setSliderPage();
                },
            );
        } else {
            _parent.$document.scrollTop(target.offset().top - stickyHeight);
        }
    };
    pub.scrollToPage = function (page, animation) {
        var target = _parent.$endlessContainer.find('.page[data-offset="' + page + '"]');
        var stickyHeight = $('#sticky-wrapper').height();
        if (!target.length) {
            return;
        }
        if (animation) {
            _parent.disableSliderUpdate = true;
            $('body').animate(
                {
                    scrollTop: target.offset().top - stickyHeight,
                },
                1000,
                'swing',
                function () {
                    _parent.disableSliderUpdate = false;
                },
            );
        } else {
            _parent.$document.scrollTop(target.offset().top - stickyHeight);
        }
    };
    pub.getScrollHeight = function () {
        var body = document.body,
            html = document.documentElement;
        return Math.max(body.scrollHeight, body.offsetHeight, html.clientHeight, html.scrollHeight, html.offsetHeight);
    };
    pub.rememberScroll = function () {
        _parent.rememberScrollVisible = pub.getVisibleElement();
    };
    pub.restoreScroll = function () {
        var visible = pub.getVisibleElement();
        if (_parent.rememberScrollVisible == null || visible == null || visible.is(_parent.rememberScrollVisible)) {
            return;
        }
        _parent.disableScrollLoad = true;
        _parent.$document.scrollTop(
            _parent.$document.scrollTop() -
                (visible.offset().top - _parent.rememberScrollVisible.offset().top) +
                (_parent.lastScrollDirection == -1 ? -200 : 200),
        );
        // to keep old scroll direction
        _parent.lastScrollTop = _parent.$document.scrollTop();
        _parent.disableScrollLoad = false;
    };
    pub.getScrollValue = function () {
        return _parent.totalKeyCount - _parent.$sliderElement.slider('value');
    };
    pub.sliderPreviewPosition = function (e) {
        var y = e.pageY - _parent.$sliderElement.offset().top;
        var h = _parent.$sliderElement.height();
        if (y < 0) {
            y = 0;
        } else if (y > h) {
            y = h;
        }
        var bottom = (1 - y / h) * 100;
        var pos = Math.ceil(_parent.totalKeyCount * (y / h));
        if (pos == 0) {
            pos = 1;
        }
        _parent.$sliderPreview.css({ bottom: bottom + '%' }).text(pos);
    };
    pub.storeScrollPosition = function () {
        setCookie(_parent.cookieLastPosition, pub.getScrollValue(), 7);
    };
    pub.toggleSliderLastRemember = function () {
        if (getCookie(_parent.cookieLastRemember) == '1') {
            setCookie(_parent.cookieLastRemember, 0, 7);
            _parent.lastRemember = false;
            Lokalise['analytics']['default'](
                Lokalise['analytics']['AnalyticsEventName'].EDITOR_TOGGLE_REMEMBER_POSITION_CLICKED,
                {
                    state: 'off',
                },
            );
        } else {
            setCookie(_parent.cookieLastRemember, 1, 7);
            _parent.lastRemember = true;
            Lokalise['analytics']['default'](
                Lokalise['analytics']['AnalyticsEventName'].EDITOR_TOGGLE_REMEMBER_POSITION_CLICKED,
                {
                    state: 'on',
                },
            );
        }

        document
            .querySelector('editor-options')
            .setAttribute('slider-remember', _parent.lastRemember ? 'true' : 'false');
    };

    // sticky
    pub.initSticky = function () {
        _parent.$stickyWrapper = $('#sticky-wrapper');
        if (_parent.$stickyWrapper.length) {
            _parent.$stickyWrapper.parent().addClass('sticky-wrapper-parent');
            if (_parent.$stickyWrapper.siblings('#sticky-wrapper-placeholder').length == 0) {
                var placeholder = $('<div></div>').attr('id', 'sticky-wrapper-placeholder');
                _parent.$stickyWrapper.after(placeholder);
            }
            _parent.startStickyPos = _parent.$stickyWrapper.get(0).getBoundingClientRect().top;
            $(window).scroll(function () {
                window.requestAnimationFrame(pub.checkProjectSticky);
            });
        }
    };
    pub.checkProjectSticky = function () {
        if (_parent.$stickyWrapper == null || !_parent.$stickyWrapper.length) {
            return;
        }
        pub.setStickySize();
        let scrollTop = window.scrollY || window.pageYOffset;
        if (scrollTop - _parent.startStickyPos > 0.1 || _parent.le_offset_start > 0) {
            pub.setStickyState(true);
        } else {
            pub.setStickyState(false);
        }
    };
    pub.setStickyState = function (state) {
        if (_parent.stickyState == state) {
            return;
        }
        _parent.stickyState = state;

        var tooltipsSticky = $('#sticky-wrapper .tooltip-sticky, #sticky-wrapper .tooltip-sticky .tooltip-holder');
        const sidePanel = $('#editorSidePanel');

        if (state) {
            const headerHeight = $('#sticky-wrapper')?.[0].getBoundingClientRect().height;
            const MAGIC_SCROLL_TOP_PADDING = 12;

            if (sidePanel.length) {
                $('#editorSidePanel').addClass('sticky-right-panel').css('top', `${headerHeight}px`);
            }

            if ($('#pagination-slider-wrap').length) {
                $('#pagination-slider-wrap').css('top', `${headerHeight + MAGIC_SCROLL_TOP_PADDING}px`);
            }

            $('body').addClass('sticky-key-function-button-over--fixed');

            // Sticky tooltips should be fully reinitialized with inline style for proper positioning
            tooltipsSticky
                .data(
                    'template',
                    '<div class="tooltip" role="tooltip" style="position: fixed;"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',
                )
                .tooltip('destroy');
            setTimeout(function () {
                tooltipsSticky.tooltip({ delay: { show: 600, hide: 0 } });
            }, 200);
        } else {
            $('body').removeClass('sticky-key-function-button-over--fixed');

            if ($('#editorSidePanel').length) {
                $('#editorSidePanel').removeClass('sticky-right-panel').css('top', '');
            }

            if ($('#pagination-slider-wrap').length) {
                $('#pagination-slider-wrap').css('top', '');
            }

            tooltipsSticky
                .data(
                    'template',
                    '<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',
                )
                .tooltip('destroy');
            setTimeout(function () {
                tooltipsSticky.tooltip({ delay: { show: 600, hide: 0 } });
            }, 200);
        }
    };
    pub.setStickySize = function () {
        var h = _parent.$stickyWrapper.outerHeight();
        _parent.$stickyWrapper.siblings('#sticky-wrapper-placeholder').height(h);
    };

    pub.scrollDirection = function () {
        var st = _parent.$document.scrollTop();
        if (Math.abs(_parent.lastScrollTop - st) <= _parent.lastScrollTopDelta) {
            return;
        }
        _parent.lastScrollDirection = st > _parent.lastScrollTop ? 1 : -1;
        _parent.lastScrollTop = st;
    };

    pub.getProjectIdForCookie = function () {
        return p_id.replace('.', '_');
    };

    pub.toggleFooter = function () {
        if (_parent.le_offset == _parent.pageCount) {
            $('#footer').show();
            return;
        }
        if (_parent.pageCount <= 1) {
            $('#footer').show();
            return;
        }
        $('#footer').hide();
    };

    pub.keyDeleted = function () {
        var position = pub.getScrollValue();
        if (position >= _parent.totalKeyCount - 1) {
            position = _parent.totalKeyCount - 1 >= 0 ? _parent.totalKeyCount - 1 : 0;
        }
        pub.loadPage(position, true);
    };
    pub.keyAdded = function (new_key) {
        switch (_parent.sortMode) {
            case 1:
            case 2:
            case 4:
                //load first page
                pub.loadPage(0, true);
                break;
        }
    };

    pub.replaceKey = function (keyId) {
        $.ajax({
            url: `/project/${p_id}/keys?key_ids[]=${keyId}`,
            dataType: 'json',
            success: (data) => {
                _twigWorker.postMessage({
                    cmd: 'reDrawKey',
                    responseData: data,
                    view: _parent.view,
                    selectedKeys: UserSession.thekeys,
                    referenceLangId: UserSession.reference_lang_id,
                    singleLangId: UserSession.single_lang_id,
                    DEFINES: DEFINES,
                    ENUMS: window.ENUMS,
                    darkmode: darkmode,
                    env: window.env,
                    is_document_project: window.is_document_project,
                    is_document_or_content_project: window.is_document_or_content_project,
                });
            },
            error: () => {
                showApplicationToast({
                    type: 'error',
                    title: 'Load all segments',
                    description: 'Something went wrong - please try again.',
                });
            },
        });
    };

    pub.filterModeCheck = function (filterMode) {
        if (_parent.filterMode != filterMode) {
            // force refresh
            window.location.reload();
            return true;
        }
        return false;
    };

    // temp function to remove large cookies
    pub.fixCookies = function () {
        var a = 'project_pagination_last_';
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == ' ') {
                c = c.substring(1);
            }
            if (c.indexOf(a) == 0) {
                var name = c.split('=')[0];
                setCookie(name, '', -1);
            }
        }
    };

    pub.updateSort = function (item, loadPage = true) {
        // <a class="cursor-pointer project-sort-item" role="menuitem" tabindex="-1" data-sort-id="2" data-label="last updated">Last updated</a>
        item = $(item);
        if (_parent.sortMode == item.data('sort-mode')) {
            return false;
        }
        _parent.sortMode = item.data('sort-mode');
        _parent.sortActiveLabel.text(item.data('label'));
        _parent.sortDropdown.find('.project-sort-item.project-sort-active').removeClass('project-sort-active');
        item.addClass('project-sort-active');
        if (loadPage) {
            _parent.isAfterSort = true;
            pub.loadPage(0, true);
        }

        Lokalise['analytics']['default'](Lokalise['analytics']['AnalyticsEventName'].PROJECT_SORTED, {
            sort_type: item.data('label'),
        });
    };

    pub.setDocumentId = function (document_id) {
        _parent.documentId = document_id;
        _parent.isAfterFilter = true;
        pub.reset();
        if ((_parent.searchString == '' || !_parent.searchMode) && _parent.filter == null) {
            _parent.searchMode = false;
        } else {
            _parent.searchMode = true;
        }
        if (_parent.documentId != null && filenames[_parent.documentId]) {
            if (filenames[_parent.documentId].indexOf('.docx') !== -1) {
                Prism.languages.lokalise = Prism.languages.lokaliseDocx;
            } else {
                Prism.languages.lokalise = Prism.languages.lokaliseMain;
            }
        }
        pub.setCurrentUrl();
        pub.disableSlider();
        pub.loadKeysBottom();
        $(document).trigger('document_changed');
    };
    pub.getDocumentId = function () {
        return _parent.documentId;
    };

    pub.applyScreenshotsFilter = function () {
        if (!_parent.screenshotsFilter) {
            return;
        }
        _parent.screenshotsFilterParams = _parent.screenshotsFilter.getParams();
        pub.setCurrentUrl();
        pub.loadPage(0, true);
    };
    pub.formatScreenshotFilterParams = function () {
        if (_parent.screenshotsFilterParams == null) {
            return null;
        } else {
            return [
                _parent.screenshotsFilterParams['tagFilterType'],
                _parent.screenshotsFilterParams['selectedScreenshotId'],
                _parent.screenshotsFilterParams['tags'].join(','),
            ].join(';');
        }
    };

    pub.getCurrentUrl = function () {
        var url = '';
        var filter_url = '';
        var search_url = _parent.searchString !== '' ? '&search=' + _parent.searchString : '';
        var screenshot_url =
            pub.formatScreenshotFilterParams() !== null
                ? '&screenshots_filter=' + pub.formatScreenshotFilterParams()
                : '';
        var branch_url = _parent.branch !== null && _parent.branch !== '' ? '&branch=' + _parent.branch : '';
        var key_url = _parent.key !== null && _parent.key !== '' ? '&k=' + _parent.key : '';
        var commentId_url =
            _parent.commentId !== null && _parent.commentId !== '' ? '&commentId=' + _parent.commentId : '';
        var comments_url = _parent.comments !== null && _parent.comments !== '' ? '&comments=' + _parent.comments : '';
        var filename_url = '';
        if (_parent.filter !== null && (_parent.key === null || _parent.key === '')) {
            filter_url = '&filter=' + _parent.filter;
        }
        if (current_view === 'single') {
            url =
                '/project/' +
                p_id +
                '/?view=single&reference_lang_id=' +
                reference_lang_id +
                '&single_lang_id=' +
                single_lang_id;
        } else if (current_view === 'document') {
            url =
                '/project/' +
                p_id +
                '/?view=document&reference_lang_id=' +
                reference_lang_id +
                '&single_lang_id=' +
                single_lang_id;
            screenshot_url = '';
            filename_url = _parent.documentId !== null ? '&document_id=' + _parent.documentId : '';
        } else {
            url = '/project/' + p_id + '/?view=multi';
        }

        if (window.is_document_or_content_project && _parent.filesSelectionHash) {
            url += `&files_selection=${_parent.filesSelectionHash}`;
        }

        url +=
            filter_url +
            key_url +
            search_url +
            screenshot_url +
            filename_url +
            branch_url +
            commentId_url +
            comments_url +
            window.location.hash;
        return url;
    };
    pub.setCurrentUrl = function () {
        url = pub.getCurrentUrl();
        history.replaceState(null, document.title, url);
    };

    pub.refreshKeyStats = function () {
        document.dispatchEvent(new CustomEvent('@key-stats/update'));
    };

    pub.getReloadedKeysTranslationIds = function (keysData) {
        let newTranslationIds = [];
        const newKeyIds = Object.keys(keysData);

        newKeyIds.forEach((keyId) => {
            const keyTranslationIds = Object.keys(keysData[keyId].translations);
            newTranslationIds = [
                ...newTranslationIds,
                ...keyTranslationIds.map((translationId) => keysData[keyId].translations[translationId][0].id),
            ];
        });
        return newTranslationIds;
    };

    pub.hideEditorSidePanel = () => {
        $('#editorSidePanel').hide();
        $('#editorSidePanelBorder').hide();
    };

    pub.showEditorSidePanel = () => {
        $('#editorSidePanel').show();
        $('#editorSidePanelBorder').show();
    };

    pub.init();
    return pub;
};
