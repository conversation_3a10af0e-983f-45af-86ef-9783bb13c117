Twig.extend(function (Twig) {
    Twig.exports.extendTag({
        // unique name for tag type
        type: 'trans',
        // regex match for tag (flag white-space anything)
        regex: /^trans\s+(.+)$/,
        // this is a standalone tag and doesn't require a following tag
        next: [],
        open: true,

        // runs on matched tokens when the template is loaded. (once per template)
        compile: function (token) {
            var expression = token.match[1];

            // Compile the expression. (turns the string into tokens)
            token.stack = Twig.expression.compile.apply(this, [
                {
                    type: Twig.expression.type.expression,
                    value: expression,
                },
            ]).stack;

            delete token.match;
            return token;
        },

        // Runs when the template is rendered
        parse: function (token, context) {
            // parse the tokens into a value with the render context
            var name = Twig.expression.parse.apply(this, [token.stack, context]),
                output = _(name);

            return {
                chain: false,
                output: output,
            };
        },
    });
});
