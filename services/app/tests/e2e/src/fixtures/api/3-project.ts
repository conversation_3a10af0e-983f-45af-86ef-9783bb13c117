import type { Client } from '@src/api';
import { type TestInfo } from '@playwright/test';
import { test as base } from './2-apiClient';
import type { Projects } from '../../api/public/projects';
import { expectProjectBranchingEnabled, expectCustomTranslationStatusesEnabled, type ProjectOptions } from '../helpers';

type ProjectData = Awaited<ReturnType<Projects['create']>>['data'];

type ProjectFixtures = {
  projectOptions: ProjectOptions;
  project: ProjectData;
  adminProjectOptions: ProjectOptions;
  adminProject: ProjectData;
  secondaryProjectOptions: ProjectOptions;
  secondaryProject: ProjectData;
};

const projectFixture = async (
  data: { api: Client; logger: (...logs: any[]) => void; name: string; projectOptions: ProjectOptions },
  useFunction: (r: ProjectData) => Promise<void>,
  testInfo: TestInfo
) => {
  const { api, logger } = data;

  logger(testInfo.workerIndex, `- CREATING ${data.name}`, data.projectOptions);
  const projectResponse = await api.public.projects.create(data.projectOptions);
  const projectData = projectResponse.data;
  logger(testInfo.workerIndex, `- CREATED ${data.name}`, projectData.project_id);

  if (data.projectOptions?.is_branching_enabled || data.projectOptions?.is_cts_enabled) {
    // TODO: Add logging
    await api.internal.settings.general.update(projectData.project_id, {
      ...(data.projectOptions?.is_branching_enabled && { is_branching_enabled: true }),
      ...(data.projectOptions?.is_cts_enabled && { is_cts_enabled: true }),
    });

    await Promise.all([
      ...(data.projectOptions?.is_branching_enabled
        ? [expectProjectBranchingEnabled(api, projectData.project_id)]
        : []),
      ...(data.projectOptions?.is_cts_enabled
        ? [expectCustomTranslationStatusesEnabled(api, projectData.project_id)]
        : []),
    ]);
  }

  logger(testInfo.workerIndex, `- USING ${data.name}`, projectData.project_id);
  await useFunction(projectData);

  try {
    logger(testInfo.workerIndex, `- DELETING ${data.name}`, projectData.project_id);
    await api.public.projects.delete({ project_id: projectData.project_id });
  } catch (error) {
    console.error(`COULD NOT DELETE ${data.name}`, error);
  }
};

export const test = base.extend<ProjectFixtures>({
  projectOptions: {},
  project: async ({ api, projectOptions, logger }, use, workerInfo) => {
    await projectFixture({ api, logger, projectOptions, name: 'API PROJECT' }, use, workerInfo);
  },
  adminProjectOptions: {},
  adminProject: async ({ adminApi, adminProjectOptions, logger }, use, workerInfo) => {
    await projectFixture(
      { api: adminApi, logger, projectOptions: adminProjectOptions, name: 'ADMIN API PROJECT' },
      use,
      workerInfo
    );
  },
  secondaryProjectOptions: {},
  secondaryProject: async ({ secondaryApi: api, secondaryProjectOptions, logger }, use, workerInfo) => {
    await projectFixture(
      { api, logger, projectOptions: secondaryProjectOptions, name: 'SECONDARY API PROJECT' },
      use,
      workerInfo
    );
  },
});
