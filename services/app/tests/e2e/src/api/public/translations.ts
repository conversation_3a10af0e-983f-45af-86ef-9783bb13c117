import { mysuperstep } from '@reporters';
import { faker } from '@faker-js/faker';
import type { Request, RequestHeaders } from '@src/api';
import type { ListParams } from './translations.types';
import { expect } from '@test/api';

export class Translations {
  private readonly request: Request;

  constructor(request: Request) {
    this.request = request;
  }

  async update({
    project_id,
    translation_id,
    data,
    headers,
    errorValidation,
  }: {
    project_id: string;
    translation_id: number;
    data: {
      translation: string;
      is_unverified?: boolean;
      is_reviewed?: boolean;
      custom_translation_status_ids?: number[];
    };
    headers?: RequestHeaders;
    errorValidation?: boolean;
  }) {
    return mysuperstep('[api] update at Translations', async () => {
      let response: {
        data: SingleTranslationModel;
        status: number;
        statusText: string;
        headers: { [key: string]: string };
        headersArray: { name: string; value: string }[];
        config: any;
      };

      const defaultData = { name: faker.string.uuid() };
      await expect
        .poll(
          async () => {
            response = await this.request<SingleTranslationModel>({
              method: 'put',
              url: `/projects/${project_id}/translations/${translation_id}`,
              data: { ...defaultData, ...data },
              headers,
            });
            return response;
          },
          { intervals: [2_000] }
        )
        .toMatchObject({ status: errorValidation ? /40*/ : 200 });

      return response;
    });
  }

  async list(projectId: string, params?: ListParams, headers?: RequestHeaders) {
    return mysuperstep('[api] list at Translations', async () => {
      return this.request<TranslationsListModel>({
        method: 'get',
        url: `/projects/${projectId}/translations/`,
        params,
        headers,
      });
    });
  }

  async retrieve({
    project_id,
    translation_id,
    headers,
  }: {
    project_id: string;
    translation_id: number;
    headers?: RequestHeaders;
  }) {
    return mysuperstep('[api] retrieve at Translations', async () => {
      return this.request<SingleTranslationModel>({
        method: 'get',
        url: `/projects/${project_id}/translations/${translation_id}`,
        headers,
      });
    });
  }
}

export type TranslationModel = {
  translation_id: number;
  key_id: number;
  language_iso: string;
  modified_at: string;
  modified_at_timestamp: number;
  modified_by: number;
  modified_by_email: string;
  translation: string;
  is_unverified: boolean;
  is_reviewed: boolean;
  reviewed_by: number;
  words: number;
  custom_translation_statuses: any[];
  task_id: null | number;
};

export type SingleTranslationModel = {
  project_id: string;
  translation: TranslationModel;
};

export type TranslationsListModel = {
  project_id: string;
  translations: TranslationModel[];
};
