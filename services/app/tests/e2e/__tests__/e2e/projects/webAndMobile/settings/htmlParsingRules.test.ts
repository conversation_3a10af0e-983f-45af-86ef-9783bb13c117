import { expect, metaData, test as base } from '@test/e2e';
import { defaultRules } from './defaultRules';
import { customRules } from './customRules';
import { faker } from '@faker-js/faker';
import { subdomain, email, password } from '@resources/apps/zendeskConfig/key.json';
import { env } from '@src/api';
import { type ZendeskGuide_EntryResponse } from 'src/api/external/zendeskGuide/data';

const test = base.extend<{ zendeskGuideArticle: ZendeskGuide_EntryResponse }>({
  zendeskGuideArticle: async ({ api }, use) => {
    const createEntry = await api.external.zendeskGuide.data.createEntry(
      {
        article: {
          body: '<p>Not unique content<br />Text after BR</p>',
          locale: 'en-us',
          title: `lokalise_automation_${faker.number.int()}`,
          user_segment_id: ************,
          permission_group_id: 811080,
        },
      },
      4972913196306
    );

    await use(createEntry);

    await api.external.zendeskGuide.cleanData(createEntry.article.id).catch(() => {
      /* NOP in case of other test failure */
    });
  },
});

test.describe('HTML Parsing Rules', () => {
  test.describe.configure({ mode: 'default' });

  test.use({
    projectOptions: {
      name: 'Test project',
      languages: [{ lang_iso: 'en_US' }, { lang_iso: 'fr' }],
      base_lang_iso: 'en_US',
      project_type: 'content_integration',
      content_integration: 'zendesk-guide',
    },
  });

  test('should save rules', async ({ launch: { app }, project: { project_id } }) => {
    await metaData({ ids: ['MRP-1100'], teams: ['MAX'] });

    await app.settings.htmlParsingRules.open(project_id);
    await app.settings.htmlParsingRules.validateRules(defaultRules);

    await app.settings.htmlParsingRules.setRules('new rules');
    await app.settings.htmlParsingRules.save();

    await app.settings.htmlParsingRules.validateRules('new rules');
  });

  test('should restore rules to default', async ({ launch: { app }, project: { project_id } }) => {
    await metaData({ ids: ['MRP-1100'], teams: ['MAX'] });

    await app.settings.htmlParsingRules.open(project_id);

    await app.settings.htmlParsingRules.setRules('new rules');
    await app.settings.htmlParsingRules.save();

    await app.settings.htmlParsingRules.restoreRules();

    await app.settings.htmlParsingRules.validateRules(defaultRules);
  });

  // The test is currently broken since Zendesk is showing a captcha.
  test.fixme(
    'should update and apply custom rules',
    async ({ launch: { app }, project: { project_id }, api, zendeskGuideArticle }) => {
      await metaData({ ids: ['MRP-1109'], teams: ['MAX'] });

      await app.settings.htmlParsingRules.open(project_id);
      await app.settings.htmlParsingRules.setRules(customRules);
      await app.settings.htmlParsingRules.save();

      //install zendesk
      await app.apps.zendeskGuide.open(project_id);

      await app.apps.zendeskGuide.modals.integrationConfig.setSubdomain(subdomain);
      await app.apps.zendeskGuide.modals.integrationConfig.confirm();
      await app.apps.zendeskGuide.auth.login(email, password);

      await app.apps.zendeskGuide.shouldHaveContent();

      //import the entry
      await app.apps.zendeskGuide.search.set(zendeskGuideArticle.article.title);
      await app.apps.zendeskGuide.content.shouldHaveEntriesCount(2);
      await app.apps.zendeskGuide.content.selectItems(zendeskGuideArticle.article.title, 'title', 'body');

      await app.apps.zendeskGuide.import();

      await app.apps.zendeskGuide.modals.importOptions.confirm();
      await app.apps.zendeskGuide.shouldHaveInfoMessage('Import process finished successfully.');

      //validate that custom rules were applied
      await expect(async () => {
        const importedEntries = await api.public.keys.list(project_id, { include_translations: 1 });
        const translationValues = importedEntries.data.keys.flatMap((key) =>
          key.translations.filter((translation) => translation.language_iso === 'en_US')
        );

        expect(importedEntries.data.keys).toHaveLength(2);
        expect(translationValues).toEqual(
          expect.arrayContaining([expect.objectContaining({ translation: 'Not unique content<br>Text after BR' })])
        );
      }).toPass({ timeout: env.timeout });

      //translate the entry
      await app.project.multilingual.open(project_id);

      await app.project.multilingual.keys.first.translation('French').plain.ensure('French title');

      await app.project.multilingual.keys.second
        .translation('French')
        .plain.ensure('Contenu non unique<br />Texte après BR');

      //export the entry
      await app.apps.zendeskGuide.open(project_id);
      await app.apps.zendeskGuide.search.set(zendeskGuideArticle.article.title);
      await app.apps.zendeskGuide.content.shouldHaveEntriesCount(2);
      await app.apps.zendeskGuide.content.selectItems(zendeskGuideArticle.article.title, 'title', 'body');

      await app.apps.zendeskGuide.export();
      await app.apps.zendeskGuide.modals.exportOptions.confirm();
      await app.apps.zendeskGuide.shouldHaveInfoMessage('Export process finished successfully.');

      //validate the exported file
      await expect
        .poll(async () => {
          const modifiedEntry = await app.api.external.zendeskGuide.data.retrieveEntryTranslations(
            zendeskGuideArticle.article.id
          );
          return modifiedEntry.translations;
        })
        .toEqual(
          expect.arrayContaining([expect.objectContaining({ body: '<p>Contenu non unique<br>Texte après BR</p>' })])
        );
    }
  );
});
