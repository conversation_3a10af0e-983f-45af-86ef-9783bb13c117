<?php

declare(strict_types=1);

namespace Lokalise\Tests\Application\Controller;

use Lokalise\Tests\AbstractWebTestCase;
use Lokalise\Tests\FixtureManager\FixtureManager;
use Symfony\Component\HttpFoundation\Response;

class LiveJSControllerTest extends AbstractWebTestCase
{
    protected FixtureManager $fixtureManager;

    protected function setUp(): void
    {
        parent::setUp();

        $this->fixtureManager = $this->getServiceById(FixtureManager::class);
        $this->fixtureManager->setUp();
    }

    public function testLiveJSDisplayTemplate()
    {
        $client = $this->client;
        $client->request('GET', '/live-js/html');
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
    }

    public function testLiveJSAuth()
    {
        $client = $this->client;
        $client->request('POST', '/live-js/111aaa222.111/auth');
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
    }

    public function testLiveJSLogin()
    {
        $client = $this->client;
        $client->request('POST', '/live-js/111aaa222.111/login');
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
    }

    public function testLiveJSProjectSet()
    {
        $client = $this->client;
        $client->request('POST', '/live-js/111aaa222.111/set');
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
    }

    public function testLiveJSProjectSync()
    {
        $client = $this->client;
        $client->request('POST', '/live-js/111aaa222.111/sync');
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
    }

    protected function tearDown(): void
    {
        $this->fixtureManager->tearDown();
        parent::tearDown();
    }
}
