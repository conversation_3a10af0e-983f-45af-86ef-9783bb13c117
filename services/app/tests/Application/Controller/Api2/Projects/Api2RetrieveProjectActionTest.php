<?php

declare(strict_types=1);

namespace Lokalise\Tests\Application\Controller\Api2\Projects;

use Faker\Factory as FakerFactory;
use Faker\Generator;
use Lokalise\Tests\AbstractWebTestCase;
use Lokalise\Tests\FixtureManager\FixtureManager;
use Symfony\Component\HttpFoundation\Request;

class Api2RetrieveProjectActionTest extends AbstractWebTestCase
{
    private int $ownerUserId;
    private int $ownerTeamId;
    private Generator $faker;
    private FixtureManager $fixtureManager;

    protected function setUp(): void
    {
        parent::setUp();

        $this->faker = FakerFactory::create();
        $this->fixtureManager = $this->getServiceById(FixtureManager::class);
        $this->fixtureManager->setUp();

        [
            'teamId' => $this->ownerTeamId,
            'userId' => $this->ownerUserId,
        ] = $this->fixtureManager->createUserAndTeam(isAdmin: true, isOwner: true);

        $this->fixtureManager->createUserApiToken($this->ownerUserId);
    }

    protected function tearDown(): void
    {
        $this->fixtureManager->tearDown();
        parent::tearDown();
    }

    /** @test */
    public function shouldRetrieveProjectUsingUuid(): void
    {
        $projectId = $this->fixtureManager->generateProjectId();
        $projectUuid = $this->fixtureManager->generateProjectUuid();

        $this->fixtureManager->initProject(
            ownerId: $this->ownerUserId,
            teamId: $this->ownerTeamId,
            projectId: $projectId,
            projectName: $this->faker->colorName,
            languages: [
                640 => 'en',
                666 => 'de',
            ],
            uuid: $projectUuid,
        );

        $this->client->request(
            Request::METHOD_GET,
            sprintf('/api2/projects/%s', $projectUuid),
            [],
            [],
            [
                'HTTP_X-Api-Token' => FixtureManager::API_TOKEN,
            ],
        );

        static::assertResponseIsSuccessful();

        $response = json_decode($this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        static::assertEquals($projectId, $response['project_id']);
        static::assertEquals($projectUuid, $response['uuid']);
    }
}
