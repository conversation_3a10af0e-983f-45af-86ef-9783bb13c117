<?php

declare(strict_types=1);

namespace Lokalise\Tests\Application\Controller;

use ElasticSearchService;
use Generator;
use InvalidArgumentException;
use Lokalise\Adapter\DatabaseAdapterInterface;
use Lokalise\Ai\Provider\AiFeatureStateProvider;
use Lokalise\Common\Permission\Constant\PermissionsFeatureFlag;
use Lokalise\Constants\ProjectTaskStatus;
use Lokalise\Constants\ProjectTaskType;
use Lokalise\Services\ContributorService;
use Lokalise\Services\ProjectTaskService;
use Lokalise\Task\FeatureFlag\TasksRemoveUnusedEndpointsFeatureFlag;
use Lokalise\Tests\AbstractWebTestCase;
use Lokalise\Tests\FixtureManager\FixtureManager;
use Lokalise\Tests\Integration\FeatureFlag\Provider\TestFeatureStateProvider;
use Lokalise\Tests\TestFramework\FeatureFlagTestHelperTrait;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function sprintf;

class LegacyTasksControllerTest extends AbstractWebTestCase
{
    use FeatureFlagTestHelperTrait;

    private FixtureManager $fixtureManager;
    private DatabaseAdapterInterface $db;
    private ContributorService $contributorService;
    private ProjectTaskService $projectTaskService;

    private string $projectId;
    private int $userId;
    private int $teamOneId;
    private int $contributorId;
    private string $contributorEmail;

    protected function setUp(): void
    {
        parent::setUp();
        $this->testFeatureStateProvider = $this->getServiceById(TestFeatureStateProvider::class);
        $this->fixtureManager = $this->getServiceById(FixtureManager::class);
        $this->fixtureManager->setUp();
        $this->db = $this->getServiceById(DatabaseAdapterInterface::class);
        $this->contributorService = static::getContainer()->get(ContributorService::class);
        $this->projectTaskService = $this->getServiceById(ProjectTaskService::class);

        $this->projectId = $this->fixtureManager->generateProjectId();

        ['userId' => $this->userId, 'teamId' => $this->teamOneId] = $this->fixtureManager->createUserAndTeam(true, true);

        $this->fixtureManager->initProject(
            $this->userId,
            $this->teamOneId,
            $this->projectId,
            'Project one',
            [640 => 'en', 597 => 'ru']
        );

        $this->contributorEmail = $this->fixtureManager->generateEmail();
        $this->contributorId = $this->fixtureManager->createUser(['email' => $this->contributorEmail, 'fullname' => 'Agent Smith', 'admin' => 1]);
        $teamId = $this->fixtureManager->createTeam(['name' => 'Team Two', 'owner_id' => $this->contributorId]);
        $this->fixtureManager->attachUserToTeam(['team_id' => $teamId, 'user_id' => $this->contributorId]);
    }

    protected function tearDown(): void
    {
        $this->testFeatureStateProvider->reset();
        $this->fixtureManager->tearDown();
        parent::tearDown();
    }

    private function initKeys(): void
    {
        $elasticSearchService = static::getContainer()->get(ElasticSearchService::class);

        $key = 'Test Key';
        $baseTranslationText = 'Test translation';
        $targetTranslationText = 'Test translation ru';
        $keyId = $this->fixtureManager->createKey(
            [
                'project_id' => $this->projectId,
                'akey' => $key,
                'akey_web' => $key,
                'akey_android' => $key,
                'akey_other' => $key,
                'plural' => 0,
            ]
        );
        foreach ([640, 597] as $langId) {
            $translation = [
                'akey_id' => $keyId,
                'lang_id' => $langId,
                'translation' => $langId === 640 ? $baseTranslationText : $targetTranslationText,
                'fuzzy' => 0,
                'is_proofread' => 0,
                'segment_number' => 1,
            ];
            $this->fixtureManager->createTranslation($translation);
        }

        $elasticSearchService->reindexOne(
            projectIds: [$this->projectId],
            reindexKeyProjects: true,
            reindexProjectKeys: true,
            waitForReindex: true,
        );
    }

    public function testThirdPartyCannotAccessFilterData(): void
    {
        ['email' => $email] = $this->fixtureManager->createUserAndTeam(false, true);
        $this->fixtureManager->initProject(
            $this->userId,
            $this->teamOneId,
            $this->fixtureManager->generateProjectId(),
            'Test project',
            [640 => 'en']
        );

        // Owner of Team Two tries to access project from Team One
        $this->login($this->client, $email);
        $this->client->request('GET', '/tasks/' . $this->projectId . '/filter-data');

        static::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    /**
     * @dataProvider getRequestAndResponse
     */
    public function testThatContributorsAreCorrectlyFilteredByLanguage(array $requestLangIds, array $expectedResponse): void
    {
        static::markTestSkipped('Validating if the test is actually needed (UI, Bulk actions and API do not use new_format');

        // Given
        $projectId = $this->fixtureManager->generateProjectId();

        $ownerEmail = $this->fixtureManager->generateEmail();

        $ownerId = $this->fixtureManager->createUser(['email' => $ownerEmail, 'fullname' => 'Owner of team']); // owner gets all languages attached in initProject()
        $teamId = $this->fixtureManager->createTeam(['name' => 'Team', 'owner_id' => $ownerId]);
        $this->fixtureManager->attachUserToTeam(['team_id' => $teamId, 'user_id' => $ownerId, 'is_admin' => 0, 'is_owner' => 1]);

        $userTwoEmail = $this->fixtureManager->generateEmail();
        $userTwoId = $this->createProjectUserWhoContributesToProjectLanguages($projectId, $userTwoEmail, 'User with CZ only', [640], [765]);

        $userGroupOneId = $this->createProjectUserGroupAndGroupLanguages($projectId, $teamId, 'UserGroup with IT only', [640], [734]);

        $userThreeEmail = $this->fixtureManager->generateEmail();
        $userThreeId = $this->createProjectUserWhoContributesToProjectLanguages($projectId, $userThreeEmail, 'User in userGroup 1', [640], [734], $userGroupOneId);

        $userGroupTwoId = $this->createProjectUserGroupAndGroupLanguages($projectId, $teamId, 'UserGroup with IT and FR', [640], [734, 673]);

        $userFourEmail = $this->fixtureManager->generateEmail();
        $userFourId = $this->createProjectUserWhoContributesToProjectLanguages($projectId, $userFourEmail, 'User in userGroup 2', [640], [734, 673], $userGroupTwoId);

        $userGroupThreeId = $this->createProjectUserGroupAndGroupLanguages($projectId, $teamId, 'UserGroup with admin rights', [], [], true);

        $userFiveEmail = $this->fixtureManager->generateEmail();
        $userFiveId = $this->createProjectUserWhoContributesToProjectLanguages($projectId, $userFiveEmail, 'User in admin userGroup', [640], [640, 765, 734, 673], $userGroupThreeId);   // needs to have all languages to mimic how \Lokalise\Services\AccessService::processAccess() behaves when the group has admin rights

        $this->fixtureManager->initProject(
            $ownerId,
            $teamId,
            $projectId,
            'Test project',
            [640 => 'en', 765 => 'cs', 734 => 'it', 673 => 'fr']
        );

        $this->login($this->client, $ownerEmail);

        // When
        $this->client->request('GET', '/tasks/contributors-for-languages', [
            'projectId' => $projectId,
            'refLangId' => 640,
            'langIds' => $requestLangIds,
            'includeAdmins' => true,
            'onlyReviewers' => false,
            'new_format' => 1,  // will be removed in LMP-109
        ]);

        $getGravatarUrl = fn (string $email) => sprintf('https://www.gravatar.com/avatar/%s?d=retro&s=100', $this->fixtureManager->getGravatarHash($email));

        // Then
        $map = [
            '%ownerId%' => $ownerId,
            '%ownerEmail%' => $ownerEmail,
            '%ownerLogoUrl%' => $getGravatarUrl($ownerEmail),
            '%userTwoId%' => $userTwoId,
            '%userTwoEmail%' => $userTwoEmail,
            '%userTwoLogoUrl%' => $getGravatarUrl($userTwoEmail),
            '%userThreeId%' => $userThreeId,
            '%userThreeEmail%' => $userThreeEmail,
            '%userThreeLogoUrl%' => $getGravatarUrl($userThreeEmail),
            '%userFourId%' => $userFourId,
            '%userFourEmail%' => $userFourEmail,
            '%userFourLogoUrl%' => $getGravatarUrl($userFourEmail),
            '%userFiveId%' => $userFiveId,
            '%userFiveEmail%' => $userFiveEmail,
            '%userFiveLogoUrl%' => $getGravatarUrl($userFiveEmail),
            '%userGroupOneId%' => $userGroupOneId,
            '%userGroupTwoId%' => $userGroupTwoId,
            '%userGroupThreeId%' => $userGroupThreeId,
            '%userGroupOneLogoUrl%' => $this->db->query('SELECT logo_url FROM `user_group` WHERE id = %i', $userGroupOneId)[0]['logo_url'],
            '%userGroupTwoLogoUrl%' => $this->db->query('SELECT logo_url FROM `user_group` WHERE id = %i', $userGroupTwoId)[0]['logo_url'],
            '%userGroupThreeLogoUrl%' => $this->db->query('SELECT logo_url FROM `user_group` WHERE id = %i', $userGroupThreeId)[0]['logo_url'],
        ];

        array_walk_recursive($expectedResponse, function (&$item) use ($map) {
            if (array_key_exists($item, $map)) {
                $item = $map[$item];
            }
        });

        static::assertSame($expectedResponse, json_decode($this->client->getResponse()->getContent(), true, flags: JSON_THROW_ON_ERROR));
    }

    public function getRequestAndResponse(): Generator
    {
        yield 'All contributors to CZ language' => [[765], [
            [
                'type' => 'group',
                'id' => '%userGroupThreeId%',
                'name' => 'UserGroup with admin rights',
                'logoUrl' => '%userGroupThreeLogoUrl%',
                'users' => [
                    [
                        'type' => 'user',
                        'id' => '%userFiveId%',
                        'name' => 'User in admin userGroup',
                        'email' => '%userFiveEmail%',
                        'logoUrl' => '%userFiveLogoUrl%',
                    ],
                ],
            ],
            [
                'type' => 'user',
                'id' => '%ownerId%',
                'name' => 'Owner of team',
                'email' => '%ownerEmail%',
                'logoUrl' => '%ownerLogoUrl%',
            ],
            [
                'type' => 'user',
                'id' => '%userTwoId%',
                'name' => 'User with CZ only',
                'email' => '%userTwoEmail%',
                'logoUrl' => '%userTwoLogoUrl%',
            ],
            [
                'type' => 'user',
                'id' => '%userFiveId%',
                'name' => 'User in admin userGroup',
                'email' => '%userFiveEmail%',
                'logoUrl' => '%userFiveLogoUrl%',
            ],
        ]];

        yield 'All contributors to IT language' => [[734], [
            [
                'type' => 'group',
                'id' => '%userGroupTwoId%',
                'name' => 'UserGroup with IT and FR',
                'logoUrl' => '%userGroupTwoLogoUrl%',
                'users' => [
                    [
                        'type' => 'user',
                        'id' => '%userFourId%',
                        'name' => 'User in userGroup 2',
                        'email' => '%userFourEmail%',
                        'logoUrl' => '%userFourLogoUrl%',
                    ],
                ],
            ],
            [
                'type' => 'group',
                'id' => '%userGroupOneId%',
                'name' => 'UserGroup with IT only',
                'logoUrl' => '%userGroupOneLogoUrl%',
                'users' => [
                    [
                        'type' => 'user',
                        'id' => '%userThreeId%',
                        'name' => 'User in userGroup 1',
                        'email' => '%userThreeEmail%',
                        'logoUrl' => '%userThreeLogoUrl%',
                    ],
                ],
            ],
            [
                'type' => 'group',
                'id' => '%userGroupThreeId%',
                'name' => 'UserGroup with admin rights',
                'logoUrl' => '%userGroupThreeLogoUrl%',
                'users' => [
                    [
                        'type' => 'user',
                        'id' => '%userFiveId%',
                        'name' => 'User in admin userGroup',
                        'email' => '%userFiveEmail%',
                        'logoUrl' => '%userFiveLogoUrl%',
                    ],
                ],
            ],
            [
                'type' => 'user',
                'id' => '%ownerId%',
                'name' => 'Owner of team',
                'email' => '%ownerEmail%',
                'logoUrl' => '%ownerLogoUrl%',
            ],
            [
                'type' => 'user',
                'id' => '%userThreeId%',
                'name' => 'User in userGroup 1',
                'email' => '%userThreeEmail%',
                'logoUrl' => '%userThreeLogoUrl%',
            ],
            [
                'type' => 'user',
                'id' => '%userFourId%',
                'name' => 'User in userGroup 2',
                'email' => '%userFourEmail%',
                'logoUrl' => '%userFourLogoUrl%',
            ],
            [
                'type' => 'user',
                'id' => '%userFiveId%',
                'name' => 'User in admin userGroup',
                'email' => '%userFiveEmail%',
                'logoUrl' => '%userFiveLogoUrl%',
            ],
        ]];

        yield 'Only contributors to both IT and FR language' => [[734, 673], [
            [
                'type' => 'group',
                'id' => '%userGroupTwoId%',
                'name' => 'UserGroup with IT and FR',
                'logoUrl' => '%userGroupTwoLogoUrl%',
                'users' => [
                    [
                        'type' => 'user',
                        'id' => '%userFourId%',
                        'name' => 'User in userGroup 2',
                        'email' => '%userFourEmail%',
                        'logoUrl' => '%userFourLogoUrl%',
                    ],
                ],
            ],
            [
                'type' => 'group',
                'id' => '%userGroupThreeId%',
                'name' => 'UserGroup with admin rights',
                'logoUrl' => '%userGroupThreeLogoUrl%',
                'users' => [
                    [
                        'type' => 'user',
                        'id' => '%userFiveId%',
                        'name' => 'User in admin userGroup',
                        'email' => '%userFiveEmail%',
                        'logoUrl' => '%userFiveLogoUrl%',
                    ],
                ],
            ],
            [
                'type' => 'user',
                'id' => '%ownerId%',
                'name' => 'Owner of team',
                'email' => '%ownerEmail%',
                'logoUrl' => '%ownerLogoUrl%',
            ],
            [
                'type' => 'user',
                'id' => '%userFourId%',
                'name' => 'User in userGroup 2',
                'email' => '%userFourEmail%',
                'logoUrl' => '%userFourLogoUrl%',
            ],
            [
                'type' => 'user',
                'id' => '%userFiveId%',
                'name' => 'User in admin userGroup',
                'email' => '%userFiveEmail%',
                'logoUrl' => '%userFiveLogoUrl%',
            ],
        ]];

        yield 'Only contributors to all languages' => [[765, 734, 673], [
            [
                'type' => 'group',
                'id' => '%userGroupThreeId%',
                'name' => 'UserGroup with admin rights',
                'logoUrl' => '%userGroupThreeLogoUrl%',
                'users' => [
                    [
                        'type' => 'user',
                        'id' => '%userFiveId%',
                        'name' => 'User in admin userGroup',
                        'email' => '%userFiveEmail%',
                        'logoUrl' => '%userFiveLogoUrl%',
                    ],
                ],
            ],
            [
                'type' => 'user',
                'id' => '%ownerId%',
                'name' => 'Owner of team',
                'email' => '%ownerEmail%',
                'logoUrl' => '%ownerLogoUrl%',
            ],
            [
                'type' => 'user',
                'id' => '%userFiveId%',
                'name' => 'User in admin userGroup',
                'email' => '%userFiveEmail%',
                'logoUrl' => '%userFiveLogoUrl%',
            ],
        ]];
    }

    private function createProjectUserWhoContributesToProjectLanguages(string $projectId, string $email, string $fullName, array $referenceLanguages, array $contributableLanguages, ?int $userGroupId = null): int
    {
        $userId = $this->fixtureManager->createUser(['email' => $email, 'fullname' => $fullName]);

        $this->fixtureManager->attachContributorToProject(['project_id' => $projectId, 'user_id' => $userId]);   // does not have impact on test functionality, is used for data correctness
        $this->fixtureManager->attachUserToProject(['project_id' => $projectId, 'user_id' => $userId]);
        $this->fixtureManager->attachUserToTeam(['user_id' => $userId, 'team_id' => $this->teamOneId]);

        if ($userGroupId !== null) {
            $this->fixtureManager->attachUserToUserGroup($userGroupId, $userId);
        }

        foreach ($referenceLanguages as $referenceLanguage) {
            $this->fixtureManager->attachUserToProjectLanguage(['project_id' => $projectId, 'lang_id' => $referenceLanguage, 'user_id' => $userId, 'is_editable' => 0]);
        }
        foreach ($contributableLanguages as $contributableLanguage) {
            $this->fixtureManager->attachUserToProjectLanguage(['project_id' => $projectId, 'lang_id' => $contributableLanguage, 'user_id' => $userId, 'is_editable' => 1]);
        }

        return $userId;
    }

    private function createProjectUserGroupAndGroupLanguages(string $projectId, int $teamId, string $name, array $referenceLanguages, array $contributableLanguages, bool $isAdmin = false): int
    {
        $userGroupId = $this->fixtureManager->createUserGroup($teamId, $name, isAdmin: $isAdmin);

        if ($isAdmin && (!empty($referenceLanguages) || !empty($contributableLanguages))) {
            throw new InvalidArgumentException('This scenario does not make sense, admin user groups need to have reference languages and contributable languages empty to properly mimic business logic');
        }

        $this->fixtureManager->attachUserGroupToProject(['user_group_id' => $userGroupId, 'project_id' => $projectId]);

        foreach ($referenceLanguages as $referenceLanguage) {
            $this->fixtureManager->createUserGroupLang(['user_group_id' => $userGroupId, 'lang_id' => $referenceLanguage, 'is_editable' => 0]);
        }
        foreach ($contributableLanguages as $contributableLanguage) {
            $this->fixtureManager->createUserGroupLang(['user_group_id' => $userGroupId, 'lang_id' => $contributableLanguage, 'is_editable' => 1]);
        }

        return $userGroupId;
    }

    // TODO: Clean up in HOME-1009
    public function testShouldLoadTasksPageByAdminWithoutFfWithProperPermission(): void
    {
        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => false]);

        $this->contributorService->add($this->projectId, $this->contributorId, $this->userId, false, true, [], [], ['tasks' => 1]);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_GET,
            uri: '/tasks/' . $this->projectId . '/',
        );
        static::assertResponseIsSuccessful();
    }

    public function testShouldLoadTasksPageByContributorWithFfWithProperPermission(): void
    {
        // TODO: Clean up in HOME-1009
        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => true]);

        $this->contributorService->add($this->projectId, $this->contributorId, $this->userId, false, false);

        $this->db->queryFirstRow('UPDATE project_contributor SET permissions = \'{"tasks": 1}\' WHERE project_id = %s AND user_id = %i', $this->projectId, $this->contributorId);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_GET,
            uri: '/tasks/' . $this->projectId . '/',
        );
        static::assertResponseIsSuccessful();
    }

    public function testShouldNotLoadTasksPageByContributorWithFfWithoutNewPermission(): void
    {
        // TODO: Clean up in HOME-1009
        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => true]);

        $this->contributorService->add($this->projectId, $this->contributorId, $this->userId, false, false, [], [], ['tasks' => 0]);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_GET,
            uri: '/tasks/' . $this->projectId . '/',
        );
        static::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function testShouldLoadTasksListByAdminWithoutFfWithProperPermission(): void
    {
        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => true]);

        $this->contributorService->add($this->projectId, $this->contributorId, $this->userId, false, true, [], [], ['tasks' => 1]);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_GET,
            uri: sprintf('/tasks/%s/load/%s', $this->projectId, ProjectTaskStatus::Created),
            parameters: ['first_id' => 1],
        );
        static::assertResponseIsSuccessful();
    }

    // TODO: Clean up in HOME-1009
    public function testShouldNotLoadTasksListByNonAdminWithoutFfWithoutProperPermission(): void
    {
        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => false]);

        $this->contributorService->add($this->projectId, $this->contributorId, $this->userId, false, false, [], [], ['tasks' => 0]);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_GET,
            uri: sprintf('/tasks/%s/load/%s', $this->projectId, ProjectTaskStatus::Created),
            parameters: ['first_id' => 1],
        );
        static::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function testShouldLoadTasksListByContributorWithFfWithProperPermission(): void
    {
        // TODO: Clean up in HOME-1009
        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => true]);

        $this->contributorService->add($this->projectId, $this->contributorId, $this->userId, false, false);

        $this->db->queryFirstRow('UPDATE project_contributor SET permissions = \'{"tasks": 1}\' WHERE project_id = %s AND user_id = %i', $this->projectId, $this->contributorId);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_GET,
            uri: sprintf('/tasks/%s/load/%s', $this->projectId, ProjectTaskStatus::Created),
            parameters: ['first_id' => 1],
        );
        static::assertResponseIsSuccessful();
    }

    public function testShouldNotLoadTasksListByContributorWithFfWithoutProperPermission(): void
    {
        // TODO: Clean up in HOME-1009
        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => true]);

        $this->contributorService->add($this->projectId, $this->contributorId, $this->userId, false, false, [], [], ['tasks' => 0]);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_GET,
            uri: sprintf('/tasks/%s/load/%s', $this->projectId, ProjectTaskStatus::Created),
            parameters: ['first_id' => 1],
        );
        static::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    // TODO: Clean up in HOME-1009
    public function testShouldNotCloseLangByNonAdminWithoutFfWithoutProperPermission(): void
    {
        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => false]);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_POST,
            uri: '/tasks/close-lang',
            parameters: [
                'task_id' => 1,
                'lang_id' => 640,
            ],
        );
        static::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function testShouldCloseLangByContributorWithFfWithProperPermission(): void
    {
        // TODO: Clean up in HOME-1009
        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => true]);

        $taskId = $this->fixtureManager->createTranslationTask($this->projectId, $this->userId, 'Test task', false, false, false, 640, [597], [$this->contributorId => [640, 597]], []);

        $this->contributorService->add($this->projectId, $this->contributorId, $this->userId, false, false, [640], [597], ['tasks' => 1]);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_POST,
            uri: '/tasks/close-lang',
            parameters: [
                'task_id' => $taskId,
                'lang_id' => 597,
            ],
        );

        static::assertResponseIsSuccessful();
    }

    public function testShouldNotCloseLangByContributorWithFfWithoutProperPermission(): void
    {
        // TODO: Clean up in HOME-1009
        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => true]);

        $this->contributorService->add($this->projectId, $this->contributorId, $this->userId, false, false);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_POST,
            uri: '/tasks/close-lang',
            parameters: [
                'task_id' => 1,
                'lang_id' => 640,
            ],
        );
        static::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function testShouldCreateTaskByAdminWithProperPermission(): void
    {
        $this->initKeys();

        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => true]);

        $this->contributorService->add($this->projectId, $this->contributorId, $this->userId, false, true, [], [], ['tasks' => 1]);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_POST,
            uri: '/tasks/create',
            parameters: [
                'project_id' => $this->projectId,
                'title' => 'Test task',
                'description' => 'Test task description',
                'due_date' => '2022-12-31',
                'type' => 'translation',
                'auto_close_items' => 0,
                'autoclose_languages' => 0,
                'autoclose_task' => 1,
                'template_title' => '',
                'is_mass' => 0,
                'source_lang_id' => 640,
                'langs' => [0 => ['id' => 597, 'contributors' => [['id' => $this->contributorId, 'type' => 'user']]]],
            ],
        );

        static::assertResponseIsSuccessful();
    }

    /**
     * @dataProvider getAiTranslationTaskSettings
     */
    public function testShouldCreateAITranslationTaskWithCorrectSettings(
        array $flags,
        bool $saveAiTranslationToTm,
        bool $useTm100PercentMatch,
        bool $useTmFuzzyMatch,
        bool $useDefaultLanguageAsSourceLanguage,
        bool $expectedSaveAiTranslationToTmResult,
        bool $expectedUseTm100PercentMatchResult,
        bool $expectedUseTmFuzzyMatchResult,
        bool $expectedUseIncludeScoreResult,
    ): void {
        $this->initKeys();

        $this->overrideFeatureFlagStates($flags);

        $this->contributorService->add(
            projectId:           $this->projectId,
            userId:              $this->contributorId,
            addedBy:             $this->userId,
            isReviewer:          false,
            isAdmin:             true,
            adminRights:         ['tasks' => 1]
        );

        $this->login($this->client, $this->contributorEmail);

        $sourceLangId = 640;
        $targetLangId = 597;
        if ($useDefaultLanguageAsSourceLanguage === false) {
            $sourceLangId = 597;
            $targetLangId = 640;
        }

        $this->client->request(
            method: Request::METHOD_POST,
            uri: '/tasks/create',
            parameters: [
                'project_id' => $this->projectId,
                'title' => 'Test task',
                'description' => 'Test task description',
                'due_date' => '2022-12-31',
                'type' => ProjectTaskType::AutomaticTranslationByAi,
                'auto_close_items' => 0,
                'autoclose_languages' => 0,
                'autoclose_task' => 1,
                'template_title' => '',
                'is_mass' => 0,
                'source_lang_id' => $sourceLangId,
                'langs' => [0 => ['id' => $targetLangId, 'contributors' => [['id' => $this->contributorId, 'type' => 'user']]]],
                'save_ai_translation_to_tm' => (int) $saveAiTranslationToTm,
                'use_tm_100_percent_match' => (int) $useTm100PercentMatch,
                'use_tm_fuzzy_match' => (int) $useTmFuzzyMatch,
            ],
        );

        static::assertResponseIsSuccessful();

        $response = json_decode($this->client->getResponse()->getContent(), true, flags: JSON_THROW_ON_ERROR);
        $taskId = (int) $response['task_id'];

        static::assertEquals(
            $expectedSaveAiTranslationToTmResult,
            $this->projectTaskService->isAiTranslationToTmTaskOptionEnabled($taskId)
        );
        static::assertEquals(
            $expectedUseTm100PercentMatchResult,
            $this->projectTaskService->isUseTm100PercentMatchTaskOptionEnabled($taskId)
        );
        static::assertEquals(
            $expectedUseTmFuzzyMatchResult,
            $this->projectTaskService->isUseTmFuzzyMatchTaskOptionEnabled($taskId)
        );
        static::assertEquals(
            $expectedUseIncludeScoreResult,
            $this->projectTaskService->isUseIncludeScoreTaskOptionEnabled($taskId)
        );
    }

    public function getAiTranslationTaskSettings(): iterable
    {
        yield 'AI translation task with no options enabled' => [
            'flags' => [],
            'saveAiTranslationToTm' => false,
            'useTm100PercentMatch' => false,
            'useTmFuzzyMatch' => false,
            'useDefaultLanguageAsSourceLanguage' => false,
            'expectedSaveAiTranslationToTmResult' => false,
            'expectedUseTm100PercentMatchResult' => false,
            'expectedUseTmFuzzyMatchResult' => false,
            'expectedUseIncludeScoreResult' => false,
        ];

        yield 'AI translation task with all options enabled' => [
            'flags' => [
                AiFeatureStateProvider::HOME_FEATURE_AT_USE_100_TM_MATCH => true,
                AiFeatureStateProvider::HOME_FEATURE_AT_USE_FUZZY_MATCH => true,
                AiFeatureStateProvider::TRANSLATIONS_FEATURE_AI_SCORING => true,
            ],
            'saveAiTranslationToTm' => true,
            'useTm100PercentMatch' => true,
            'useTmFuzzyMatch' => true,
            'useDefaultLanguageAsSourceLanguage' => true,
            'expectedSaveAiTranslationToTmResult' => true,
            'expectedUseTm100PercentMatchResult' => true,
            'expectedUseTmFuzzyMatchResult' => true,
            'expectedUseIncludeScoreResult' => true,
        ];

        yield 'AI translation task with all options enabled but request has all options off' => [
            'flags' => [
                AiFeatureStateProvider::HOME_FEATURE_AT_USE_100_TM_MATCH => true,
                AiFeatureStateProvider::HOME_FEATURE_AT_USE_FUZZY_MATCH => true,
                AiFeatureStateProvider::TRANSLATIONS_FEATURE_AI_SCORING => true,
            ],
            'saveAiTranslationToTm' => false,
            'useTm100PercentMatch' => false,
            'useTmFuzzyMatch' => false,
            'useDefaultLanguageAsSourceLanguage' => false,
            'expectedSaveAiTranslationToTmResult' => false,
            'expectedUseTm100PercentMatchResult' => false,
            'expectedUseTmFuzzyMatchResult' => false,
            'expectedUseIncludeScoreResult' => false,
        ];
    }

    public function testShouldNotCreateTaskByNonAdminWithoutProperPermission(): void
    {
        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => true]);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_POST,
            uri: '/tasks/create',
            parameters: [
                'project_id' => $this->projectId,
            ]
        );
        static::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function testShouldCreateTaskByContributorWithFfWithProperPermission(): void
    {
        $this->initKeys();

        // TODO: Clean up in HOME-1009
        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => true]);

        $this->contributorService->add($this->projectId, $this->contributorId, $this->userId, false, true, [640], [597], ['tasks' => 1]);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_POST,
            uri: '/tasks/create',
            parameters: [
                'project_id' => $this->projectId,
                'title' => 'Test task',
                'description' => 'Test task description',
                'due_date' => '2022-12-31',
                'type' => 'translation',
                'auto_close_items' => 0,
                'autoclose_languages' => 0,
                'autoclose_task' => 1,
                'template_title' => '',
                'is_mass' => 0,
                'source_lang_id' => 640,
                'langs' => [0 => ['id' => 597, 'contributors' => [['id' => $this->contributorId, 'type' => 'user']]]],
            ],
        );

        static::assertResponseIsSuccessful();
    }

    public function testShouldNotCreateTaskByContributorWithFfWithoutProperPermission(): void
    {
        // TODO: Clean up in HOME-1009
        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => true]);

        $this->contributorService->add($this->projectId, $this->contributorId, $this->userId, false, false, [], [], ['tasks' => 0]);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_POST,
            uri: '/tasks/create',
            parameters: [
                'project_id' => $this->projectId,
                'title' => 'Test task',
                'description' => 'Test task description',
                'due_date' => '2022-12-31',
                'type' => 'translation',
                'auto_close_items' => 0,
                'autoclose_languages' => 0,
                'autoclose_task' => 1,
                'template_title' => '',
                'is_mass' => 0,
                'source_lang_id' => 640,
                'langs' => [0 => ['id' => 597, 'contributors' => [['id' => $this->contributorId, 'type' => 'user']]]],
            ],
        );
        static::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function testShouldGetReportByAdminWithoutFfWithProperPermission(): void
    {
        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => true]);

        $taskId = $this->fixtureManager->createTranslationTask($this->projectId, $this->userId, 'Test task', false, false, false, 640, [597], [$this->contributorId => [640, 597]], []);

        $this->contributorService->add($this->projectId, $this->contributorId, $this->userId, false, true, [], [], ['tasks' => 1]);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_GET,
            uri: sprintf('/tasks/%d/report', $taskId),
        );

        static::assertResponseIsSuccessful();
    }

    // TODO: Clean up in HOME-1009
    public function testShouldNotGetReportByNonAdminWithoutFfWithoutProperPermission(): void
    {
        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => false]);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_GET,
            uri: sprintf('/tasks/%d/report', 1),
        );
        static::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function testShouldGetReportByContributorWithFfWithProperPermission(): void
    {
        // TODO: Clean up in HOME-1009
        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => true]);

        $taskId = $this->fixtureManager->createTranslationTask($this->projectId, $this->userId, 'Test task', false, false, false, 640, [597], [$this->contributorId => [640, 597]], []);

        $this->contributorService->add($this->projectId, $this->contributorId, $this->userId, false, true);

        $this->db->queryFirstRow('UPDATE project_contributor SET permissions = \'{"tasks": 1}\' WHERE project_id = %s AND user_id = %i', $this->projectId, $this->contributorId);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_GET,
            uri: sprintf('/tasks/%d/report', $taskId),
        );

        static::assertResponseIsSuccessful();
    }

    public function testShouldNotGetReportByContributorWithFfWithoutProperPermission(): void
    {
        // TODO: Clean up in HOME-1009
        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => true]);

        $this->contributorService->add($this->projectId, $this->contributorId, $this->userId, false, false);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_GET,
            uri: sprintf('/tasks/%d/report', 1),
        );
        static::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function testShouldDownloadXliffByAdminWithoutFfWithProperPermission(): void
    {
        $this->initKeys();

        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => true]);

        $taskId = $this->fixtureManager->createTranslationTask($this->projectId, $this->userId, 'Test task', false, false, false, 640, [597], [$this->contributorId => [640, 597]], []);

        $this->contributorService->add($this->projectId, $this->contributorId, $this->userId, false, true, [], [], ['tasks' => 1]);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_GET,
            uri: sprintf('/tasks/%d/download-xliff', $taskId),
        );

        static::assertResponseIsSuccessful();
    }

    // TODO: Clean up in HOME-1009
    public function testShouldNotDownloadXliffByNonAdminWithoutFfWithoutProperPermission(): void
    {
        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => false]);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_GET,
            uri: sprintf('/tasks/%d/download-xliff', 1),
        );
        static::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function testShouldDownloadXliffByContributorWithFfWithProperPermission(): void
    {
        $this->initKeys();

        // TODO: Clean up in HOME-1009
        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => true]);

        $taskId = $this->fixtureManager->createTranslationTask($this->projectId, $this->userId, 'Test task', false, false, false, 640, [597], [$this->contributorId => [640, 597]], []);

        $this->contributorService->add($this->projectId, $this->contributorId, $this->userId, false, true);

        $this->db->queryFirstRow('UPDATE project_contributor SET permissions = \'{"tasks": 1}\' WHERE project_id = %s AND user_id = %i', $this->projectId, $this->contributorId);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_GET,
            uri: sprintf('/tasks/%d/download-xliff', $taskId),
        );

        static::assertResponseIsSuccessful();
    }

    public function testShouldNotDownloadXliffByContributorWithFfWithoutProperPermission(): void
    {
        // TODO: Clean up in HOME-1009
        $this->overrideFeatureFlagStates([PermissionsFeatureFlag::PHASE_1 => true]);

        $this->contributorService->add($this->projectId, $this->contributorId, $this->userId, false, false);

        $this->login($this->client, $this->contributorEmail);

        $this->client->request(
            method: Request::METHOD_GET,
            uri: sprintf('/tasks/%d/download-xliff', 1),
        );
        static::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    /**
     * @test
     *
     * @dataProvider deprecatedEndpointsDataProvider
     */
    public function shouldReturnEntityGoneIfEndpointIsDeprecatedAndFeatureFlagIsOn(string $method, string $url): void
    {
        // Given
        $this->overrideFeatureFlagStates([TasksRemoveUnusedEndpointsFeatureFlag::NAME => true]);

        $this->login($this->client, $this->contributorEmail);

        // When
        $this->client->request(
            $method,
            $url,
        );

        // Then
        $response = $this->client->getResponse();
        static::assertThat($response->getStatusCode(), static::equalTo(Response::HTTP_GONE));
    }

    private function deprecatedEndpointsDataProvider(): array
    {
        return [
            'postBuildTaskPageNew' => [
                'method' => Request::METHOD_POST,
                'url' => '/tasks/my-project-id/new-task',
            ],
            'getTaskLoadStatus' => [
                'method' => Request::METHOD_GET,
                'url' => '/tasks/my-project-id/load/completed',
            ],
            'getSearchContributors' => [
                'method' => Request::METHOD_GET,
                'url' => '/tasks/search-contributors',
            ],
            'getLangKeys' => [
                'method' => Request::METHOD_GET,
                'url' => '/tasks/lang-keys',
            ],
            'postUpdateTask' => [
                'method' => Request::METHOD_POST,
                'url' => '/tasks/update-task',
            ],
            'postSaveLangContributors' => [
                'method' => Request::METHOD_POST,
                'url' => '/tasks/save-lang-contributors',
            ],
            'postTaskReport' => [
                'method' => Request::METHOD_POST,
                'url' => '/tasks/123/report',
            ],
        ];
    }
}
