<?php

declare(strict_types=1);

namespace Lokalise\Tests\Application\LongFormContent\ContentPreview\Controller;

use Lokalise\Enums\FileFormat;
use Lokalise\Enums\ProjectType;
use Lokalise\Tests\AbstractWebTestCase;
use Lokalise\Tests\Application\Common\S3\FakeS3Service;
use Lokalise\Tests\Application\Common\S3\ProxyS3FileService;
use Lokalise\Tests\FixtureManager\FixtureManager;
use Lokalise\Tests\FixtureManager\LongFormContentFixtureManager;
use Lokalise\Tests\FixtureManager\UserFixtureManager;
use Symfony\Component\HttpFoundation\Response;

class FileContentControllerTest extends AbstractWebTestCase
{
    private const EN_CA_ID = 612;
    private const PL_PL_ID = 10157;

    private FixtureManager $fixtureManager;
    private UserFixtureManager $userFixtureManager;
    private FakeS3Service $fakeS3Service;
    private ProxyS3FileService $proxyS3FileService;
    private LongFormContentFixtureManager $longFormContentFixtureManager;

    private string $projectId;
    private string $email;

    protected function setUp(): void
    {
        parent::setUp();

        $this->fakeS3Service = new FakeS3Service();

        $this->proxyS3FileService = $this->getServiceById(ProxyS3FileService::class);
        $this->proxyS3FileService->overrideS3FileService($this->fakeS3Service);

        $this->fixtureManager = $this->getServiceById(FixtureManager::class);
        $this->userFixtureManager = $this->getServiceById(UserFixtureManager::class);
        $this->longFormContentFixtureManager = $this->getServiceById(LongFormContentFixtureManager::class);
        $this->longFormContentFixtureManager->setFakeS3Service($this->fakeS3Service);
        $this->projectId = $this->fixtureManager->generateProjectId();
        $this->email = $this->fixtureManager->generateEmail();

        $this->fixtureTeamOwner = $this->userFixtureManager->createFixtureTeamOwner($this->email, 'John Doe');

        $this->fixtureManager->initProject(
            $this->fixtureTeamOwner->getUserId(),
            $this->fixtureTeamOwner->getTeamId(),
            $this->projectId,
            'File content project',
            [
                self::EN_CA_ID => 'en_CA',
                self::PL_PL_ID => 'pl_PL',
            ],
            projectType: ProjectType::PagedDocuments
        );
    }

    protected function tearDown(): void
    {
        $this->proxyS3FileService->reset();
        $this->longFormContentFixtureManager->tearDown();
        parent::tearDown();
    }

    /**
     * @test
     */
    public function shouldShowBadRequestIfNotDocumentTypeProject(): void
    {
        $projectId = $this->fixtureManager->generateProjectId();

        $this->fixtureManager->initProject(
            $this->fixtureTeamOwner->getUserId(),
            $this->fixtureTeamOwner->getTeamId(),
            $projectId,
            'Software localization project',
            [
                self::EN_CA_ID => 'en_CA',
                self::PL_PL_ID => 'pl_PL',
            ],
        );

        $this->login($this->client, $this->email);

        $this->client->request('GET', sprintf('/projects/%s/files/Some.html/file-content?langId=%d', $projectId, 640));

        static::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
    }

    /**
     * @test
     */
    public function shouldShowBadRequestIfNotHtml(): void
    {
        $this->longFormContentFixtureManager->createKeyWithFilenameAndTranslation($this->projectId, 'Word.docx', 'wordFileInS3', 'some_key', 'test', [self::EN_CA_ID => 'test'], FileFormat::Docx);

        $this->login($this->client, $this->email);

        $this->client->request(
            'GET',
            sprintf(
                '/projects/%s/files/Word.docx/file-content?langId=%d',
                $this->projectId,
                self::EN_CA_ID
            )
        );

        static::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
    }

    /**
     * @test
     */
    public function shouldRedirectToLoginIfUserNotAuthenticated(): void
    {
        $this->client->request(
            'GET',
            sprintf(
                '/projects/%s/files/Some.html/file-content?langId=%d',
                $this->projectId,
                self::EN_CA_ID
            )
        );

        $response = $this->client->getResponse();

        static::assertEquals(302, $response->getStatusCode());

        static::assertStringStartsWith('/login', $response->getTargetUrl());
    }

    /**
     * @test
     */
    public function shouldReturnBadRequestIfLangIdQueryParamMissing(): void
    {
        $this->login($this->client, $this->email);

        $this->client->request('GET', sprintf('/projects/%s/files/Some.html/file-content', $this->projectId));

        $response = $this->client->getResponse();

        static::assertEquals(400, $response->getStatusCode());
    }

    /**
     * @test
     */
    public function shouldForbidIfUserDoesNotHaveAccessRights(): void
    {
        $differentUserEmail = $this->fixtureManager->generateEmail();

        $this->userFixtureManager->createFixtureTeamOwner($differentUserEmail, 'Different User');

        $this->login($this->client, $differentUserEmail);

        $this->client->request(
            'GET',
            sprintf(
                '/projects/%s/files/Some.html/file-content?langId=%d',
                $this->projectId,
                self::EN_CA_ID
            )
        );

        $response = $this->client->getResponse();

        static::assertEquals(403, $response->getStatusCode());
    }

    /**
     * @test
     */
    public function shouldReturnBadRequestIfFileNotExists(): void
    {
        $this->login($this->client, $this->email);

        $this->client->request(
            'GET',
            sprintf(
                '/projects/%s/files/Nonexistent.html/file-content?langId=%d',
                $this->projectId,
                self::EN_CA_ID
            )
        );

        static::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
    }

    /**
     * @test
     */
    public function shouldRenderHtmlContent(): void
    {
        $this->longFormContentFixtureManager->createKeyWithFilenameAndTranslation($this->projectId, 'Some name with whitespace.html', 'someFileInS3', '/html/head/title', '<html><head><title>Title of the file</title></head></html>', [self::EN_CA_ID => 'Title of the file']);
        $this->longFormContentFixtureManager->createKeyWithFilenameAndTranslation($this->projectId, 'Unwanted.html', 'unwantedFileInS3', '/html/head/title', '<html><head><title>Title of the unwanted file</title></head></html>', [self::EN_CA_ID => 'Title of the unwanted file']);   // this file is created to make sure we exclude it thanks to the filename filter

        $this->login($this->client, $this->email);

        $this->client->request(
            'GET',
            sprintf(
                '/projects/%s/files/%s/file-content?langId=%d',
                $this->projectId,
                urlencode('Some name with whitespace.html'),
                self::EN_CA_ID
            )
        );

        $response = $this->client->getResponse();

        static::assertEquals(200, $response->getStatusCode());

        static::assertEquals('<html><head><title>Title of the file</title></head></html>', $response->getContent());

        static::assertEquals('sameorigin', $response->headers->get('x-frame-options'));
    }

    /**
     * @test
     */
    public function shouldRenderHtmlContentWithTargetLanguage(): void
    {
        $this->longFormContentFixtureManager->createKeyWithFilenameAndTranslation($this->projectId, 'Filename.html', 'filename', '/html/head/title', '<html><head><title>Title of the file</title></head></html>', [self::EN_CA_ID => 'Title of the file', self::PL_PL_ID => 'Tytuł pliku']);

        $this->login($this->client, $this->email);

        $this->client->request(
            'GET',
            sprintf(
                '/projects/%s/files/%s/file-content?langId=%d',
                $this->projectId,
                'Filename.html',
                self::PL_PL_ID
            )
        );

        $response = $this->client->getResponse();

        static::assertEquals(200, $response->getStatusCode());

        static::assertEquals('<html><head><title>Tytuł pliku</title></head></html>', $response->getContent());
    }

    /**
     * @test
     */
    public function shouldRenderHtmlContentWithBaseLanguageIfTargetTranslationMissing(): void
    {
        $this->longFormContentFixtureManager->createKeyWithFilenameAndTranslation($this->projectId, 'Filename.html', 'filename', '/html/head/title', '<html><head><title>Title of the file</title></head></html>', [self::EN_CA_ID => 'Title of the file', self::PL_PL_ID => '']);

        $this->login($this->client, $this->email);

        $this->client->request(
            'GET',
            sprintf(
                '/projects/%s/files/%s/file-content?langId=%d',
                $this->projectId,
                'Filename.html',
                self::PL_PL_ID
            )
        );

        $response = $this->client->getResponse();

        static::assertEquals(200, $response->getStatusCode());

        static::assertEquals('<html><head><title>Title of the file</title></head></html>', $response->getContent());
    }
}
