<?php

declare(strict_types=1);

namespace Lokalise\Tests\Application\Billing\Metrics\Controller;

use DateTime;
use DateTimeImmutable;
use DateTimeInterface;
use Faker\Factory as FakerFactory;
use Faker\Generator;
use Lokalise\Billing\Metrics\Dto\BillingMetricStatus;
use Lokalise\Constants\Plan;
use Lokalise\Coupon\Service\TeamDiscountCouponsService;
use Lokalise\Services\Stripe\StripeService;
use Lokalise\Services\TeamService;
use Lokalise\TeamPlanEntitlements\Services\TeamPlanEntitlementsService;
use Lokalise\Tests\AbstractWebTestCase;
use Lokalise\Tests\Application\Ai\FakeService\FakeSubscriptionService;
use Lokalise\Tests\FixtureManager\FixtureManager;
use Lokalise\Tests\TestFramework\PaymentsClient\InMemoryPaymentClient;
use Lokalise\Tests\TestFramework\PaymentsClient\TestPaymentClient;
use Stripe\StripeClient;
use Symfony\Component\Uid\Uuid;
use UserService;

use function json_decode;

class BillingMetricsControllerTest extends AbstractWebTestCase
{
    private int $teamId;
    private int $userId;
    private string $email;
    private string $name;
    private string $expiresInAYearString;
    private int $infiniteValue;

    protected FixtureManager $fixtureManager;
    protected Generator $faker;
    protected StripeService $mockStripeService;
    protected TeamService $teamService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->getContainer()->set('lokalise.service.stripe.subscription', new FakeSubscriptionService(
            $this->getServiceById(TeamDiscountCouponsService::class),
            [],
            $this->getServiceById(StripeClient::class),
            $this->getServiceById(TestPaymentClient::class),
        ));

        $this->testPaymentClient = $this->getServiceById(TestPaymentClient::class);

        $this->inMemoryPaymentClient = new InMemoryPaymentClient();
        $this->fixtureManager = static::getContainer()->get(FixtureManager::class);
        $this->teamService = static::getContainer()->get(TeamService::class);
        $this->userService = $userService = static::getContainer()->get(UserService::class);

        $this->fixtureManager->setUp();

        $this->faker = FakerFactory::create();
        $this->email = $this->faker->safeEmail();
        $this->name = 'julio';
        $this->userId = $this->fixtureManager->createUser(
            ['email' => $this->email, 'fullname' => $this->name, 'admin' => 1, 'super_admin' => 1]
        );

        // Expires in a year
        $expires = new DateTime('+1 year');
        $this->expiresInAYearString = $expires->format('Y-m-d H:i:s');

        $this->teamId = $this->fixtureManager->createTeam([
            'name' => 'Team One',
            'owner_id' => $this->userId,
            'expires' => $this->expiresInAYearString,
            'billing_details' => '{"zip": "22000", "city": "Lima", "phone": "007", "company": "Acme", "address1": "str 1", "address2": "add2", "vatnumber": "1", "state_code": "AL", "country_code": "PE", "billing_email": "<EMAIL>"}',
        ]);
        $this->fixtureManager->attachUserToTeam(
            ['team_id' => $this->teamId, 'user_id' => $this->userId, 'is_admin' => 1, 'is_owner' => 1, 'is_biller' => 1]
        );
        $teamId = $this->teamId;
        $userService->updateUser($this->userId, ['current_team_id' => $teamId]);

        $this->infiniteValue = TeamPlanEntitlementsService::INFINITY_LIMIT_VALUE;
    }

    public function testGetBillingMetricsTeamNotFound(): void
    {
        $this->login($this->client, $this->email);

        // Set session team id, this value is required, before sending the request.
        $bag = $this->client->getContainer()->get('session')->getBag('team');
        $bag->set('id', $this->teamId);

        $this->client->request(
            'GET',
            '/v1/teams/' . Uuid::v4()->toRfc4122() . '/billing-metrics'
        );
        static::assertEquals(400, $this->client->getResponse()->getStatusCode());
    }

    public function testGetBillingMetricsV1Trial(): void
    {
        $this->login($this->client, $this->email);

        // Set session team id, this value is required, before sending the request.
        $bag = $this->client->getContainer()->get('session')->getBag('team');
        $bag->set('id', $this->teamId);

        $team = $this->teamService->getTeamEntityById($this->teamId);

        $this->client->request(
            'GET',
            '/v1/teams/' . $team->getUuid() . '/billing-metrics'
        );
        $this->assertResponseIsSuccessful();
        $response = json_decode($this->client->getResponse()->getContent(), true);
        static::assertNotNull($response);

        $expectedResponse = [
            'data' => [
                [
                    'metricCode' => 'keyLimit',
                    'currentValue' => 0,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'projectLimit',
                    'currentValue' => 0,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'contributorLimit',
                    'currentValue' => 1,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'glossaryLimit',
                    'currentValue' => null,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'mauLimit',
                    'currentValue' => null,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'automationLimit',
                    'currentValue' => 0,
                    'limitValue' => 150,
                    'metricStatus' => BillingMetricStatus::OK->value,
                ],
                [
                    'metricCode' => 'automationLimitMtChars',
                    'currentValue' => 0,
                    'limitValue' => 10000,
                    'metricStatus' => BillingMetricStatus::OK->value,
                ],
                [
                    'metricCode' => 'maxNumberOfProjectLevelWebhookHandlers',
                    'currentValue' => null,
                    'limitValue' => 20,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'maxNumberOfTeamWebhookLevelHandlers',
                    'currentValue' => null,
                    'limitValue' => 500,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'otaTrafficLimit',
                    'currentValue' => 0,
                    'limitValue' => 1,
                    'metricStatus' => BillingMetricStatus::OK->value,
                ],
                [
                    'metricCode' => 'aiUsageWordsLimit',
                    'currentValue' => 0,
                    'limitValue' => null,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'workflowsNumberLimit',
                    'currentValue' => null,
                    'limitValue' => 50,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'workflowsTemplateGateLevel',
                    'currentValue' => null,
                    'limitValue' => 3,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'processedWordsLimit',
                    'currentValue' => null,
                    'limitValue' => 0,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'hostedWordsLimit',
                    'currentValue' => null,
                    'limitValue' => 0,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'advancedSeatsLimit',
                    'currentValue' => null,
                    'limitValue' => 0,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'proAiTranslationsLimit',
                    'currentValue' => null,
                    'limitValue' => 0,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'languagesLimit',
                    'currentValue' => null,
                    'limitValue' => 0,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'apiCallsRateLimit',
                    'currentValue' => null,
                    'limitValue' => 0,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'integrationsLimit',
                    'currentValue' => null,
                    'limitValue' => 0,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'automationV2Limit',
                    'currentValue' => null,
                    'limitValue' => 0,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
            ],
            'meta' => [
                'count' => 21,
            ],
        ];

        static::assertNotEmpty($response['meta']['retrievedAt']);
        $date = DateTimeImmutable::createFromFormat(DateTimeInterface::ATOM, $response['meta']['retrievedAt']);
        static::assertInstanceOf(DateTimeImmutable::class, $date);

        unset($response['meta']['retrievedAt']);
        static::assertSame($expectedResponse, $response);
    }

    public function testGetBillingMetricsV2Pro(): void
    {
        $this->teamService->updateById($this->teamId, ['plan' => Plan::V2_SEAT_BASED_PRO_ANNUAL]);

        $invitedUserId = $this->fixtureManager->createUser(['email' => '<EMAIL>', 'fullname' => 'invited']);
        $this->fixtureManager->attachUserToTeam(
            ['team_id' => $this->teamId, 'user_id' => $invitedUserId, 'is_admin' => 1, 'is_owner' => 1]
        );

        $this->login($this->client, $this->email);

        // Set session team id, this value is required, before sending the request.
        $bag = $this->client->getContainer()->get('session')->getBag('team');
        $bag->set('id', $this->teamId);

        $team = $this->teamService->getTeamEntityById($this->teamId);

        $this->client->request(
            'GET',
            '/v1/teams/' . $team->getUuid() . '/billing-metrics'
        );
        $this->assertResponseIsSuccessful();
        $response = json_decode($this->client->getResponse()->getContent(), true);
        static::assertNotNull($response);

        $expectedResponse = [
            'data' => [
                [
                    'metricCode' => 'keyLimit',
                    'currentValue' => 0,
                    'limitValue' => 30000,
                    'metricStatus' => BillingMetricStatus::OK->value,
                ],
                [
                    'metricCode' => 'projectLimit',
                    'currentValue' => 0,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'contributorLimit',
                    'currentValue' => 2,
                    'limitValue' => 0,
                    'metricStatus' => BillingMetricStatus::UPGRADE_REQUIRED->value,
                ],
                [
                    'metricCode' => 'glossaryLimit',
                    'currentValue' => null,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'mauLimit',
                    'currentValue' => null,
                    'limitValue' => 1000000,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'automationLimit',
                    'currentValue' => 0,
                    'limitValue' => 100,
                    'metricStatus' => BillingMetricStatus::OK->value,
                ],
                [
                    'metricCode' => 'automationLimitMtChars',
                    'currentValue' => 0,
                    'limitValue' => 1000000,
                    'metricStatus' => BillingMetricStatus::OK->value,
                ],
                [
                    'metricCode' => 'maxNumberOfProjectLevelWebhookHandlers',
                    'currentValue' => null,
                    'limitValue' => 20,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'maxNumberOfTeamWebhookLevelHandlers',
                    'currentValue' => null,
                    'limitValue' => 500,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'otaTrafficLimit',
                    'currentValue' => 0,
                    'limitValue' => 200,
                    'metricStatus' => BillingMetricStatus::OK->value,
                ],
                [
                    'metricCode' => 'aiUsageWordsLimit',
                    'currentValue' => 0,
                    'limitValue' => null,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'workflowsNumberLimit',
                    'currentValue' => null,
                    'limitValue' => 0,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'workflowsTemplateGateLevel',
                    'currentValue' => null,
                    'limitValue' => 3,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'processedWordsLimit',
                    'currentValue' => null,
                    'limitValue' => 0,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'hostedWordsLimit',
                    'currentValue' => null,
                    'limitValue' => 0,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'advancedSeatsLimit',
                    'currentValue' => null,
                    'limitValue' => 0,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'proAiTranslationsLimit',
                    'currentValue' => null,
                    'limitValue' => 0,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'languagesLimit',
                    'currentValue' => null,
                    'limitValue' => 0,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'apiCallsRateLimit',
                    'currentValue' => null,
                    'limitValue' => 0,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'integrationsLimit',
                    'currentValue' => null,
                    'limitValue' => 0,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'automationV2Limit',
                    'currentValue' => null,
                    'limitValue' => 0,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
            ],
            'meta' => [
                'count' => 21,
            ],
        ];

        static::assertNotEmpty($response['meta']['retrievedAt']);
        $date = DateTimeImmutable::createFromFormat(DateTimeInterface::ATOM, $response['meta']['retrievedAt']);
        static::assertInstanceOf(DateTimeImmutable::class, $date);

        unset($response['meta']['retrievedAt']);
        static::assertSame($expectedResponse, $response);
    }

    public function testGetBillingMetricsNewPricing(): void
    {
        $this->teamService->updateById($this->teamId, ['plan' => Plan::GEN2_ADVANCED_MONTHLY]);

        $this->login($this->client, $this->email);

        // Set session team id, this value is required, before sending the request.
        $bag = $this->client->getContainer()->get('session')->getBag('team');
        $bag->set('id', $this->teamId);

        $team = $this->teamService->getTeamEntityById($this->teamId);

        $this->client->request(
            'GET',
            '/v1/teams/' . $team->getUuid() . '/billing-metrics'
        );
        self::assertResponseIsSuccessful();
        $response = json_decode($this->client->getResponse()->getContent(), true);
        static::assertNotNull($response);

        $expectedResponse = [
            'data' => [
                [
                    'metricCode' => 'keyLimit',
                    'currentValue' => 0,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'projectLimit',
                    'currentValue' => 0,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'contributorLimit',
                    'currentValue' => 1,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'glossaryLimit',
                    'currentValue' => null,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'mauLimit',
                    'currentValue' => null,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'automationLimit',
                    'currentValue' => 0,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'automationLimitMtChars',
                    'currentValue' => 0,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'maxNumberOfProjectLevelWebhookHandlers',
                    'currentValue' => null,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'maxNumberOfTeamWebhookLevelHandlers',
                    'currentValue' => null,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'otaTrafficLimit',
                    'currentValue' => 0,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'aiUsageWordsLimit',
                    'currentValue' => 0,
                    'limitValue' => null,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'workflowsNumberLimit',
                    'currentValue' => null,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'workflowsTemplateGateLevel',
                    'currentValue' => null,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'processedWordsLimit',
                    'currentValue' => 1,
                    'limitValue' => 1000000,
                    'metricStatus' => BillingMetricStatus::OK->value,
                ],
                [
                    'metricCode' => 'hostedWordsLimit',
                    'currentValue' => null,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'advancedSeatsLimit',
                    'currentValue' => 1,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'proAiTranslationsLimit',
                    'currentValue' => null,
                    'limitValue' => 150000,
                    'metricStatus' => BillingMetricStatus::UNKNOWN->value,
                ],
                [
                    'metricCode' => 'languagesLimit',
                    'currentValue' => null,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'apiCallsRateLimit',
                    'currentValue' => null,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'integrationsLimit',
                    'currentValue' => 1,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'automationV2Limit',
                    'currentValue' => null,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
            ],
            'meta' => [
                'count' => 21,
            ],
        ];

        static::assertNotEmpty($response['meta']['retrievedAt']);
        $date = DateTimeImmutable::createFromFormat(DateTimeInterface::ATOM, $response['meta']['retrievedAt']);
        static::assertInstanceOf(DateTimeImmutable::class, $date);

        unset($response['meta']['retrievedAt']);

        static::assertSame($expectedResponse, $response);
    }

    public function testGetBillingMetricsWithFilter(): void
    {
        $this->login($this->client, $this->email);

        // Set session team id, this value is required, before sending the request.
        $bag = $this->client->getContainer()->get('session')->getBag('team');
        $bag->set('id', $this->teamId);

        $team = $this->teamService->getTeamEntityById($this->teamId);

        $this->client->request(
            'GET',
            '/v1/teams/' . $team->getUuid() . '/billing-metrics?filter-metricCode[]=keyLimit&filter-metricCode[]=projectLimit'
        );
        $this->assertResponseIsSuccessful();
        $response = json_decode($this->client->getResponse()->getContent(), true);
        static::assertNotNull($response);

        $expectedResponse = [
            'data' => [
                [
                    'metricCode' => 'keyLimit',
                    'currentValue' => 0,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
                [
                    'metricCode' => 'projectLimit',
                    'currentValue' => 0,
                    'limitValue' => $this->infiniteValue,
                    'metricStatus' => BillingMetricStatus::UNLIMITED->value,
                ],
            ],
            'meta' => [
                'count' => 2,
            ],
        ];

        static::assertNotEmpty($response['meta']['retrievedAt']);
        unset($response['meta']['retrievedAt']);
        static::assertSame($expectedResponse, $response);
    }

    public function testGetBillingMetricsWrongFilter(): void
    {
        $this->login($this->client, $this->email);

        $team = $this->teamService->getTeamEntityById($this->teamId);

        // Set session team id, this value is required, before sending the request.
        $bag = $this->client->getContainer()->get('session')->getBag('team');
        $bag->set('id', $this->teamId);

        $this->client->request(
            'GET',
            '/v1/teams/' . $team->getUuid() . '/billing-metrics?filter-metricCode[]=WOWWW'
        );
        static::assertEquals(400, $this->client->getResponse()->getStatusCode());
        $response = json_decode($this->client->getResponse()->getContent(), true);
        static::assertNotNull($response);
        static::assertSame(
            'Validation failed.',
            $response['error']['message']
        );
        static::assertSame('Unknown metric code: "WOWWW".', $response['error']['violations']['filterMetricCodes0'][0]);
    }

    protected function tearDown(): void
    {
        $this->fixtureManager->tearDown();
        parent::tearDown();
    }
}
