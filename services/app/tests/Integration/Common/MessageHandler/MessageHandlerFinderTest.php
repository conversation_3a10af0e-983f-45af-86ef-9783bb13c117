<?php

declare(strict_types=1);

namespace Lokalise\Tests\Integration\Common\MessageHandler;

use Lokalise\Common\MessageHandler\MessageHandlerInterface;
use Lokalise\Common\MessageHandler\Service\MessageHandlerFinder;
use Lokalise\Common\MessageHandler\Service\MessageHandlerRegistry;
use Lokalise\Orders\Message\ProcessTranslationUploadOrderMessage;
use Lokalise\Services\Stripe\Subscription\Message\UpdatingDefaultPaymentMethod;
use Lokalise\Tests\AbstractKernelTestCase;

class MessageHandlerFinderTest extends AbstractKernelTestCase
{
    private MessageHandlerFinder $finder;
    private MessageHandlerRegistry $registry;

    protected function setUp(): void
    {
        parent::setUp();

        $this->finder = $this->getServiceById(MessageHandlerFinder::class);
        $this->registry = $this->getServiceById(MessageHandlerRegistry::class);
    }

    /** @test
     */
    public function shouldNotInstantiateHandlerIfItsNotMeantToBeUsed(): void
    {
        $payload = serialize(new UpdatingDefaultPaymentMethod(0));
        $rawMessage = ['params' => $payload];

        $handler = $this->finder->locate($rawMessage);
        static::assertInstanceOf(MessageHandlerInterface::class, $handler);

        $message = $this->finder->processPayload($payload);

        $handler($message);
        static::assertThat(DummyMessageHandler::getConstructorCounter(), static::equalTo(0));
    }

    /** @test */
    public function shouldInstantiateHandlerOnlyOnceIfItsMeantToBeUsed(): void
    {
        static::assertThat(DummyMessageHandler::getConstructorCounter(), static::equalTo(0));

        $payload = serialize(new DummyMessage(1));
        $rawMessage = ['params' => $payload];

        $handler = $this->finder->locate($rawMessage);
        static::assertInstanceOf(MessageHandlerInterface::class, $handler);

        $message = $this->finder->processPayload($payload);

        $handler($message);
        $handler($message);

        static::assertThat(DummyMessageHandler::getConstructorCounter(), static::equalTo(1));
    }

    /** @test */
    public function shouldHaveCorrectMappingInRegistry(): void
    {
        $registeredHandlers = $this->registry->getAllowedClassesForDeserialization();

        static::assertContains(DummyMessage::class, $registeredHandlers);
    }

    /** @test */
    public function shouldRegisterProcessUploadOrderMessageInRegistryThatIsNotExplicitlyTagged(): void
    {
        $registeredHandlers = $this->registry->getAllowedClassesForDeserialization();

        static::assertContains(ProcessTranslationUploadOrderMessage::class, $registeredHandlers);
    }
}
