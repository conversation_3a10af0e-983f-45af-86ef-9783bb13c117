<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="1.2" xsi:schemaLocation="urn:oasis:names:tc:xliff:document:1.2 http://docs.oasis-open.org/xliff/v1.2/os/xliff-core-1.2-strict.xsd">
	<file original="" datatype="plaintext" xml:space="preserve" source-language="en" target-language="ru">
		<header>
			<tool tool-id="lokalise.com" tool-name="Lokalise"></tool>
		</header>
		<body>
			<trans-unit id="placeholder_key">
				<source>(placeholder_key) Hello %s!</source>
				<target>(placeholder_key) Привет, %s!</target>
			</trans-unit>
			<trans-unit id="multi_non_positional_placeholder_key">
				<source>(multi_non_positional_placeholder_key) Hello %s! My name is %s.</source>
				<target>(multi_non_positional_placeholder_key) Привет, %s! Меня зовут %s.</target>
			</trans-unit>
			<trans-unit id="multi_positional_placeholder_key">
				<source>(multi_positional_placeholder_key) Hello %1$s! My name is %2$s.</source>
				<target>(multi_positional_placeholder_key) Привет, %1$s! Меня зовут %2$s.</target>
			</trans-unit>
			<trans-unit id="universal_placeholder_key">
				<source>(universal_placeholder_key) Hello %@!</source>
				<target>(universal_placeholder_key) Привет, %@!</target>
			</trans-unit>
			<trans-unit id="multi_universal_placeholder_non_positional_key">
				<source>(multi_universal_placeholder_non_positional_key) Hello %@! My name is %@.</source>
				<target>(multi_universal_placeholder_non_positional_key) Привет, %@! Меня зовут %@.</target>
			</trans-unit>
			<trans-unit id="multi_universal_positional_placeholder_key">
				<source>(multi_universal_positional_placeholder_key) Hello %1$@! My name is %2$@.</source>
				<target>(multi_universal_positional_placeholder_key) Привет, %1$@! Меня зовут %2$@.</target>
			</trans-unit>
			<trans-unit id="multi_universal_named_positional_placeholder_key">
				<source>(multi_universal_named_positional_placeholder_key) Hello %1$@! My name is %2$@.</source>
				<target>(multi_universal_named_positional_placeholder_key) Привет, %1$@! Меня зовут %2$@.</target>
			</trans-unit>
			<trans-unit id="placeholder_plural_key:dict/NSStringLocalizedFormatKey:dict/:string">
				<source>format</source>
				<target>format</target>
			</trans-unit>
			<trans-unit id="/placeholder_plural_key:dict/format:dict/one:dict/:string">
				<source>(placeholder_plural_key) %d cat</source>
				<target>(placeholder_plural_key) %d кот</target>
			</trans-unit>
			<trans-unit id="/placeholder_plural_key:dict/format:dict/other:dict/:string">
				<source>(placeholder_plural_key) %d cats</source>
				<target>(placeholder_plural_key) %d котов</target>
			</trans-unit>
			<trans-unit id="/placeholder_plural_key:dict/format:dict/few:dict/:string">
				<source>(placeholder_plural_key) %d cats</source>
				<target>(placeholder_plural_key) %d кота</target>
			</trans-unit>
			<trans-unit id="/placeholder_plural_key:dict/format:dict/many:dict/:string">
				<source>(placeholder_plural_key) %d cats</source>
				<target>(placeholder_plural_key) %d котов</target>
			</trans-unit>
			<trans-unit id="placeholder_line_break_plural_key:dict/NSStringLocalizedFormatKey:dict/:string">
				<source>format</source>
				<target>format</target>
			</trans-unit>
			<trans-unit id="/placeholder_line_break_plural_key:dict/format:dict/one:dict/:string">
				<source>(placeholder_line_break_plural_key) %d
cat</source>
				<target>(placeholder_line_break_plural_key) %d
кот</target>
			</trans-unit>
			<trans-unit id="/placeholder_line_break_plural_key:dict/format:dict/other:dict/:string">
				<source>(placeholder_line_break_plural_key) %d
cats</source>
				<target>(placeholder_line_break_plural_key) %d
котов</target>
			</trans-unit>
			<trans-unit id="/placeholder_line_break_plural_key:dict/format:dict/few:dict/:string">
				<source>(placeholder_line_break_plural_key) %d
cats</source>
				<target>(placeholder_line_break_plural_key) %d
кота</target>
			</trans-unit>
			<trans-unit id="/placeholder_line_break_plural_key:dict/format:dict/many:dict/:string">
				<source>(placeholder_line_break_plural_key) %d
cats</source>
				<target>(placeholder_line_break_plural_key) %d
котов</target>
			</trans-unit>
			<trans-unit id="universal_placeholder_plural_key:dict/NSStringLocalizedFormatKey:dict/:string">
				<source>format</source>
				<target>format</target>
			</trans-unit>
			<trans-unit id="/universal_placeholder_plural_key:dict/format:dict/one:dict/:string">
				<source>(universal_placeholder_plural_key) %li cat</source>
				<target>(universal_placeholder_plural_key) %li кот</target>
			</trans-unit>
			<trans-unit id="/universal_placeholder_plural_key:dict/format:dict/other:dict/:string">
				<source>(universal_placeholder_plural_key) %li cats</source>
				<target>(universal_placeholder_plural_key) %li котов</target>
			</trans-unit>
			<trans-unit id="/universal_placeholder_plural_key:dict/format:dict/few:dict/:string">
				<source>(universal_placeholder_plural_key) %li cats</source>
				<target>(universal_placeholder_plural_key) %li кота</target>
			</trans-unit>
			<trans-unit id="/universal_placeholder_plural_key:dict/format:dict/many:dict/:string">
				<source>(universal_placeholder_plural_key) %li cats</source>
				<target>(universal_placeholder_plural_key) %li котов</target>
			</trans-unit>
		</body>
	</file>
</xliff>
