[{"iso": "en", "items": [{"type": 1, "key": "array_key", "value": "[\"(array_key[0]) Hello world!\",\"(array_key[1]) Hello world!\",\"(array_key[2]) Hello world!\"]"}, {"type": 1, "key": "cross_project_array_key", "value": "[\"(cross_project_array_key[0]) Hello world! (Project 1)\",\"(cross_project_array_key[1]) Hello world! (Project 1)\",\"(cross_project_array_key[2]) Hello world! (Project 1)\"]"}, {"type": 0, "key": "simple_key", "value": "(simple_key) Hello world!"}, {"type": 0, "key": "simple_key_with_0_as_translation", "value": "0"}, {"type": 0, "key": "simple_key_with_description", "value": "(simple_key_with_description) Hello world!"}, {"type": 0, "key": "simple_key_with_comments", "value": "(simple_key_with_comments) Hello world!"}, {"type": 0, "key": "simple_key_with_description_and_comments", "value": "(simple_key_with_description_and_comments) Hello world!"}, {"type": 0, "key": "simple_android_only_key", "value": "(simple_android_only_key) Hello world!"}, {"type": 0, "key": "simple_ios_android_only_key", "value": "(simple_ios_android_only_key) Hello world!"}, {"type": 0, "key": "simple_android_web_only_key", "value": "(simple_android_web_only_key) Hello world!"}, {"type": 0, "key": "same_name_across_projects_key", "value": "(same_name_across_projects_key) Hello world! (Project 1)"}, {"type": 0, "key": "referenced_key", "value": "(referenced_key) Hello world!"}, {"type": 0, "key": "with_reference_key", "value": "(with_reference_key) Hello world! (referenced_key) Hello world!"}, {"type": 0, "key": "with_reference_different_file_key", "value": "(with_reference_different_file_key) Hello world! (referenced_key) Hello world!"}, {"type": 0, "key": "partially_with_reference_key", "value": "(partially_with_reference_key) Hello world! (referenced_key) Hello world!"}, {"type": 0, "key": "reference_only_key", "value": "(referenced_key) Hello world!"}, {"type": 0, "key": "partial_reference_only_key", "value": "(referenced_key) Hello world!"}, {"type": 0, "key": "simple_file_with_iso_key", "value": "(simple_file_with_iso_key) Hello world!"}, {"type": 0, "key": "empty_key", "value": ""}, {"type": 0, "key": "untranslated_key", "value": "(untranslated_key) Hello world!"}, {"type": 0, "key": "untranslated_reference_only_key", "value": "(untranslated_key) Hello world!"}, {"type": 0, "key": "partially_untranslated_key", "value": "(partially_untranslated_key) Hello world!"}, {"type": 0, "key": "partially_untranslated_reference_only_key", "value": "(untranslated_key) Hello world!"}, {"type": 0, "key": "void_key", "value": ""}, {"type": 0, "key": "void_reference_only_key", "value": ""}, {"type": 0, "key": "true_key", "value": "1"}, {"type": 0, "key": "true_reference_only_key", "value": "1"}, {"type": 0, "key": "false_key", "value": "0"}, {"type": 0, "key": "false_reference_only_key", "value": "0"}, {"type": 0, "key": "space_key", "value": " "}, {"type": 0, "key": "space_reference_only_key", "value": " "}, {"type": 0, "key": "universal_percent_key", "value": "(universal_percent_key) Hello world! 100%!"}, {"type": 0, "key": "untranslated_without_file_key", "value": "(untranslated_without_file_key) Hello world!"}, {"type": 0, "key": "line_break_key", "value": "(line_break_key) Hello<br>world!"}, {"type": 0, "key": "hidden_key", "value": "(hidden_key) Hello world!"}, {"type": 0, "key": "fuzzy_key", "value": "(fuzzy_key) Hello world!"}, {"type": 0, "key": "fuzzy_base_key", "value": "(fuzzy_base_key) Hello world!"}, {"type": 0, "key": "fuzzy_non_base_key", "value": "(fuzzy_non_base_key) Hello world!"}, {"type": 0, "key": "hidden_fuzzy_key", "value": "(hidden_fuzzy_key) Hello world!"}, {"type": 0, "key": "hidden_fuzzy_base_key", "value": "(hidden_fuzzy_base_key) Hello world!"}, {"type": 0, "key": "hidden_fuzzy_non_base_key", "value": "(hidden_fuzzy_non_base_key) Hello world!"}, {"type": 0, "key": "reviewed_key", "value": "(reviewed_key) Hello world!"}, {"type": 0, "key": "reviewed_base_key", "value": "(reviewed_base_key) Hello world!"}, {"type": 0, "key": "reviewed_non_base_key", "value": "(reviewed_non_base_key) Hello world!"}, {"type": 0, "key": "hidden_reviewed_key", "value": "(hidden_reviewed_key) Hello world!"}, {"type": 0, "key": "hidden_reviewed_base_key", "value": "(hidden_reviewed_base_key) Hello world!"}, {"type": 0, "key": "hidden_reviewed_non_base_key", "value": "(hidden_reviewed_non_base_key) Hello world!"}, {"type": 0, "key": "placeholder_key", "value": "(placeholder_key) Hello %s!"}, {"type": 0, "key": "multi_non_positional_placeholder_key", "value": "(multi_non_positional_placeholder_key) Hello %s! My name is %s."}, {"type": 0, "key": "multi_positional_placeholder_key", "value": "(multi_positional_placeholder_key) Hello %1$s! My name is %2$s."}, {"type": 0, "key": "universal_placeholder_key", "value": "(universal_placeholder_key) Hello %s!"}, {"type": 0, "key": "multi_universal_placeholder_non_positional_key", "value": "(multi_universal_placeholder_non_positional_key) Hello %s! My name is %s."}, {"type": 0, "key": "multi_universal_positional_placeholder_key", "value": "(multi_universal_positional_placeholder_key) Hello %1$s! My name is %2$s."}, {"type": 0, "key": "multi_universal_named_positional_placeholder_key", "value": "(multi_universal_named_positional_placeholder_key) Hello %1$s! My name is %2$s."}, {"type": 0, "key": "key_name_ends_with_plural", "value": "(key_name_ends_with_plural) Hello world!"}, {"type": 2, "key": "plural_key_name_ends_with_plural", "value": "{\"one\":\"(plural_key_name_ends_with_plural) cat\",\"other\":\"(plural_key_name_ends_with_plural) cats\"}"}, {"type": 2, "key": "plural_key", "value": "{\"one\":\"(plural_key) cat\",\"other\":\"(plural_key) cats\"}"}, {"type": 2, "key": "empty_plural_key", "value": "{\"one\":\"\",\"other\":\"\"}"}, {"type": 2, "key": "untranslated_plural_key", "value": "{\"one\":\"(untranslated_plural_key) cat\",\"other\":\"(untranslated_plural_key) cats\"}"}, {"type": 2, "key": "partially_untranslated_plural_key", "value": "{\"one\":\"(partially_untranslated_plural_key) cat\",\"other\":\"(partially_untranslated_plural_key) cats\"}"}, {"type": 2, "key": "placeholder_plural_key", "value": "{\"one\":\"(placeholder_plural_key) %d cat\",\"other\":\"(placeholder_plural_key) %d cats\"}"}, {"type": 2, "key": "placeholder_line_break_plural_key", "value": "{\"one\":\"(placeholder_line_break_plural_key) %d<br>cat\",\"other\":\"(placeholder_line_break_plural_key) %d<br>cats\"}"}, {"type": 2, "key": "universal_placeholder_plural_key", "value": "{\"one\":\"(universal_placeholder_plural_key) %d cat\",\"other\":\"(universal_placeholder_plural_key) %d cats\"}"}, {"type": 0, "key": "tagged_key_with_tag1", "value": "(tagged_key_with_tag1) Hello world! tag1"}, {"type": 0, "key": "tagged_key_with_tag1_tag2", "value": "(tagged_key_with_tag1_tag2) Hello world! tag1 tag2"}, {"type": 0, "key": "tagged_key_with_tag2", "value": "(tagged_key_with_tag2) Hello world! tag2"}, {"type": 0, "key": "marked_status_key_with_status1", "value": "(marked_status_key_with_status1) Hello world! status1"}, {"type": 0, "key": "marked_status_key_with_status1_status2", "value": "(marked_status_key_with_status1_status2) Hello world! status1 status2"}, {"type": 0, "key": "marked_status_key_with_status2", "value": "(marked_status_key_with_status2) Hello world! status2"}], "is_default": 1}, {"iso": "ru", "items": [{"type": 1, "key": "array_key", "value": "[\"(array_key[0]) \\u041f\\u0440\\u0438\\u0432\\u0435\\u0442, \\u043c\\u0438\\u0440!\",\"(array_key[1]) \\u041f\\u0440\\u0438\\u0432\\u0435\\u0442, \\u043c\\u0438\\u0440!\",\"(array_key[2]) \\u041f\\u0440\\u0438\\u0432\\u0435\\u0442, \\u043c\\u0438\\u0440!\"]"}, {"type": 1, "key": "cross_project_array_key", "value": "[\"(cross_project_array_key[0]) \\u041f\\u0440\\u0438\\u0432\\u0435\\u0442, \\u043c\\u0438\\u0440! (Project 1)\",\"(cross_project_array_key[1]) \\u041f\\u0440\\u0438\\u0432\\u0435\\u0442, \\u043c\\u0438\\u0440! (Project 1)\",\"(cross_project_array_key[2]) \\u041f\\u0440\\u0438\\u0432\\u0435\\u0442, \\u043c\\u0438\\u0440! (Project 1)\"]"}, {"type": 0, "key": "simple_key", "value": "(simple_key) Привет, мир!"}, {"type": 0, "key": "simple_key_with_0_as_translation", "value": "0"}, {"type": 0, "key": "simple_key_with_description", "value": "(simple_key_with_description) Привет, мир!"}, {"type": 0, "key": "simple_key_with_comments", "value": "(simple_key_with_comments) Привет, мир!"}, {"type": 0, "key": "simple_key_with_description_and_comments", "value": "(simple_key_with_description_and_comments) Привет, мир!"}, {"type": 0, "key": "simple_android_only_key", "value": "(simple_android_only_key) Привет, мир!"}, {"type": 0, "key": "simple_ios_android_only_key", "value": "(simple_ios_android_only_key) Привет, мир!"}, {"type": 0, "key": "simple_android_web_only_key", "value": "(simple_android_web_only_key) Привет, мир!"}, {"type": 0, "key": "same_name_across_projects_key", "value": "(same_name_across_projects_key) Привет, мир! (Project 1)"}, {"type": 0, "key": "referenced_key", "value": "(referenced_key) Привет, мир!"}, {"type": 0, "key": "with_reference_key", "value": "(with_reference_key) Привет, мир! (referenced_key) Привет, мир!"}, {"type": 0, "key": "with_reference_different_file_key", "value": "(with_reference_different_file_key) Привет, мир! (referenced_key) Привет, мир!"}, {"type": 0, "key": "partially_with_reference_key", "value": "(partially_with_reference_key) Привет, мир! (referenced_key) Привет, мир!"}, {"type": 0, "key": "reference_only_key", "value": "(referenced_key) Привет, мир!"}, {"type": 0, "key": "partial_reference_only_key", "value": "(referenced_key) Привет, мир!"}, {"type": 0, "key": "simple_file_with_iso_key", "value": "(simple_file_with_iso_key) Привет, мир!"}, {"type": 0, "key": "empty_key", "value": ""}, {"type": 0, "key": "untranslated_key", "value": ""}, {"type": 0, "key": "untranslated_reference_only_key", "value": ""}, {"type": 0, "key": "partially_untranslated_key", "value": "(partially_untranslated_key) Привет, мир!"}, {"type": 0, "key": "partially_untranslated_reference_only_key", "value": ""}, {"type": 0, "key": "void_key", "value": ""}, {"type": 0, "key": "void_reference_only_key", "value": ""}, {"type": 0, "key": "true_key", "value": "1"}, {"type": 0, "key": "true_reference_only_key", "value": "1"}, {"type": 0, "key": "false_key", "value": "0"}, {"type": 0, "key": "false_reference_only_key", "value": "0"}, {"type": 0, "key": "space_key", "value": " "}, {"type": 0, "key": "space_reference_only_key", "value": " "}, {"type": 0, "key": "universal_percent_key", "value": "(universal_percent_key) Привет, мир! 100%!"}, {"type": 0, "key": "untranslated_without_file_key", "value": ""}, {"type": 0, "key": "line_break_key", "value": "(line_break_key) Привет,<br>мир!"}, {"type": 0, "key": "hidden_key", "value": "(hidden_key) Привет, мир!"}, {"type": 0, "key": "fuzzy_key", "value": "(fuzzy_key) Привет, мир!"}, {"type": 0, "key": "fuzzy_base_key", "value": "(fuzzy_base_key) Привет, мир!"}, {"type": 0, "key": "fuzzy_non_base_key", "value": "(fuzzy_non_base_key) Привет, мир!"}, {"type": 0, "key": "hidden_fuzzy_key", "value": "(hidden_fuzzy_key) Привет, мир!"}, {"type": 0, "key": "hidden_fuzzy_base_key", "value": "(hidden_fuzzy_base_key) Привет, мир!"}, {"type": 0, "key": "hidden_fuzzy_non_base_key", "value": "(hidden_fuzzy_non_base_key) Привет, мир!"}, {"type": 0, "key": "reviewed_key", "value": "(reviewed_key) Привет, мир!"}, {"type": 0, "key": "reviewed_base_key", "value": "(reviewed_base_key) Привет, мир!"}, {"type": 0, "key": "reviewed_non_base_key", "value": "(reviewed_non_base_key) Привет, мир!"}, {"type": 0, "key": "hidden_reviewed_key", "value": "(hidden_reviewed_key) Привет, мир!"}, {"type": 0, "key": "hidden_reviewed_base_key", "value": "(hidden_reviewed_base_key) Привет, мир!"}, {"type": 0, "key": "hidden_reviewed_non_base_key", "value": "(hidden_reviewed_non_base_key) Привет, мир!"}, {"type": 0, "key": "placeholder_key", "value": "(placeholder_key) Привет, %s!"}, {"type": 0, "key": "multi_non_positional_placeholder_key", "value": "(multi_non_positional_placeholder_key) Привет, %s! Меня зовут %s."}, {"type": 0, "key": "multi_positional_placeholder_key", "value": "(multi_positional_placeholder_key) Привет, %1$s! Меня зовут %2$s."}, {"type": 0, "key": "universal_placeholder_key", "value": "(universal_placeholder_key) Привет, %s!"}, {"type": 0, "key": "multi_universal_placeholder_non_positional_key", "value": "(multi_universal_placeholder_non_positional_key) Привет, %s! Меня зовут %s."}, {"type": 0, "key": "multi_universal_positional_placeholder_key", "value": "(multi_universal_positional_placeholder_key) Привет, %1$s! Меня зовут %2$s."}, {"type": 0, "key": "multi_universal_named_positional_placeholder_key", "value": "(multi_universal_named_positional_placeholder_key) Привет, %1$s! Меня зовут %2$s."}, {"type": 0, "key": "key_name_ends_with_plural", "value": "(key_name_ends_with_plural) Привет, мир!"}, {"type": 2, "key": "plural_key_name_ends_with_plural", "value": "{\"one\":\"(plural_key_name_ends_with_plural) \\u043a\\u043e\\u0442\",\"few\":\"(plural_key_name_ends_with_plural) \\u043a\\u043e\\u0442\\u0430\",\"many\":\"(plural_key_name_ends_with_plural) \\u043a\\u043e\\u0442\\u043e\\u0432\",\"other\":\"(plural_key_name_ends_with_plural) \\u043a\\u043e\\u0442\\u043e\\u0432\"}"}, {"type": 2, "key": "plural_key", "value": "{\"one\":\"(plural_key) \\u043a\\u043e\\u0442\",\"few\":\"(plural_key) \\u043a\\u043e\\u0442\\u0430\",\"many\":\"(plural_key) \\u043a\\u043e\\u0442\\u043e\\u0432\",\"other\":\"(plural_key) \\u043a\\u043e\\u0442\\u043e\\u0432\"}"}, {"type": 2, "key": "empty_plural_key", "value": "{\"one\":\"\",\"few\":\"\",\"many\":\"\",\"other\":\"\"}"}, {"type": 2, "key": "untranslated_plural_key", "value": "{\"one\":\"\",\"few\":\"\",\"many\":\"\",\"other\":\"\"}"}, {"type": 2, "key": "partially_untranslated_plural_key", "value": "{\"one\":\"(partially_untranslated_plural_key) \\u043a\\u043e\\u0442\",\"few\":\"(partially_untranslated_plural_key) \\u043a\\u043e\\u0442\\u0430\",\"many\":\"(partially_untranslated_plural_key) \\u043a\\u043e\\u0442\\u043e\\u0432\",\"other\":\"(partially_untranslated_plural_key) \\u043a\\u043e\\u0442\\u043e\\u0432\"}"}, {"type": 2, "key": "placeholder_plural_key", "value": "{\"one\":\"(placeholder_plural_key) %d \\u043a\\u043e\\u0442\",\"few\":\"(placeholder_plural_key) %d \\u043a\\u043e\\u0442\\u0430\",\"many\":\"(placeholder_plural_key) %d \\u043a\\u043e\\u0442\\u043e\\u0432\",\"other\":\"(placeholder_plural_key) %d \\u043a\\u043e\\u0442\\u043e\\u0432\"}"}, {"type": 2, "key": "placeholder_line_break_plural_key", "value": "{\"one\":\"(placeholder_line_break_plural_key) %d<br>\\u043a\\u043e\\u0442\",\"few\":\"(placeholder_line_break_plural_key) %d<br>\\u043a\\u043e\\u0442\\u0430\",\"many\":\"(placeholder_line_break_plural_key) %d<br>\\u043a\\u043e\\u0442\\u043e\\u0432\",\"other\":\"(placeholder_line_break_plural_key) %d<br>\\u043a\\u043e\\u0442\\u043e\\u0432\"}"}, {"type": 2, "key": "universal_placeholder_plural_key", "value": "{\"one\":\"(universal_placeholder_plural_key) %d \\u043a\\u043e\\u0442\",\"few\":\"(universal_placeholder_plural_key) %d \\u043a\\u043e\\u0442\\u0430\",\"many\":\"(universal_placeholder_plural_key) %d \\u043a\\u043e\\u0442\\u043e\\u0432\",\"other\":\"(universal_placeholder_plural_key) %d \\u043a\\u043e\\u0442\\u043e\\u0432\"}"}, {"type": 0, "key": "tagged_key_with_tag1", "value": "(tagged_key_with_tag1) Привет, мир! tag1"}, {"type": 0, "key": "tagged_key_with_tag1_tag2", "value": "(tagged_key_with_tag1_tag2) Привет, мир! tag1 tag2"}, {"type": 0, "key": "tagged_key_with_tag2", "value": "(tagged_key_with_tag2) Привет, мир! tag2"}, {"type": 0, "key": "marked_status_key_with_status1", "value": "(marked_status_key_with_status1) Привет, мир! status1"}, {"type": 0, "key": "marked_status_key_with_status1_status2", "value": "(marked_status_key_with_status1_status2) Привет, мир! status1 status2"}, {"type": 0, "key": "marked_status_key_with_status2", "value": "(marked_status_key_with_status2) Привет, мир! status2"}], "is_default": 0}, {"iso": "lv", "items": [{"type": 1, "key": "array_key", "value": "[\"(array_key[0]) Sveika pasaule!\",\"(array_key[1]) Sveika pasaule!\",\"(array_key[2]) Sveika pasaule!\"]"}, {"type": 1, "key": "cross_project_array_key", "value": "[\"(cross_project_array_key[0]) Sveika pasaule! (Project 1)\",\"(cross_project_array_key[1]) Sveika pasaule! (Project 1)\",\"(cross_project_array_key[2]) Sveika pasaule! (Project 1)\"]"}, {"type": 0, "key": "simple_key", "value": "(simple_key) <PERSON><PERSON><PERSON> pasaule!"}, {"type": 0, "key": "simple_key_with_0_as_translation", "value": "0"}, {"type": 0, "key": "simple_key_with_description", "value": "(simple_key_with_description) <PERSON><PERSON>ika pasaule!"}, {"type": 0, "key": "simple_key_with_comments", "value": "(simple_key_with_comments) <PERSON><PERSON><PERSON> pasaule!"}, {"type": 0, "key": "simple_key_with_description_and_comments", "value": "(simple_key_with_description_and_comments) <PERSON><PERSON>ika pasaule!"}, {"type": 0, "key": "simple_android_only_key", "value": "(simple_android_only_key) <PERSON><PERSON><PERSON> pasaule!"}, {"type": 0, "key": "simple_ios_android_only_key", "value": "(simple_ios_android_only_key) <PERSON><PERSON>ika pasaule!"}, {"type": 0, "key": "simple_android_web_only_key", "value": "(simple_android_web_only_key) <PERSON><PERSON>ika pasaule!"}, {"type": 0, "key": "same_name_across_projects_key", "value": "(same_name_across_projects_key) Sveika pasaule! (Project 1)"}, {"type": 0, "key": "referenced_key", "value": "(referenced_key) <PERSON><PERSON><PERSON> pasaule!"}, {"type": 0, "key": "with_reference_key", "value": "(with_reference_key) <PERSON><PERSON>ika pasaule! (referenced_key) <PERSON><PERSON>ika pasaule!"}, {"type": 0, "key": "with_reference_different_file_key", "value": "(with_reference_different_file_key) Sveika pasaule! (referenced_key) Sveika pasaule!"}, {"type": 0, "key": "partially_with_reference_key", "value": "(partially_with_reference_key) <PERSON><PERSON>ika pasaule!"}, {"type": 0, "key": "reference_only_key", "value": "(referenced_key) <PERSON><PERSON><PERSON> pasaule!"}, {"type": 0, "key": "partial_reference_only_key", "value": ""}, {"type": 0, "key": "simple_file_with_iso_key", "value": "(simple_file_with_iso_key) <PERSON>veika pasaule!"}, {"type": 0, "key": "empty_key", "value": ""}, {"type": 0, "key": "untranslated_key", "value": ""}, {"type": 0, "key": "untranslated_reference_only_key", "value": ""}, {"type": 0, "key": "partially_untranslated_key", "value": ""}, {"type": 0, "key": "partially_untranslated_reference_only_key", "value": ""}, {"type": 0, "key": "void_key", "value": ""}, {"type": 0, "key": "void_reference_only_key", "value": ""}, {"type": 0, "key": "true_key", "value": "1"}, {"type": 0, "key": "true_reference_only_key", "value": "1"}, {"type": 0, "key": "false_key", "value": "0"}, {"type": 0, "key": "false_reference_only_key", "value": "0"}, {"type": 0, "key": "space_key", "value": " "}, {"type": 0, "key": "space_reference_only_key", "value": " "}, {"type": 0, "key": "universal_percent_key", "value": "(universal_percent_key) <PERSON><PERSON>ika pasaule! 100%!"}, {"type": 0, "key": "untranslated_without_file_key", "value": ""}, {"type": 0, "key": "line_break_key", "value": "(line_break_key) Sveika<br>pasaule!"}, {"type": 0, "key": "hidden_key", "value": "(hidden_key) <PERSON><PERSON><PERSON> pasaule!"}, {"type": 0, "key": "fuzzy_key", "value": "(fuzzy_key) <PERSON><PERSON><PERSON> pasaule!"}, {"type": 0, "key": "fuzzy_base_key", "value": "(fuzzy_base_key) <PERSON><PERSON><PERSON> pasaule!"}, {"type": 0, "key": "fuzzy_non_base_key", "value": "(fuzzy_non_base_key) <PERSON><PERSON><PERSON> pasaule!"}, {"type": 0, "key": "hidden_fuzzy_key", "value": "(hidden_fuzzy_key) <PERSON><PERSON><PERSON> pasaule!"}, {"type": 0, "key": "hidden_fuzzy_base_key", "value": "(hidden_fuzzy_base_key) <PERSON><PERSON><PERSON> pasaule!"}, {"type": 0, "key": "hidden_fuzzy_non_base_key", "value": "(hidden_fuzzy_non_base_key) <PERSON><PERSON><PERSON> pasaule!"}, {"type": 0, "key": "reviewed_key", "value": "(reviewed_key) <PERSON><PERSON><PERSON> pasaule!"}, {"type": 0, "key": "reviewed_base_key", "value": "(reviewed_base_key) <PERSON><PERSON>ika pasaule!"}, {"type": 0, "key": "reviewed_non_base_key", "value": "(reviewed_non_base_key) <PERSON><PERSON>ika pasaule!"}, {"type": 0, "key": "hidden_reviewed_key", "value": "(hidden_reviewed_key) <PERSON><PERSON><PERSON> pasaule!"}, {"type": 0, "key": "hidden_reviewed_base_key", "value": "(hidden_reviewed_base_key) <PERSON><PERSON><PERSON> pasaule!"}, {"type": 0, "key": "hidden_reviewed_non_base_key", "value": "(hidden_reviewed_non_base_key) <PERSON><PERSON>ika pasaule!"}, {"type": 0, "key": "placeholder_key", "value": "(placeholder_key) Sveika %s!"}, {"type": 0, "key": "multi_non_positional_placeholder_key", "value": "(multi_non_positional_placeholder_key) Sveika %s! <PERSON><PERSON> sauc %s."}, {"type": 0, "key": "multi_positional_placeholder_key", "value": "(multi_positional_placeholder_key) Sveika %1$s! <PERSON><PERSON> sauc %2$s."}, {"type": 0, "key": "universal_placeholder_key", "value": "(universal_placeholder_key) Sveika %s!"}, {"type": 0, "key": "multi_universal_placeholder_non_positional_key", "value": "(multi_universal_placeholder_non_positional_key) Sveika %s! <PERSON><PERSON>uc %s."}, {"type": 0, "key": "multi_universal_positional_placeholder_key", "value": "(multi_universal_positional_placeholder_key) Sveika %1$s! <PERSON><PERSON> sauc %2$s."}, {"type": 0, "key": "multi_universal_named_positional_placeholder_key", "value": "(multi_universal_named_positional_placeholder_key) Sveika %1$s! <PERSON><PERSON> sauc %2$s."}, {"type": 0, "key": "key_name_ends_with_plural", "value": "(key_name_ends_with_plural) <PERSON><PERSON><PERSON> pasaule!"}, {"type": 2, "key": "plural_key_name_ends_with_plural", "value": "{\"zero\":\"(plural_key_name_ends_with_plural) ka\\u0137u\",\"one\":\"(plural_key_name_ends_with_plural) ka\\u0137is\",\"other\":\"(plural_key_name_ends_with_plural) ka\\u0137i\"}"}, {"type": 2, "key": "plural_key", "value": "{\"zero\":\"(plural_key) ka\\u0137u\",\"one\":\"(plural_key) ka\\u0137is\",\"other\":\"(plural_key) ka\\u0137i\"}"}, {"type": 2, "key": "empty_plural_key", "value": "{\"zero\":\"\",\"one\":\"\",\"other\":\"\"}"}, {"type": 2, "key": "untranslated_plural_key", "value": "{\"zero\":\"\",\"one\":\"\",\"other\":\"\"}"}, {"type": 2, "key": "partially_untranslated_plural_key", "value": "{\"zero\":\"\",\"one\":\"\",\"other\":\"\"}"}, {"type": 2, "key": "placeholder_plural_key", "value": "{\"zero\":\"(placeholder_plural_key) %d ka\\u0137u\",\"one\":\"(placeholder_plural_key) %d ka\\u0137is\",\"other\":\"(placeholder_plural_key) %d ka\\u0137i\"}"}, {"type": 2, "key": "placeholder_line_break_plural_key", "value": "{\"zero\":\"(placeholder_line_break_plural_key) %d<br>ka\\u0137u\",\"one\":\"(placeholder_line_break_plural_key) %d<br>ka\\u0137is\",\"other\":\"(placeholder_line_break_plural_key) %d<br>ka\\u0137i\"}"}, {"type": 2, "key": "universal_placeholder_plural_key", "value": "{\"zero\":\"(universal_placeholder_plural_key) %d ka\\u0137u\",\"one\":\"(universal_placeholder_plural_key) %d ka\\u0137is\",\"other\":\"(universal_placeholder_plural_key) %d ka\\u0137i\"}"}, {"type": 0, "key": "tagged_key_with_tag1", "value": "(tagged_key_with_tag1) Sveika pasaule! tag1"}, {"type": 0, "key": "tagged_key_with_tag1_tag2", "value": "(tagged_key_with_tag1_tag2) Sveika pasaule! tag1 tag2"}, {"type": 0, "key": "tagged_key_with_tag2", "value": "(tagged_key_with_tag2) Sveika pasaule! tag2"}, {"type": 0, "key": "marked_status_key_with_status1", "value": "(marked_status_key_with_status1) Sveika pasaule! status1"}, {"type": 0, "key": "marked_status_key_with_status1_status2", "value": "(marked_status_key_with_status1_status2) Sveika pasaule! status1 status2"}, {"type": 0, "key": "marked_status_key_with_status2", "value": "(marked_status_key_with_status2) Sveika pasaule! status2"}], "is_default": 0}]