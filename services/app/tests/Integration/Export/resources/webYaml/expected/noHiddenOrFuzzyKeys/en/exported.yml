en:
    simple_key: '(simple_key) Hello world!'
    simple_key_with_0_as_translation: '0'
    simple_key_with_description: '(simple_key_with_description) Hello world!'
    simple_key_with_comments: '(simple_key_with_comments) Hello world!'
    simple_key_with_description_and_comments: '(simple_key_with_description_and_comments) Hello world!'
    simple_web_only_key: '(simple_web_only_key) Hello world!'
    simple_android_web_only_key: '(simple_android_web_only_key) Hello world!'
    'array_key[0]': '(array_key[0]) Hello world!'
    'array_key[1]': '(array_key[1]) Hello world!'
    'array_key[2]': '(array_key[2]) Hello world!'
    'cross_project_array_key[0]': '(cross_project_array_key[0]) Hello world! (Project 1)'
    'cross_project_array_key[1]': '(cross_project_array_key[1]) Hello world! (Project 1)'
    'cross_project_array_key[2]': '(cross_project_array_key[2]) Hello world! (Project 1)'
    same_name_across_projects_key: '(same_name_across_projects_key) Hello world! (Project 1)'
    referenced_key: '(referenced_key) Hello world!'
    with_reference_key: '(with_reference_key) Hello world! (referenced_key) Hello world!'
    with_reference_different_file_key: '(with_reference_different_file_key) Hello world! (referenced_key) Hello world!'
    partially_with_reference_key: '(partially_with_reference_key) Hello world! (referenced_key) Hello world!'
    reference_only_key: '(referenced_key) Hello world!'
    partial_reference_only_key: '(referenced_key) Hello world!'
    simple_file_with_iso_key: '(simple_file_with_iso_key) Hello world!'
    empty_key: ''
    untranslated_key: '(untranslated_key) Hello world!'
    untranslated_reference_only_key: '(untranslated_key) Hello world!'
    partially_untranslated_key: '(partially_untranslated_key) Hello world!'
    partially_untranslated_reference_only_key: '(untranslated_key) Hello world!'
    void_key: ''
    void_reference_only_key: ''
    true_key: '1'
    true_reference_only_key: '1'
    false_key: '0'
    false_reference_only_key: '0'
    space_key: ' '
    space_reference_only_key: ' '
    universal_percent_key: '(universal_percent_key) Hello world! 100%!'
    untranslated_without_file_key: '(untranslated_without_file_key) Hello world!'
    line_break_key: "(line_break_key) Hello\nworld!"
    fuzzy_key: '(fuzzy_key) Hello world!'
    fuzzy_base_key: '(fuzzy_base_key) Hello world!'
    fuzzy_non_base_key: '(fuzzy_non_base_key) Hello world!'
    reviewed_key: '(reviewed_key) Hello world!'
    reviewed_base_key: '(reviewed_base_key) Hello world!'
    reviewed_non_base_key: '(reviewed_non_base_key) Hello world!'
    placeholder_key: '(placeholder_key) Hello %s!'
    multi_non_positional_placeholder_key: '(multi_non_positional_placeholder_key) Hello %s! My name is %s.'
    multi_positional_placeholder_key: '(multi_positional_placeholder_key) Hello %1$s! My name is %2$s.'
    universal_placeholder_key: '(universal_placeholder_key) Hello %s!'
    multi_universal_placeholder_non_positional_key: '(multi_universal_placeholder_non_positional_key) Hello %s! My name is %s.'
    multi_universal_positional_placeholder_key: '(multi_universal_positional_placeholder_key) Hello %1$s! My name is %2$s.'
    multi_universal_named_positional_placeholder_key: '(multi_universal_named_positional_placeholder_key) Hello %1$s! My name is %2$s.'
    key_name_ends_with_plural: '(key_name_ends_with_plural) Hello world!'
    plural_key_name_ends_with_plural:
        one: '(plural_key_name_ends_with_plural) cat'
        other: '(plural_key_name_ends_with_plural) cats'
    plural_key:
        one: '(plural_key) cat'
        other: '(plural_key) cats'
    empty_plural_key:
        one: ''
        other: ''
    untranslated_plural_key:
        one: '(untranslated_plural_key) cat'
        other: '(untranslated_plural_key) cats'
    partially_untranslated_plural_key:
        one: '(partially_untranslated_plural_key) cat'
        other: '(partially_untranslated_plural_key) cats'
    placeholder_plural_key:
        one: '(placeholder_plural_key) %d cat'
        other: '(placeholder_plural_key) %d cats'
    placeholder_line_break_plural_key:
        one: "(placeholder_line_break_plural_key) %d\ncat"
        other: "(placeholder_line_break_plural_key) %d\ncats"
    universal_placeholder_plural_key:
        one: '(universal_placeholder_plural_key) %d cat'
        other: '(universal_placeholder_plural_key) %d cats'
    tagged_key_with_tag1: '(tagged_key_with_tag1) Hello world! tag1'
    tagged_key_with_tag1_tag2: '(tagged_key_with_tag1_tag2) Hello world! tag1 tag2'
    tagged_key_with_tag2: '(tagged_key_with_tag2) Hello world! tag2'
    marked_status_key_with_status1: '(marked_status_key_with_status1) Hello world! status1'
    marked_status_key_with_status1_status2: '(marked_status_key_with_status1_status2) Hello world! status1 status2'
    marked_status_key_with_status2: '(marked_status_key_with_status2) Hello world! status2'
