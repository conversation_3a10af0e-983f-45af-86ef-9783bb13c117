"simple_key" = "(simple_key) Привет, мир!";
"simple_key_with_0_as_translation" = "0";
"simple_key_with_description" = "(simple_key_with_description) Привет, мир!";
"simple_key_with_comments" = "(simple_key_with_comments) Привет, мир!";
"simple_key_with_description_and_comments" = "(simple_key_with_description_and_comments) Привет, мир!";
"simple_ios_only_key" = "(simple_ios_only_key) Привет, мир!";
"simple_ios_android_only_key" = "(simple_ios_android_only_key) Привет, мир!";
"simple_ios_other_only_key" = "(simple_ios_other_only_key) Привет, мир!";
"array_key[0]" = "(array_key[0]) Привет, мир!";
"array_key[1]" = "(array_key[1]) Привет, мир!";
"array_key[2]" = "(array_key[2]) Привет, мир!";
"cross_project_array_key[0]" = "(cross_project_array_key[0]) Привет, мир! (Project 1)";
"cross_project_array_key[1]" = "(cross_project_array_key[1]) Привет, мир! (Project 1)";
"cross_project_array_key[2]" = "(cross_project_array_key[2]) Привет, мир! (Project 1)";
"same_name_across_projects_key" = "(same_name_across_projects_key) Привет, мир! (Project 1)";
"referenced_key" = "(referenced_key) Привет, мир!";
"with_reference_key" = "(with_reference_key) Привет, мир! (referenced_key) Привет, мир!";
"partially_with_reference_key" = "(partially_with_reference_key) Привет, мир! (referenced_key) Привет, мир!";
"reference_only_key" = "(referenced_key) Привет, мир!";
"partial_reference_only_key" = "(referenced_key) Привет, мир!";
"untranslated_reference_only_key" = "";
"partially_untranslated_key" = "(partially_untranslated_key) Привет, мир!";
"partially_untranslated_reference_only_key" = "";
"void_key" = "";
"void_reference_only_key" = "";
"true_key" = "1";
"true_reference_only_key" = "1";
"false_key" = "0";
"false_reference_only_key" = "0";
"space_key" = " ";
"space_reference_only_key" = " ";
"universal_percent_key" = "(universal_percent_key) Привет, мир! 100%!";
"untranslated_without_file_key" = "";
"line_break_key" = "(line_break_key) Привет,\
мир!";
"hidden_key" = "(hidden_key) Привет, мир!";
"fuzzy_key" = "(fuzzy_key) Привет, мир!";
"fuzzy_base_key" = "(fuzzy_base_key) Привет, мир!";
"fuzzy_non_base_key" = "(fuzzy_non_base_key) Привет, мир!";
"hidden_fuzzy_key" = "(hidden_fuzzy_key) Привет, мир!";
"hidden_fuzzy_base_key" = "(hidden_fuzzy_base_key) Привет, мир!";
"hidden_fuzzy_non_base_key" = "(hidden_fuzzy_non_base_key) Привет, мир!";
"reviewed_key" = "(reviewed_key) Привет, мир!";
"reviewed_base_key" = "(reviewed_base_key) Привет, мир!";
"reviewed_non_base_key" = "(reviewed_non_base_key) Привет, мир!";
"hidden_reviewed_key" = "(hidden_reviewed_key) Привет, мир!";
"hidden_reviewed_base_key" = "(hidden_reviewed_base_key) Привет, мир!";
"hidden_reviewed_non_base_key" = "(hidden_reviewed_non_base_key) Привет, мир!";
"key_name_ends_with_plural" = "(key_name_ends_with_plural) Привет, мир!";
"tagged_key_with_tag1" = "(tagged_key_with_tag1) Привет, мир! tag1";
"tagged_key_with_tag1_tag2" = "(tagged_key_with_tag1_tag2) Привет, мир! tag1 tag2";
"tagged_key_with_tag2" = "(tagged_key_with_tag2) Привет, мир! tag2";
"marked_status_key_with_status1" = "(marked_status_key_with_status1) Привет, мир! status1";
"marked_status_key_with_status1_status2" = "(marked_status_key_with_status1_status2) Привет, мир! status1 status2";
"marked_status_key_with_status2" = "(marked_status_key_with_status2) Привет, мир! status2";