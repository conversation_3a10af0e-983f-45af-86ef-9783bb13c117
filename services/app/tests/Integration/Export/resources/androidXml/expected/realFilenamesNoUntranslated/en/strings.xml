<?xml version="1.0" encoding="UTF-8"?>
<resources>
  <string name="simple_key">(simple_key) Hello world!</string>
  <string name="simple_key_with_0_as_translation">0</string>
  <string name="simple_key_with_description">(simple_key_with_description) Hello world!</string>
  <string name="simple_key_with_comments">(simple_key_with_comments) Hello world!</string>
  <string name="simple_key_with_description_and_comments">(simple_key_with_description_and_comments) Hello world!</string>
  <string name="simple_android_only_key">(simple_android_only_key) Hello world!</string>
  <string name="simple_ios_android_only_key">(simple_ios_android_only_key) Hello world!</string>
  <string name="simple_android_web_only_key">(simple_android_web_only_key) Hello world!</string>
  <string-array name="array_key">
    <item>(array_key[0]) Hello world!</item>
    <item>(array_key[1]) Hello world!</item>
    <item>(array_key[2]) Hello world!</item>
  </string-array>
  <string-array name="cross_project_array_key">
    <item>(cross_project_array_key[0]) Hello world! (Project 1)</item>
    <item>(cross_project_array_key[1]) Hello world! (Project 1)</item>
    <item>(cross_project_array_key[2]) Hello world! (Project 1)</item>
  </string-array>
  <string name="same_name_across_projects_key">(same_name_across_projects_key) Hello world! (Project 1)</string>
  <string name="referenced_key">(referenced_key) Hello world!</string>
  <string name="with_reference_key">(with_reference_key) Hello world! (referenced_key) Hello world!</string>
  <string name="partially_with_reference_key">(partially_with_reference_key) Hello world! (referenced_key) Hello world!</string>
  <string name="reference_only_key">(referenced_key) Hello world!</string>
  <string name="partial_reference_only_key">(referenced_key) Hello world!</string>
  <string name="untranslated_reference_only_key">(untranslated_key) Hello world!</string>
  <string name="partially_untranslated_key">(partially_untranslated_key) Hello world!</string>
  <string name="partially_untranslated_reference_only_key">(untranslated_key) Hello world!</string>
  <string name="void_key"></string>
  <string name="void_reference_only_key"></string>
  <string name="true_key">1</string>
  <string name="true_reference_only_key">1</string>
  <string name="false_key">0</string>
  <string name="false_reference_only_key">0</string>
  <string name="space_key"> </string>
  <string name="space_reference_only_key"> </string>
  <string name="universal_percent_key">(universal_percent_key) Hello world! 100%!</string>
  <string name="untranslated_without_file_key">(untranslated_without_file_key) Hello world!</string>
  <string name="line_break_key">(line_break_key) Hello
world!</string>
  <string name="hidden_key">(hidden_key) Hello world!</string>
  <string name="fuzzy_key">(fuzzy_key) Hello world!</string>
  <string name="fuzzy_base_key">(fuzzy_base_key) Hello world!</string>
  <string name="fuzzy_non_base_key">(fuzzy_non_base_key) Hello world!</string>
  <string name="hidden_fuzzy_key">(hidden_fuzzy_key) Hello world!</string>
  <string name="hidden_fuzzy_base_key">(hidden_fuzzy_base_key) Hello world!</string>
  <string name="hidden_fuzzy_non_base_key">(hidden_fuzzy_non_base_key) Hello world!</string>
  <string name="reviewed_key">(reviewed_key) Hello world!</string>
  <string name="reviewed_base_key">(reviewed_base_key) Hello world!</string>
  <string name="reviewed_non_base_key">(reviewed_non_base_key) Hello world!</string>
  <string name="hidden_reviewed_key">(hidden_reviewed_key) Hello world!</string>
  <string name="hidden_reviewed_base_key">(hidden_reviewed_base_key) Hello world!</string>
  <string name="hidden_reviewed_non_base_key">(hidden_reviewed_non_base_key) Hello world!</string>
  <string name="key_name_ends_with_plural">(key_name_ends_with_plural) Hello world!</string>
  <string name="tagged_key_with_tag1">(tagged_key_with_tag1) Hello world! tag1</string>
  <string name="tagged_key_with_tag1_tag2">(tagged_key_with_tag1_tag2) Hello world! tag1 tag2</string>
  <string name="tagged_key_with_tag2">(tagged_key_with_tag2) Hello world! tag2</string>
  <string name="marked_status_key_with_status1">(marked_status_key_with_status1) Hello world! status1</string>
  <string name="marked_status_key_with_status1_status2">(marked_status_key_with_status1_status2) Hello world! status1 status2</string>
  <string name="marked_status_key_with_status2">(marked_status_key_with_status2) Hello world! status2</string>
</resources>
