<?php

declare(strict_types=1);

namespace Lokalise\Tests\Integration\Client\TeamPlanEntitlements;

use Lokalise\Client\TeamPlanEntitlements\TeamPlanLimitClient;
use Lokalise\Common\Team\ValueObject\TeamId;
use Lokalise\Common\TeamPlanEntitlements\Dto\Request\TeamPlanLimitRequest;
use Lokalise\Common\TeamPlanEntitlements\Dto\Request\TeamPlanLimitsRequest;
use Lokalise\Common\TeamPlanEntitlements\Enum\TeamPlanEntitlementsEnum;
use Lokalise\Constants\Plan;
use Lokalise\Plan\Model\PlanId;
use Lokalise\Services\PlanService;
use Lokalise\Services\TeamService;
use Lokalise\TeamPlanEntitlements\Services\TeamPlanEntitlementsService;
use Lokalise\Tests\AbstractKernelTestCase;
use Lokalise\Tests\FixtureManager\FixtureManager;

class TeamPlanLimitClientTest extends AbstractKernelTestCase
{
    private FixtureManager $fixtureManager;
    private TeamPlanLimitClient $teamPlanLimitClient;
    private TeamService $teamService;
    private PlanService $planService;
    private int $teamId;
    private int $teamIdTwo;
    private int $teamIdThree;

    public function setUp(): void
    {
        parent::setUp();

        $this->fixtureManager = $this->getServiceById(FixtureManager::class);
        $this->fixtureManager->setUp();

        $this->teamPlanLimitClient = $this->getServiceById(TeamPlanLimitClient::class);
        $this->teamService = $this->getServiceById(TeamService::class);
        $this->planService = $this->getServiceById(PlanService::class);

        [
            'teamId' => $this->teamId,
        ] = $this->fixtureManager->createUserAndTeam(isOwner: true, plan: Plan::V2_SEAT_BASED_ESSENTIAL_ANNUAL, teamData: ['plan_seat_limit' => 12]);

        [
            'teamId' => $this->teamIdTwo,
        ] = $this->fixtureManager->createUserAndTeam(isOwner: true, plan: Plan::V3_SEAT_BASED_PRO_ANNUAL, teamData: ['plan_seat_limit' => 20]);

        [
            'teamId' => $this->teamIdThree,
        ] = $this->fixtureManager->createUserAndTeam(isOwner: true, plan: Plan::GEN2_ADVANCED_YEARLY);

        $this->projectId = $this->fixtureManager->generateProjectId();
    }

    public function testCheckPlanLimits(): void
    {
        $currentLimits = $this->teamPlanLimitClient->getTeamPlanLimits(
            new TeamPlanLimitsRequest(new TeamId($this->teamId))
        );

        static::assertEquals(10000, $currentLimits->getKeyLimit());
        static::assertEquals(999999999, $currentLimits->getProjectLimit());
        static::assertEquals(12, $currentLimits->getContributorLimit());
        static::assertEquals(999999999, $currentLimits->getGlossaryLimit());
        static::assertEquals(200000, $currentLimits->getMauLimit());
        static::assertEquals(50, $currentLimits->getAutomationLimit());
        static::assertEquals(500000, $currentLimits->getAutomationLimitMtChars());
        static::assertEquals(20, $currentLimits->getMaxNumberOfProjectLevelWebhookHandlers());
        static::assertEquals(500, $currentLimits->getMaxNumberOfTeamWebhookLevelHandlers());
        static::assertEquals(50, $currentLimits->getOtaTrafficLimit());
        static::assertEquals(10, $currentLimits->getWorkflowsNumberLimit(), 'Essential should have access to 10 workflows');
        static::assertEquals(2, $currentLimits->getWorkflowsTemplateGateLevel(), 'Essential should have a level of Templated gate as 2');
        static::assertEquals(0, $currentLimits->getProcessedWordsLimit());
        static::assertEquals(0, $currentLimits->getHostedWordsLimit());
        static::assertEquals(0, $currentLimits->getAdvancedSeatsLimit());
        static::assertEquals(0, $currentLimits->getProAiTranslationsLimit());
        static::assertEquals(0, $currentLimits->getLanguagesLimit());
        static::assertEquals(0, $currentLimits->getApiCallsRateLimit());
        static::assertEquals(0, $currentLimits->getIntegrationsLimit());
        static::assertEquals(0, $currentLimits->getAutomationV2Limit());

        $keyLimit = $this->teamPlanLimitClient->getTeamPlanLimit(
            new TeamPlanLimitRequest(new TeamId($this->teamId), TeamPlanEntitlementsEnum::KEY_LIMIT)
        );

        static::assertEquals(10000, $keyLimit);

        $otaTrafficLimit = $this->teamPlanLimitClient->getTeamPlanLimit(
            new TeamPlanLimitRequest(new TeamId($this->teamId), TeamPlanEntitlementsEnum::OTA_TRAFFIC_LIMIT)
        );

        static::assertEquals(50, $otaTrafficLimit);

        $workflowsNumberLimit = $this->teamPlanLimitClient->getTeamPlanLimit(
            new TeamPlanLimitRequest(new TeamId($this->teamId), TeamPlanEntitlementsEnum::WORKFLOWS_NUMBER_LIMIT)
        );

        static::assertEquals(10, $workflowsNumberLimit);

        $workflowsTemplateGateLevel = $this->teamPlanLimitClient->getTeamPlanLimit(
            new TeamPlanLimitRequest(new TeamId($this->teamId), TeamPlanEntitlementsEnum::WORKFLOWS_TEMPLATE_GATE_LEVEL)
        );

        static::assertEquals(2, $workflowsTemplateGateLevel);
    }

    public function testCheckPlanLimitsOverride(): void
    {
        $overrides =  [
            'key_limit' => 20100,
            'automation_limit' => 100,
            'ota_traffic_limit' => 123,
            'automation_limit_mt_chars' => 200,
            'max_number_of_team_webhook_level_handlers' => 1000,
            'max_number_of_project_level_webhook_handlers' => 600,
            'features' => [1, 2, 3],
            'workflows_number_limit' => 12,
            'workflows_template_gate_level' => 8,
        ];

        $this->teamService->updateById($this->teamId, ['custom_plan' => json_encode($overrides, JSON_THROW_ON_ERROR)]);

        $currentLimits = $this->teamPlanLimitClient->getTeamPlanLimits(
            new TeamPlanLimitsRequest(new TeamId($this->teamId))
        );

        static::assertEquals(20100, $currentLimits->getKeyLimit());
        static::assertEquals(999999999, $currentLimits->getProjectLimit());
        static::assertEquals(12, $currentLimits->getContributorLimit());
        static::assertEquals(999999999, $currentLimits->getGlossaryLimit());
        static::assertEquals(200000, $currentLimits->getMauLimit());
        static::assertEquals(100, $currentLimits->getAutomationLimit());
        static::assertEquals(200, $currentLimits->getAutomationLimitMtChars());
        static::assertEquals(600, $currentLimits->getMaxNumberOfProjectLevelWebhookHandlers());
        static::assertEquals(1000, $currentLimits->getMaxNumberOfTeamWebhookLevelHandlers());
        static::assertEquals(123, $currentLimits->getOtaTrafficLimit());
        static::assertEquals(12, $currentLimits->getWorkflowsNumberLimit());
        static::assertEquals(8, $currentLimits->getWorkflowsTemplateGateLevel());
        static::assertEquals(0, $currentLimits->getProcessedWordsLimit());
        static::assertEquals(0, $currentLimits->getHostedWordsLimit());
        static::assertEquals(0, $currentLimits->getAdvancedSeatsLimit());
        static::assertEquals(0, $currentLimits->getProAiTranslationsLimit());
        static::assertEquals(0, $currentLimits->getLanguagesLimit());
        static::assertEquals(0, $currentLimits->getApiCallsRateLimit());
        static::assertEquals(0, $currentLimits->getIntegrationsLimit());
        static::assertEquals(0, $currentLimits->getAutomationV2Limit());

        $keyLimit = $this->teamPlanLimitClient->getTeamPlanLimit(
            new TeamPlanLimitRequest(new TeamId($this->teamId), TeamPlanEntitlementsEnum::KEY_LIMIT)
        );

        static::assertEquals(20100, $keyLimit);

        $otaTrafficLimit = $this->teamPlanLimitClient->getTeamPlanLimit(
            new TeamPlanLimitRequest(new TeamId($this->teamId), TeamPlanEntitlementsEnum::OTA_TRAFFIC_LIMIT)
        );

        static::assertEquals(123, $otaTrafficLimit);

        $workflowsNumberLimit = $this->teamPlanLimitClient->getTeamPlanLimit(
            new TeamPlanLimitRequest(new TeamId($this->teamId), TeamPlanEntitlementsEnum::WORKFLOWS_NUMBER_LIMIT)
        );

        static::assertEquals(12, $workflowsNumberLimit);

        $workflowsTemplateGateLevel = $this->teamPlanLimitClient->getTeamPlanLimit(
            new TeamPlanLimitRequest(new TeamId($this->teamId), TeamPlanEntitlementsEnum::WORKFLOWS_TEMPLATE_GATE_LEVEL)
        );

        static::assertEquals(8, $workflowsTemplateGateLevel);
    }

    public function testCheckPlanLimitsOverrideAsString(): void
    {
        $overrides =  [
            'key_limit' => '20100',
            'automation_limit' => '100',
            'ota_traffic_limit' => 123,
        ];

        $this->teamService->updateById($this->teamId, ['custom_plan' => json_encode($overrides, JSON_THROW_ON_ERROR)]);

        $currentLimits = $this->teamPlanLimitClient->getTeamPlanLimits(
            new TeamPlanLimitsRequest(new TeamId($this->teamId))
        );

        static::assertEquals(20100, $currentLimits->getKeyLimit());
        static::assertEquals(100, $currentLimits->getAutomationLimit());
        static::assertEquals(123, $currentLimits->getOtaTrafficLimit());
    }

    public function testCheckPlanLimitsProPlan(): void
    {
        $currentLimits = $this->teamPlanLimitClient->getTeamPlanLimits(
            new TeamPlanLimitsRequest(new TeamId($this->teamIdTwo))
        );

        static::assertEquals(30000, $currentLimits->getKeyLimit());
        static::assertEquals(999999999, $currentLimits->getProjectLimit());
        static::assertEquals(20, $currentLimits->getContributorLimit());
        static::assertEquals(999999999, $currentLimits->getGlossaryLimit());
        static::assertEquals(1000000, $currentLimits->getMauLimit());
        static::assertEquals(100, $currentLimits->getAutomationLimit());
        static::assertEquals(1000000, $currentLimits->getAutomationLimitMtChars());
        static::assertEquals(20, $currentLimits->getMaxNumberOfProjectLevelWebhookHandlers());
        static::assertEquals(500, $currentLimits->getMaxNumberOfTeamWebhookLevelHandlers());
        static::assertEquals(20, $currentLimits->getWorkflowsNumberLimit());
        static::assertEquals(3, $currentLimits->getWorkflowsTemplateGateLevel());
        static::assertEquals(200, $currentLimits->getOtaTrafficLimit());
        static::assertEquals(0, $currentLimits->getProcessedWordsLimit());
        static::assertEquals(0, $currentLimits->getHostedWordsLimit());
        static::assertEquals(0, $currentLimits->getAdvancedSeatsLimit());
        static::assertEquals(0, $currentLimits->getProAiTranslationsLimit());
        static::assertEquals(0, $currentLimits->getLanguagesLimit());
        static::assertEquals(0, $currentLimits->getApiCallsRateLimit());
        static::assertEquals(0, $currentLimits->getIntegrationsLimit());
        static::assertEquals(0, $currentLimits->getAutomationV2Limit());
    }

    public function testCheckPlanLimitsGenerationV2Pro(): void
    {
        $currentLimits = $this->teamPlanLimitClient->getTeamPlanLimits(
            new TeamPlanLimitsRequest(new TeamId($this->teamIdThree))
        );

        $team = $this->teamService->getTeamEntityById($this->teamIdThree);
        $plan = $this->planService->findByPlanId(new PlanId($team->getPlan()));

        static::assertEquals(TeamPlanEntitlementsService::INFINITY_LIMIT_VALUE, $currentLimits->getKeyLimit());
        static::assertEquals(TeamPlanEntitlementsService::INFINITY_LIMIT_VALUE, $currentLimits->getProjectLimit());
        static::assertEquals(TeamPlanEntitlementsService::INFINITY_LIMIT_VALUE, $currentLimits->getContributorLimit());
        static::assertEquals(TeamPlanEntitlementsService::INFINITY_LIMIT_VALUE, $currentLimits->getGlossaryLimit());
        static::assertEquals(TeamPlanEntitlementsService::INFINITY_LIMIT_VALUE, $currentLimits->getMauLimit());
        static::assertEquals(TeamPlanEntitlementsService::INFINITY_LIMIT_VALUE, $currentLimits->getAutomationLimit());
        static::assertEquals(TeamPlanEntitlementsService::INFINITY_LIMIT_VALUE, $currentLimits->getAutomationLimitMtChars());
        static::assertEquals(TeamPlanEntitlementsService::INFINITY_LIMIT_VALUE, $currentLimits->getMaxNumberOfProjectLevelWebhookHandlers());
        static::assertEquals(TeamPlanEntitlementsService::INFINITY_LIMIT_VALUE, $currentLimits->getMaxNumberOfTeamWebhookLevelHandlers());
        static::assertEquals(TeamPlanEntitlementsService::INFINITY_LIMIT_VALUE, $currentLimits->getWorkflowsNumberLimit());
        static::assertEquals(TeamPlanEntitlementsService::INFINITY_LIMIT_VALUE, $currentLimits->getWorkflowsTemplateGateLevel());
        static::assertEquals(TeamPlanEntitlementsService::INFINITY_LIMIT_VALUE, $currentLimits->getOtaTrafficLimit());
        static::assertEquals($plan->getProcessedWordsLimit(), $currentLimits->getProcessedWordsLimit());
        static::assertEquals(TeamPlanEntitlementsService::INFINITY_LIMIT_VALUE, $currentLimits->getHostedWordsLimit());
        static::assertEquals(TeamPlanEntitlementsService::INFINITY_LIMIT_VALUE, $currentLimits->getAdvancedSeatsLimit());
        static::assertEquals($plan->getProAiTranslationsLimit(), $currentLimits->getProAiTranslationsLimit());
        static::assertEquals(TeamPlanEntitlementsService::INFINITY_LIMIT_VALUE, $currentLimits->getLanguagesLimit());
        static::assertEquals(TeamPlanEntitlementsService::INFINITY_LIMIT_VALUE, $currentLimits->getApiCallsRateLimit());
        static::assertEquals(TeamPlanEntitlementsService::INFINITY_LIMIT_VALUE, $currentLimits->getIntegrationsLimit());
        static::assertEquals(TeamPlanEntitlementsService::INFINITY_LIMIT_VALUE, $currentLimits->getAutomationV2Limit());
    }
}
