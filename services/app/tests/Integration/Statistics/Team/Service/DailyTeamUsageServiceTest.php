<?php

declare(strict_types=1);

namespace Integration\Statistics\Team\Service;

use DateTime;
use Lokalise\Constants\Plan;
use Lokalise\Statistics\Team\Repository\DailyTeamUsageRepository;
use Lokalise\Statistics\Team\Service\DailyTeamUsageService;
use Lokalise\TeamManagement\Team\Service\TeamService;
use Lokalise\Tests\FixtureManager\FixtureManager;
use Lokalise\Tests\FixtureManager\UserFixtureManager;
use Lokalise\Tests\RollbackDbKernelTestCase;

class DailyTeamUsageServiceTest extends RollbackDbKernelTestCase
{
    private DailyTeamUsageService $dailyTeamUsageService;
    private FixtureManager $fixtureManager;
    private UserFixtureManager $userFixtureManager;
    private TeamService $teamService;
    private DailyTeamUsageRepository $dailyTeamUsageRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->dailyTeamUsageRepository = $this->getServiceById(DailyTeamUsageRepository::class);
        $this->teamService = $this->getServiceById(TeamService::class);
        $this->fixtureManager = $this->getServiceById(FixtureManager::class);
        $this->dailyTeamUsageService = $this->getServiceById(DailyTeamUsageService::class);

        $this->userFixtureManager = $this->getServiceById(UserFixtureManager::class);
    }

    /** @test */
    public function shouldNotPutLimitsToV2MetricsIfTeamIsNotOnV2GenerationPlan(): void
    {
        $userAndTeam = $this->userFixtureManager->createFixtureTeamOwner();

        $teamEntity = $this->teamService->findTeamEntityById($userAndTeam->getTeamId());

        $date = new DateTime();
        $this->dailyTeamUsageService->cacheDailyUsageForTeam(
            $date,
            $teamEntity,
            false
        );

        $usage = $this->dailyTeamUsageRepository->findOneByUniqueKey($date, $teamEntity->getId());

        static::assertThat($usage->processedWordsLimit, static::isNull());
        static::assertThat($usage->integrationsLimit, static::isNull());
        static::assertThat($usage->advancedSeatsLimit, static::isNull());
    }

    /** @test */
    public function shouldPutLimitsIfTeamIsOnV2GenerationPlan(): void
    {
        $userAndTeam = $this->userFixtureManager->createFixtureTeamOwner(plan: Plan::GEN2_ADVANCED_YEARLY);

        $teamEntity = $this->teamService->findTeamEntityById($userAndTeam->getTeamId());

        $date = new DateTime();
        $this->dailyTeamUsageService->cacheDailyUsageForTeam(
            $date,
            $teamEntity,
            false
        );

        $usage = $this->dailyTeamUsageRepository->findOneByUniqueKey($date, $teamEntity->getId());

        static::assertThat($usage->processedWordsLimit, static::equalTo(1000000));
        static::assertThat($usage->integrationsLimit, static::equalTo(999999999));
        static::assertThat($usage->advancedSeatsLimit, static::equalTo(999999999));
    }
}
