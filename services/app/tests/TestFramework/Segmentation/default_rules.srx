<?xml version="1.0" encoding="UTF-8"?>
<srx xmlns="http://www.lisa.org/srx20" xmlns:okpsrx="http://okapi.sf.net/srx-extensions" version="2.0">
    <header cascade="yes" segmentsubflows="yes">
        <formathandle include="no" type="start" />
        <formathandle include="yes" type="end" />
        <formathandle include="no" type="isolated" />
        <okpsrx:options oneSegmentIncludesAll="no" trimLeadingWhitespaces="no" trimTrailingWhitespaces="no" />
        <okpsrx:rangeRule />
    </header>
    <body>
        <languagerules/>
        <maprules>
            <languagemap languagerulename="supplement" languagepattern=".*" />
            <languagemap languagerulename="common" languagepattern=".*" />
        </maprules>
    </body>
</srx>
