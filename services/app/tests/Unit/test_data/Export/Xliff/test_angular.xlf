<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="1.2" xsi:schemaLocation="urn:oasis:names:tc:xliff:document:1.2 http://docs.oasis-open.org/xliff/v1.2/os/xliff-core-1.2-strict.xsd">
  <file original="" datatype="plaintext" xml:space="preserve" source-language="en" target-language="de">
    <header>
      <tool tool-id="lokalise.com" tool-name="Lokalise"></tool>
    </header>
    <body>
      <trans-unit id="Login" maxwidth="50" size-unit="char">
        <source>Login</source>
        <target></target>
      </trans-unit>
      <trans-unit id="Logout">
        <source>Logout</source>
        <target>Logout</target>
        <context-group purpose="location">
          <context context-type="type">context-value</context>
        </context-group>
      </trans-unit>
      <trans-unit id="With x-tag">
        <source>Updated: <x id="ICU" equiv-text="{minutes, plural, =0 {...} =1 {...} other {...}}"/> x-tag</source>
        <target>Updated: <x id="ICU" equiv-text="{minutes, plural, =0 {...} =1 {...} other {...}}"/> x-tag</target>
      </trans-unit>
      <trans-unit id="With x-tag having tag">
        <source>We will email a link to <x id="START_BOLD_TEXT" ctype="x-b" equiv-text="&lt;b&gt;"/> <x id="INTERPOLATION" equiv-text="{{ username }}"/> <x id="CLOSE_BOLD_TEXT" ctype="x-b" equiv-text="&lt;/b&gt;"/> when your GCPs are ready to tag. This dataset will not start processing until you have completed the tagging process.</source>
        <target>We will email a link to <x id="START_BOLD_TEXT" ctype="x-b" equiv-text="&lt;b&gt;"/> <x id="INTERPOLATION" equiv-text="{{ username }}"/> <x id="CLOSE_BOLD_TEXT" ctype="x-b" equiv-text="&lt;/b&gt;"/> when your GCPs are ready to tag. This dataset will not start processing until you have completed the tagging process.</target>
      </trans-unit>
      <trans-unit id="html entities">
        <source>The 5 predefined xml entities: &quot;, &apos;, &lt;, &gt;, and &amp;. I don&apos;t speak French &amp; and don&apos;t speak Simplified&#160;Chinese or Traditional&#xa0;Chinese.</source>
        <target>The 5 predefined xml entities: &quot;, &apos;, &lt;, &gt;, and &amp;. I don&apos;t speak French &amp; and don&apos;t speak Simplified&#160;Chinese or Traditional&#xa0;Chinese.</target>
      </trans-unit>
    </body>
  </file>
  <file original="new-context-for-testing-sorting" datatype="plaintext" xml:space="preserve" source-language="en" target-language="de">
    <header>
      <tool tool-id="lokalise.com" tool-name="Lokalise"></tool>
    </header>
    <body>
      <trans-unit id="Test 2">
        <source>Test 2</source>
        <target>Test 2</target>
      </trans-unit>
      <trans-unit id="plural_key">
        <source>{"one":"One strawberry","other":"%n strawberries"}</source>
        <target>{"one":"One strawberry","other":"%n strawberries"}</target>
        <note priority="bar" from="john doe">foo</note>
      </trans-unit>
    </body>
  </file>
  <file original="test-context" datatype="plaintext" xml:space="preserve" source-language="en" target-language="de">
    <header>
      <tool tool-id="lokalise.com" tool-name="Lokalise"></tool>
    </header>
    <body>
      <trans-unit id="test1" maxwidth="500" size-unit="char">
        <source>Test</source>
        <target>Test</target>
        <context-group>
          <context context-type="">context-value</context>
        </context-group>
      </trans-unit>
      <group id="my second group">
        <group id="inner group 1">
          <group id="inner group 2">
            <trans-unit id="Welcome">
              <source>Welcome</source>
              <target>Welcome</target>
            </trans-unit>
          </group>
        </group>
      </group>
    </body>
  </file>
</xliff>
