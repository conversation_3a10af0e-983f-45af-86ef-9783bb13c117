<?php

namespace Lokalise\Tests\Unit\Integration\Wordpress;

use Generator;

class ContentProvider
{
    public static function normalizableContent(): Generator
    {
        yield [
            '<!-- wp:paragraph --><p>test</p><!-- /wp:paragraph -->',
            '<wp-block data-type="paragraph"><p>test</p></wp-block>',
        ];

        yield [
            '<!-- divi:paragraph --><p>test</p><!-- /divi:paragraph -->',
            '<wp-block-divi data-type="paragraph"><p>test</p></wp-block-divi>',
        ];

        yield [
            '<div>pre block content</div><!-- wp:paragraph --><p>test</p><!-- /wp:paragraph -->',
            '<div>pre block content</div><wp-block data-type="paragraph"><p>test</p></wp-block>',
        ];

        yield [
            '<div>pre block content</div><!-- divi:paragraph --><p>test</p><!-- /divi:paragraph -->',
            '<div>pre block content</div><wp-block-divi data-type="paragraph"><p>test</p></wp-block-divi>',
        ];

        yield [
            '<!-- wp:paragraph --><!-- wp:paragraph --><p>Nested</p><!-- /wp:paragraph --><!-- /wp:paragraph -->',
            '<wp-block data-type="paragraph"><wp-block data-type="paragraph"><p>Nested</p></wp-block></wp-block>',
        ];

        yield [
            '<!-- wp:paragraph --><div><!-- wp:paragraph --><p>Deep nested</p><!-- /wp:paragraph --></div><!-- /wp:paragraph -->',
            '<wp-block data-type="paragraph"><div><wp-block data-type="paragraph"><p>Deep nested</p></wp-block></div></wp-block>',
        ];

        yield [
            '<!-- wp:custom/header /-->',
            '<wp-block data-type="custom/header" data-shorthand="true"></wp-block>',
        ];
    }

    public static function emptyContent(): Generator
    {
        yield ['', null];
        yield ['   ', null];
        yield ["\n\n", null];
        yield ['<p></p>', null];
        yield ['<p>   </p>', null];
        yield ["<p>\n\n</p>", null];
        yield ["\n<p></p>\n", null];
        yield ['<div></div>', null];
        yield ['<div>   </div>', null];
        yield ["<div>\n\n</div>", null];
        yield ["\n<div></div>\n", null];
        yield [
            '<!-- wp:paragraph --><p></p><!-- /wp:paragraph -->',
            "<wp-block data-type='paragraph'><p></p></wp-block>",
        ];
        yield [
            '<!-- divi:paragraph --><p></p><!-- /divi:paragraph -->',
            "<wp-block-divi data-type='paragraph'><p></p></wp-block-divi>",
        ];
        yield [
            "\n<!-- wp:paragraph --><p></p><!-- /wp:paragraph -->\n",
            "\n<wp-block data-type='paragraph'><p></p></wp-block>\n",
        ];
        yield [
            "<!-- wp:paragraph --><p>\n</p><!-- /wp:paragraph -->",
            "<wp-block data-type='paragraph'><p>\n</p></wp-block>",
        ];
        yield [
            "<!-- wp:paragraph -->\n<p></p>\n<!-- /wp:paragraph -->",
            "<wp-block data-type='paragraph'>\n<p></p>\n</wp-block>",
        ];
        yield [
            '<!-- wp:paragraph --><div></div><!-- /wp:paragraph -->',
            "<wp-block data-type='paragraph'><div></div></wp-block>",
        ];
        yield [
            "\n<!-- wp:paragraph --><div></div><!-- /wp:paragraph -->\n",
            "\n<wp-block data-type='paragraph'><div></div></wp-block>\n",
        ];
        yield [
            "<!-- wp:paragraph --><div>\n</div><!-- /wp:paragraph -->",
            "<wp-block data-type='paragraph'><div>\n</div></wp-block>",
        ];
        yield [
            "<!-- wp:paragraph -->\n<div></div>\n<!-- /wp:paragraph -->",
            "<wp-block data-type='paragraph'>\n<div></div>\n</wp-block>",
        ];
    }

    public static function nonEmptyContent(): Generator
    {
        yield ['desa', null];
        yield ['  desas ', null];
        yield ["\ndesa\n", null];
        yield ['<p>des</p>', null];
        yield ['<p> desas  </p>', null];
        yield ["<p>\ndesa\n</p>", null];
        yield ["\n<p>desas</p>\n", null];
        yield ['<div>des</div>', null];
        yield ['<div> desas  </div>', null];
        yield ["<div>\ndesa\n</div>", null];
        yield ["\n<div>desas</div>\n", null];
        yield [
            '<!-- wp:paragraph --><p>desa</p><!-- /wp:paragraph -->',
            "<wp-block data-type='paragraph'><p>desa</p></wp-block>",
        ];
        yield [
            "\n<!-- wp:paragraph --><p>desas</p><!-- /wp:paragraph -->\n",
            "\n<wp-block data-type='paragraph'><p>desas</p></wp-block>\n",
        ];
        yield [
            "<!-- wp:paragraph --><p>\ndesa</p><!-- /wp:paragraph -->",
            "<wp-block data-type='paragraph'><p>\ndesa</p></wp-block>",
        ];
        yield [
            "<!-- wp:paragraph -->\n<p>desa</p>\n<!-- /wp:paragraph -->",
            "<wp-block data-type='paragraph'>\n<p>desa</p>\n</wp-block>",
        ];
        yield [
            "<!-- wp:quote -->\n<p>desa</p>\n<!-- /wp:quote -->",
            "<wp-block data-type='quote'>\n<p>desa</p>\n</wp-block>",
        ];
        yield [
            '<!-- wp:paragraph --><div>desa</div><!-- /wp:paragraph -->',
            "<wp-block data-type='paragraph'><div>desa</div></wp-block>",
        ];
        yield [
            "\n<!-- wp:paragraph --><div>desas</div><!-- /wp:paragraph -->\n",
            "\n<wp-block data-type='paragraph'><div>desas</div></wp-block>\n",
        ];
        yield [
            "<!-- wp:paragraph --><div>\ndesa</div><!-- /wp:paragraph -->",
            "<wp-block data-type='paragraph'><div>\ndesa</div></wp-block>",
        ];
        yield [
            "<!-- wp:paragraph -->\n<div>desa</div>\n<!-- /wp:paragraph -->",
            "<wp-block data-type='paragraph'>\n<div>desa</div>\n</wp-block>",
        ];
        yield [
            "<!-- wp:quote -->\n<div>desa</div>\n<!-- /wp:quote -->",
            "<wp-block data-type='quote'>\n<div>desa</div>\n</wp-block>",
        ];
        yield [
            "<!-- divi:quote -->\n<div>desa</div>\n<!-- /divi:quote -->",
            "<wp-block-divi data-type='quote'>\n<div>desa</div>\n</wp-block-divi>",
        ];
    }
}
