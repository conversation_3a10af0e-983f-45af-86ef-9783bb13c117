<?php

declare(strict_types=1);

namespace Lokalise\Tests\Unit\Snapshot\Metrics;

use Lokalise\Snapshot\Helper\SnapshotCreationProfiler;
use PHPUnit\Framework\TestCase;

class SnapshotCreationProfilerTest extends TestCase
{
    public function testGetMetrics(): void
    {
        $profiler = new SnapshotCreationProfiler();
        $metricsArray = $profiler->getMetrics()->toArray();

        static::assertSame(0, $metricsArray['time.diskOperation']);
        static::assertSame(0, $metricsArray['time.dataDump']);
        static::assertSame(0, $metricsArray['time.dataGather']);
        static::assertSame(0, $metricsArray['time.compression']);
        static::assertSame(0, $metricsArray['time.upload']);
        static::assertGreaterThan(0, $metricsArray['time.total']);

        $profiler = new SnapshotCreationProfiler();
        $metricsArray = $profiler->getMetrics()->toArray();

        $profiler->diskOperationStart();
        $profiler->diskOperationEnd();
        $profiler->dataDumpStart();
        $profiler->dataDumpEnd();
        $profiler->dataChunkGatherStart();
        $profiler->dataChunkGatherEnd();
        $profiler->dataCompressionStart();
        $profiler->dataCompressionEnd();
        $profiler->dataUploadStart();
        $profiler->dataUploadEnd();

        static::assertGreaterThanOrEqual(0, $metricsArray['time.diskOperation']);
        static::assertGreaterThanOrEqual(0, $metricsArray['time.dataDump']);
        static::assertGreaterThanOrEqual(0, $metricsArray['time.dataGather']);
        static::assertGreaterThanOrEqual(0, $metricsArray['time.compression']);
        static::assertGreaterThanOrEqual(0, $metricsArray['time.upload']);
        static::assertGreaterThanOrEqual(0, $metricsArray['time.total']);
        static::assertArrayHasKey('memory.initial', $metricsArray);
        static::assertArrayHasKey('memory.initialPeak', $metricsArray);
        static::assertArrayHasKey('memory.final', $metricsArray);
        static::assertArrayHasKey('memory.finalPeak', $metricsArray);
    }
}
