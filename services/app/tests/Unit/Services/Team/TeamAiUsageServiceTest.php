<?php

namespace Lokalise\Tests\Unit\Services\Team;

use Lokalise\AiBilling\Services\AiUsageRedisService;
use Lokalise\AiBilling\Services\ModuleProxyService;
use Lokalise\AiBilling\Services\TeamAiUsageService;
use Lokalise\Client\TeamPlanEntitlements\TeamPlanLimitClient;
use Lokalise\Repository\TranslationRepository;
use MongoDB\Collection;
use PHPUnit\Framework\TestCase;
use TranslationService;

class TeamAiUsageServiceTest extends TestCase
{
    private TeamAiUsageService $teamAiUsageService;
    private readonly ModuleProxyService $moduleProxyService;
    private readonly TranslationService $translationService;
    private readonly AiUsageRedisService $aiUsageRedisService;

    protected function setUp(): void
    {
        $this->moduleProxyService =
            $this->createMock(ModuleProxyService::class);
        $this->translationService =
            $this->createMock(TranslationService::class);
        $this->aiUsageRedisService =
            $this->createMock(AiUsageRedisService::class);

        $this->teamAiUsageService = new TeamAiUsageService(
            $this->moduleProxyService,
            $this->translationService,
            $this->aiUsageRedisService,
            $this->createMock(Collection::class),
            $this->createMock(TranslationRepository::class),
            $this->createMock(TeamPlanLimitClient::class),
        );
    }

    public function testConstruct(): void
    {
        static::assertInstanceOf(TeamAiUsageService::class, $this->teamAiUsageService);
    }

    public function testUsageShouldTriggerFirstThreshold(): void
    {
        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(80, 100);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[0], $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(81, 100);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[0], $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(82, 100);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[0], $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(86, 100);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[0], $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(89, 100);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[0], $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(160, 200);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[0], $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(171, 200);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[0], $warningThresholdStep);
    }

    public function testUsageShouldTriggerSecondThreshold(): void
    {
        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(90, 100);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[1], $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(91, 100);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[1], $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(92, 100);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[1], $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(93, 100);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[1], $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(94, 100);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[1], $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(180, 200);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[1], $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(189, 200);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[1], $warningThresholdStep);
    }

    public function testUsageShouldTriggerThirdThreshold(): void
    {
        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(95, 100);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[2], $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(96, 100);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[2], $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(97, 100);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[2], $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(98, 100);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[2], $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(99, 100);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[2], $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(190, 200);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[2], $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(199, 200);
        static::assertEquals(TeamAiUsageService::QUOTA_WARNING_THRESHOLDS[2], $warningThresholdStep);
    }

    public function testUsageShouldTriggerFinalThreshold(): void
    {
        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(100, 100);
        static::assertEquals(TeamAiUsageService::QUOTA_REACHED_THRESHOLD, $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(101, 100);
        static::assertEquals(TeamAiUsageService::QUOTA_REACHED_THRESHOLD, $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(200, 100);
        static::assertEquals(TeamAiUsageService::QUOTA_REACHED_THRESHOLD, $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(200, 200);
        static::assertEquals(TeamAiUsageService::QUOTA_REACHED_THRESHOLD, $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(241, 200);
        static::assertEquals(TeamAiUsageService::QUOTA_REACHED_THRESHOLD, $warningThresholdStep);
    }

    public function testUsageShouldNotTriggerAnyThreshold(): void
    {
        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(0, 100);
        static::assertEquals(null, $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(8, 100);
        static::assertEquals(null, $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(79, 100);
        static::assertEquals(null, $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(0, 200);
        static::assertEquals(null, $warningThresholdStep);

        $warningThresholdStep = $this->teamAiUsageService->getLimitThresholdStep(159, 200);
        static::assertEquals(null, $warningThresholdStep);
    }
}
